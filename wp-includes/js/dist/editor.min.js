/*! This file is auto-generated */
(()=>{var e={66:e=>{"use strict";var t=function(e){return function(e){return!!e&&"object"==typeof e}(e)&&!function(e){var t=Object.prototype.toString.call(e);return"[object RegExp]"===t||"[object Date]"===t||function(e){return e.$$typeof===s}(e)}(e)};var s="function"==typeof Symbol&&Symbol.for?Symbol.for("react.element"):60103;function o(e,t){return!1!==t.clone&&t.isMergeableObject(e)?l((s=e,Array.isArray(s)?[]:{}),e,t):e;var s}function n(e,t,s){return e.concat(t).map((function(e){return o(e,s)}))}function i(e){return Object.keys(e).concat(function(e){return Object.getOwnPropertySymbols?Object.getOwnPropertySymbols(e).filter((function(t){return Object.propertyIsEnumerable.call(e,t)})):[]}(e))}function r(e,t){try{return t in e}catch(e){return!1}}function a(e,t,s){var n={};return s.isMergeableObject(e)&&i(e).forEach((function(t){n[t]=o(e[t],s)})),i(t).forEach((function(i){(function(e,t){return r(e,t)&&!(Object.hasOwnProperty.call(e,t)&&Object.propertyIsEnumerable.call(e,t))})(e,i)||(r(e,i)&&s.isMergeableObject(t[i])?n[i]=function(e,t){if(!t.customMerge)return l;var s=t.customMerge(e);return"function"==typeof s?s:l}(i,s)(e[i],t[i],s):n[i]=o(t[i],s))})),n}function l(e,s,i){(i=i||{}).arrayMerge=i.arrayMerge||n,i.isMergeableObject=i.isMergeableObject||t,i.cloneUnlessOtherwiseSpecified=o;var r=Array.isArray(s);return r===Array.isArray(e)?r?i.arrayMerge(e,s,i):a(e,s,i):o(s,i)}l.all=function(e,t){if(!Array.isArray(e))throw new Error("first argument should be an array");return e.reduce((function(e,s){return l(e,s,t)}),{})};var c=l;e.exports=c},461:(e,t,s)=>{var o=s(6109);e.exports=function(e){var t=o(e,"line-height"),s=parseFloat(t,10);if(t===s+""){var n=e.style.lineHeight;e.style.lineHeight=t+"em",t=o(e,"line-height"),s=parseFloat(t,10),n?e.style.lineHeight=n:delete e.style.lineHeight}if(-1!==t.indexOf("pt")?(s*=4,s/=3):-1!==t.indexOf("mm")?(s*=96,s/=25.4):-1!==t.indexOf("cm")?(s*=96,s/=2.54):-1!==t.indexOf("in")?s*=96:-1!==t.indexOf("pc")&&(s*=16),s=Math.round(s),"normal"===t){var i=e.nodeName,r=document.createElement(i);r.innerHTML="&nbsp;","TEXTAREA"===i.toUpperCase()&&r.setAttribute("rows","1");var a=o(e,"font-size");r.style.fontSize=a,r.style.padding="0px",r.style.border="0px";var l=document.body;l.appendChild(r),s=r.offsetHeight,l.removeChild(r)}return s}},628:(e,t,s)=>{"use strict";var o=s(4067);function n(){}function i(){}i.resetWarningCache=n,e.exports=function(){function e(e,t,s,n,i,r){if(r!==o){var a=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw a.name="Invariant Violation",a}}function t(){return e}e.isRequired=e;var s={array:e,bigint:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,elementType:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:i,resetWarningCache:n};return s.PropTypes=s,s}},1609:e=>{"use strict";e.exports=window.React},4067:e=>{"use strict";e.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},4132:(e,t,s)=>{"use strict";var o=s(4462);t.A=o.TextareaAutosize},4306:function(e,t){var s,o,n;
/*!
	autosize 4.0.4
	license: MIT
	http://www.jacklmoore.com/autosize
*/o=[e,t],s=function(e,t){"use strict";var s,o,n="function"==typeof Map?new Map:(s=[],o=[],{has:function(e){return s.indexOf(e)>-1},get:function(e){return o[s.indexOf(e)]},set:function(e,t){-1===s.indexOf(e)&&(s.push(e),o.push(t))},delete:function(e){var t=s.indexOf(e);t>-1&&(s.splice(t,1),o.splice(t,1))}}),i=function(e){return new Event(e,{bubbles:!0})};try{new Event("test")}catch(e){i=function(e){var t=document.createEvent("Event");return t.initEvent(e,!0,!1),t}}function r(e){if(e&&e.nodeName&&"TEXTAREA"===e.nodeName&&!n.has(e)){var t=null,s=null,o=null,r=function(){e.clientWidth!==s&&p()},a=function(t){window.removeEventListener("resize",r,!1),e.removeEventListener("input",p,!1),e.removeEventListener("keyup",p,!1),e.removeEventListener("autosize:destroy",a,!1),e.removeEventListener("autosize:update",p,!1),Object.keys(t).forEach((function(s){e.style[s]=t[s]})),n.delete(e)}.bind(e,{height:e.style.height,resize:e.style.resize,overflowY:e.style.overflowY,overflowX:e.style.overflowX,wordWrap:e.style.wordWrap});e.addEventListener("autosize:destroy",a,!1),"onpropertychange"in e&&"oninput"in e&&e.addEventListener("keyup",p,!1),window.addEventListener("resize",r,!1),e.addEventListener("input",p,!1),e.addEventListener("autosize:update",p,!1),e.style.overflowX="hidden",e.style.wordWrap="break-word",n.set(e,{destroy:a,update:p}),l()}function l(){var s=window.getComputedStyle(e,null);"vertical"===s.resize?e.style.resize="none":"both"===s.resize&&(e.style.resize="horizontal"),t="content-box"===s.boxSizing?-(parseFloat(s.paddingTop)+parseFloat(s.paddingBottom)):parseFloat(s.borderTopWidth)+parseFloat(s.borderBottomWidth),isNaN(t)&&(t=0),p()}function c(t){var s=e.style.width;e.style.width="0px",e.offsetWidth,e.style.width=s,e.style.overflowY=t}function d(e){for(var t=[];e&&e.parentNode&&e.parentNode instanceof Element;)e.parentNode.scrollTop&&t.push({node:e.parentNode,scrollTop:e.parentNode.scrollTop}),e=e.parentNode;return t}function u(){if(0!==e.scrollHeight){var o=d(e),n=document.documentElement&&document.documentElement.scrollTop;e.style.height="",e.style.height=e.scrollHeight+t+"px",s=e.clientWidth,o.forEach((function(e){e.node.scrollTop=e.scrollTop})),n&&(document.documentElement.scrollTop=n)}}function p(){u();var t=Math.round(parseFloat(e.style.height)),s=window.getComputedStyle(e,null),n="content-box"===s.boxSizing?Math.round(parseFloat(s.height)):e.offsetHeight;if(n<t?"hidden"===s.overflowY&&(c("scroll"),u(),n="content-box"===s.boxSizing?Math.round(parseFloat(window.getComputedStyle(e,null).height)):e.offsetHeight):"hidden"!==s.overflowY&&(c("hidden"),u(),n="content-box"===s.boxSizing?Math.round(parseFloat(window.getComputedStyle(e,null).height)):e.offsetHeight),o!==n){o=n;var r=i("autosize:resized");try{e.dispatchEvent(r)}catch(e){}}}}function a(e){var t=n.get(e);t&&t.destroy()}function l(e){var t=n.get(e);t&&t.update()}var c=null;"undefined"==typeof window||"function"!=typeof window.getComputedStyle?((c=function(e){return e}).destroy=function(e){return e},c.update=function(e){return e}):((c=function(e,t){return e&&Array.prototype.forEach.call(e.length?e:[e],(function(e){return r(e,t)})),e}).destroy=function(e){return e&&Array.prototype.forEach.call(e.length?e:[e],a),e},c.update=function(e){return e&&Array.prototype.forEach.call(e.length?e:[e],l),e}),t.default=c,e.exports=t.default},void 0===(n="function"==typeof s?s.apply(t,o):s)||(e.exports=n)},4462:function(e,t,s){"use strict";var o,n=this&&this.__extends||(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var s in t)t.hasOwnProperty(s)&&(e[s]=t[s])},function(e,t){function s(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(s.prototype=t.prototype,new s)}),i=this&&this.__assign||Object.assign||function(e){for(var t,s=1,o=arguments.length;s<o;s++)for(var n in t=arguments[s])Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n]);return e},r=this&&this.__rest||function(e,t){var s={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(s[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var n=0;for(o=Object.getOwnPropertySymbols(e);n<o.length;n++)t.indexOf(o[n])<0&&(s[o[n]]=e[o[n]])}return s};t.__esModule=!0;var a=s(1609),l=s(5826),c=s(4306),d=s(461),u="autosize:resized",p=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.state={lineHeight:null},t.textarea=null,t.onResize=function(e){t.props.onResize&&t.props.onResize(e)},t.updateLineHeight=function(){t.textarea&&t.setState({lineHeight:d(t.textarea)})},t.onChange=function(e){var s=t.props.onChange;t.currentValue=e.currentTarget.value,s&&s(e)},t}return n(t,e),t.prototype.componentDidMount=function(){var e=this,t=this.props,s=t.maxRows,o=t.async;"number"==typeof s&&this.updateLineHeight(),"number"==typeof s||o?setTimeout((function(){return e.textarea&&c(e.textarea)})):this.textarea&&c(this.textarea),this.textarea&&this.textarea.addEventListener(u,this.onResize)},t.prototype.componentWillUnmount=function(){this.textarea&&(this.textarea.removeEventListener(u,this.onResize),c.destroy(this.textarea))},t.prototype.render=function(){var e=this,t=this.props,s=(t.onResize,t.maxRows),o=(t.onChange,t.style),n=(t.innerRef,t.children),l=r(t,["onResize","maxRows","onChange","style","innerRef","children"]),c=this.state.lineHeight,d=s&&c?c*s:null;return a.createElement("textarea",i({},l,{onChange:this.onChange,style:d?i({},o,{maxHeight:d}):o,ref:function(t){e.textarea=t,"function"==typeof e.props.innerRef?e.props.innerRef(t):e.props.innerRef&&(e.props.innerRef.current=t)}}),n)},t.prototype.componentDidUpdate=function(){this.textarea&&c.update(this.textarea)},t.defaultProps={rows:1,async:!1},t.propTypes={rows:l.number,maxRows:l.number,onResize:l.func,innerRef:l.any,async:l.bool},t}(a.Component);t.TextareaAutosize=a.forwardRef((function(e,t){return a.createElement(p,i({},e,{innerRef:t}))}))},5215:e=>{"use strict";e.exports=function e(t,s){if(t===s)return!0;if(t&&s&&"object"==typeof t&&"object"==typeof s){if(t.constructor!==s.constructor)return!1;var o,n,i;if(Array.isArray(t)){if((o=t.length)!=s.length)return!1;for(n=o;0!=n--;)if(!e(t[n],s[n]))return!1;return!0}if(t.constructor===RegExp)return t.source===s.source&&t.flags===s.flags;if(t.valueOf!==Object.prototype.valueOf)return t.valueOf()===s.valueOf();if(t.toString!==Object.prototype.toString)return t.toString()===s.toString();if((o=(i=Object.keys(t)).length)!==Object.keys(s).length)return!1;for(n=o;0!=n--;)if(!Object.prototype.hasOwnProperty.call(s,i[n]))return!1;for(n=o;0!=n--;){var r=i[n];if(!e(t[r],s[r]))return!1}return!0}return t!=t&&s!=s}},5826:(e,t,s)=>{e.exports=s(628)()},6109:e=>{e.exports=function(e,t,s){return((s=window.getComputedStyle)?s(e):e.currentStyle)[t.replace(/-(\w)/gi,(function(e,t){return t.toUpperCase()}))]}},9681:e=>{var t={À:"A",Á:"A",Â:"A",Ã:"A",Ä:"A",Å:"A",Ấ:"A",Ắ:"A",Ẳ:"A",Ẵ:"A",Ặ:"A",Æ:"AE",Ầ:"A",Ằ:"A",Ȃ:"A",Ả:"A",Ạ:"A",Ẩ:"A",Ẫ:"A",Ậ:"A",Ç:"C",Ḉ:"C",È:"E",É:"E",Ê:"E",Ë:"E",Ế:"E",Ḗ:"E",Ề:"E",Ḕ:"E",Ḝ:"E",Ȇ:"E",Ẻ:"E",Ẽ:"E",Ẹ:"E",Ể:"E",Ễ:"E",Ệ:"E",Ì:"I",Í:"I",Î:"I",Ï:"I",Ḯ:"I",Ȋ:"I",Ỉ:"I",Ị:"I",Ð:"D",Ñ:"N",Ò:"O",Ó:"O",Ô:"O",Õ:"O",Ö:"O",Ø:"O",Ố:"O",Ṍ:"O",Ṓ:"O",Ȏ:"O",Ỏ:"O",Ọ:"O",Ổ:"O",Ỗ:"O",Ộ:"O",Ờ:"O",Ở:"O",Ỡ:"O",Ớ:"O",Ợ:"O",Ù:"U",Ú:"U",Û:"U",Ü:"U",Ủ:"U",Ụ:"U",Ử:"U",Ữ:"U",Ự:"U",Ý:"Y",à:"a",á:"a",â:"a",ã:"a",ä:"a",å:"a",ấ:"a",ắ:"a",ẳ:"a",ẵ:"a",ặ:"a",æ:"ae",ầ:"a",ằ:"a",ȃ:"a",ả:"a",ạ:"a",ẩ:"a",ẫ:"a",ậ:"a",ç:"c",ḉ:"c",è:"e",é:"e",ê:"e",ë:"e",ế:"e",ḗ:"e",ề:"e",ḕ:"e",ḝ:"e",ȇ:"e",ẻ:"e",ẽ:"e",ẹ:"e",ể:"e",ễ:"e",ệ:"e",ì:"i",í:"i",î:"i",ï:"i",ḯ:"i",ȋ:"i",ỉ:"i",ị:"i",ð:"d",ñ:"n",ò:"o",ó:"o",ô:"o",õ:"o",ö:"o",ø:"o",ố:"o",ṍ:"o",ṓ:"o",ȏ:"o",ỏ:"o",ọ:"o",ổ:"o",ỗ:"o",ộ:"o",ờ:"o",ở:"o",ỡ:"o",ớ:"o",ợ:"o",ù:"u",ú:"u",û:"u",ü:"u",ủ:"u",ụ:"u",ử:"u",ữ:"u",ự:"u",ý:"y",ÿ:"y",Ā:"A",ā:"a",Ă:"A",ă:"a",Ą:"A",ą:"a",Ć:"C",ć:"c",Ĉ:"C",ĉ:"c",Ċ:"C",ċ:"c",Č:"C",č:"c",C̆:"C",c̆:"c",Ď:"D",ď:"d",Đ:"D",đ:"d",Ē:"E",ē:"e",Ĕ:"E",ĕ:"e",Ė:"E",ė:"e",Ę:"E",ę:"e",Ě:"E",ě:"e",Ĝ:"G",Ǵ:"G",ĝ:"g",ǵ:"g",Ğ:"G",ğ:"g",Ġ:"G",ġ:"g",Ģ:"G",ģ:"g",Ĥ:"H",ĥ:"h",Ħ:"H",ħ:"h",Ḫ:"H",ḫ:"h",Ĩ:"I",ĩ:"i",Ī:"I",ī:"i",Ĭ:"I",ĭ:"i",Į:"I",į:"i",İ:"I",ı:"i",Ĳ:"IJ",ĳ:"ij",Ĵ:"J",ĵ:"j",Ķ:"K",ķ:"k",Ḱ:"K",ḱ:"k",K̆:"K",k̆:"k",Ĺ:"L",ĺ:"l",Ļ:"L",ļ:"l",Ľ:"L",ľ:"l",Ŀ:"L",ŀ:"l",Ł:"l",ł:"l",Ḿ:"M",ḿ:"m",M̆:"M",m̆:"m",Ń:"N",ń:"n",Ņ:"N",ņ:"n",Ň:"N",ň:"n",ŉ:"n",N̆:"N",n̆:"n",Ō:"O",ō:"o",Ŏ:"O",ŏ:"o",Ő:"O",ő:"o",Œ:"OE",œ:"oe",P̆:"P",p̆:"p",Ŕ:"R",ŕ:"r",Ŗ:"R",ŗ:"r",Ř:"R",ř:"r",R̆:"R",r̆:"r",Ȓ:"R",ȓ:"r",Ś:"S",ś:"s",Ŝ:"S",ŝ:"s",Ş:"S",Ș:"S",ș:"s",ş:"s",Š:"S",š:"s",Ţ:"T",ţ:"t",ț:"t",Ț:"T",Ť:"T",ť:"t",Ŧ:"T",ŧ:"t",T̆:"T",t̆:"t",Ũ:"U",ũ:"u",Ū:"U",ū:"u",Ŭ:"U",ŭ:"u",Ů:"U",ů:"u",Ű:"U",ű:"u",Ų:"U",ų:"u",Ȗ:"U",ȗ:"u",V̆:"V",v̆:"v",Ŵ:"W",ŵ:"w",Ẃ:"W",ẃ:"w",X̆:"X",x̆:"x",Ŷ:"Y",ŷ:"y",Ÿ:"Y",Y̆:"Y",y̆:"y",Ź:"Z",ź:"z",Ż:"Z",ż:"z",Ž:"Z",ž:"z",ſ:"s",ƒ:"f",Ơ:"O",ơ:"o",Ư:"U",ư:"u",Ǎ:"A",ǎ:"a",Ǐ:"I",ǐ:"i",Ǒ:"O",ǒ:"o",Ǔ:"U",ǔ:"u",Ǖ:"U",ǖ:"u",Ǘ:"U",ǘ:"u",Ǚ:"U",ǚ:"u",Ǜ:"U",ǜ:"u",Ứ:"U",ứ:"u",Ṹ:"U",ṹ:"u",Ǻ:"A",ǻ:"a",Ǽ:"AE",ǽ:"ae",Ǿ:"O",ǿ:"o",Þ:"TH",þ:"th",Ṕ:"P",ṕ:"p",Ṥ:"S",ṥ:"s",X́:"X",x́:"x",Ѓ:"Г",ѓ:"г",Ќ:"К",ќ:"к",A̋:"A",a̋:"a",E̋:"E",e̋:"e",I̋:"I",i̋:"i",Ǹ:"N",ǹ:"n",Ồ:"O",ồ:"o",Ṑ:"O",ṑ:"o",Ừ:"U",ừ:"u",Ẁ:"W",ẁ:"w",Ỳ:"Y",ỳ:"y",Ȁ:"A",ȁ:"a",Ȅ:"E",ȅ:"e",Ȉ:"I",ȉ:"i",Ȍ:"O",ȍ:"o",Ȑ:"R",ȑ:"r",Ȕ:"U",ȕ:"u",B̌:"B",b̌:"b",Č̣:"C",č̣:"c",Ê̌:"E",ê̌:"e",F̌:"F",f̌:"f",Ǧ:"G",ǧ:"g",Ȟ:"H",ȟ:"h",J̌:"J",ǰ:"j",Ǩ:"K",ǩ:"k",M̌:"M",m̌:"m",P̌:"P",p̌:"p",Q̌:"Q",q̌:"q",Ř̩:"R",ř̩:"r",Ṧ:"S",ṧ:"s",V̌:"V",v̌:"v",W̌:"W",w̌:"w",X̌:"X",x̌:"x",Y̌:"Y",y̌:"y",A̧:"A",a̧:"a",B̧:"B",b̧:"b",Ḑ:"D",ḑ:"d",Ȩ:"E",ȩ:"e",Ɛ̧:"E",ɛ̧:"e",Ḩ:"H",ḩ:"h",I̧:"I",i̧:"i",Ɨ̧:"I",ɨ̧:"i",M̧:"M",m̧:"m",O̧:"O",o̧:"o",Q̧:"Q",q̧:"q",U̧:"U",u̧:"u",X̧:"X",x̧:"x",Z̧:"Z",z̧:"z",й:"и",Й:"И",ё:"е",Ё:"Е"},s=Object.keys(t).join("|"),o=new RegExp(s,"g"),n=new RegExp(s,"");function i(e){return t[e]}var r=function(e){return e.replace(o,i)};e.exports=r,e.exports.has=function(e){return!!e.match(n)},e.exports.remove=r}},t={};function s(o){var n=t[o];if(void 0!==n)return n.exports;var i=t[o]={exports:{}};return e[o].call(i.exports,i,i.exports,s),i.exports}s.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return s.d(t,{a:t}),t},s.d=(e,t)=>{for(var o in t)s.o(t,o)&&!s.o(e,o)&&Object.defineProperty(e,o,{enumerable:!0,get:t[o]})},s.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),s.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var o={};(()=>{"use strict";s.r(o),s.d(o,{AlignmentToolbar:()=>Qh,Autocomplete:()=>qh,AutosaveMonitor:()=>Wc,BlockAlignmentToolbar:()=>Xh,BlockControls:()=>Jh,BlockEdit:()=>eg,BlockEditorKeyboardShortcuts:()=>tg,BlockFormatControls:()=>sg,BlockIcon:()=>og,BlockInspector:()=>ng,BlockList:()=>ig,BlockMover:()=>rg,BlockNavigationDropdown:()=>ag,BlockSelectionClearer:()=>lg,BlockSettingsMenu:()=>cg,BlockTitle:()=>dg,BlockToolbar:()=>ug,CharacterCount:()=>zh,ColorPalette:()=>pg,ContrastChecker:()=>mg,CopyHandler:()=>hg,DefaultBlockAppender:()=>gg,DocumentBar:()=>Xc,DocumentOutline:()=>ld,DocumentOutlineCheck:()=>cd,EditorHistoryRedo:()=>md,EditorHistoryUndo:()=>hd,EditorKeyboardShortcuts:()=>Fl,EditorKeyboardShortcutsRegister:()=>dd,EditorNotices:()=>_d,EditorProvider:()=>ql,EditorSnackbars:()=>bd,EntitiesSavedStates:()=>jd,ErrorBoundary:()=>Nd,FontSizePicker:()=>_g,InnerBlocks:()=>bg,Inserter:()=>fg,InspectorAdvancedControls:()=>yg,InspectorControls:()=>xg,LocalAutosaveMonitor:()=>Md,MediaPlaceholder:()=>Pg,MediaUpload:()=>jg,MediaUploadCheck:()=>Eg,MultiSelectScrollIntoView:()=>Tg,NavigableToolbar:()=>Bg,ObserveTyping:()=>Ig,PageAttributesCheck:()=>Ld,PageAttributesOrder:()=>Vd,PageAttributesPanel:()=>eu,PageAttributesParent:()=>Qd,PageTemplate:()=>uu,PanelColorSettings:()=>vg,PlainText:()=>Sg,PluginBlockSettingsMenuItem:()=>Su,PluginDocumentSettingPanel:()=>vu,PluginMoreMenuItem:()=>wu,PluginPostPublishPanel:()=>ju,PluginPostStatusInfo:()=>Iu,PluginPrePublishPanel:()=>Ru,PluginPreviewMenuItem:()=>Mu,PluginSidebar:()=>Lu,PluginSidebarMoreMenuItem:()=>Ou,PostAuthor:()=>qu,PostAuthorCheck:()=>Qu,PostAuthorPanel:()=>Ju,PostComments:()=>tp,PostDiscussionPanel:()=>rp,PostExcerpt:()=>ap,PostExcerptCheck:()=>lp,PostExcerptPanel:()=>gp,PostFeaturedImage:()=>Pp,PostFeaturedImageCheck:()=>yp,PostFeaturedImagePanel:()=>Ep,PostFormat:()=>Ip,PostFormatCheck:()=>Tp,PostLastRevision:()=>Rp,PostLastRevisionCheck:()=>Np,PostLastRevisionPanel:()=>Mp,PostLockedModal:()=>Lp,PostPendingStatus:()=>Fp,PostPendingStatusCheck:()=>Op,PostPingbacks:()=>sp,PostPreviewButton:()=>Vp,PostPublishButton:()=>Gp,PostPublishButtonLabel:()=>zp,PostPublishPanel:()=>Qm,PostSavedState:()=>rh,PostSchedule:()=>pm,PostScheduleCheck:()=>ah,PostScheduleLabel:()=>hm,PostSchedulePanel:()=>ch,PostSticky:()=>th,PostStickyCheck:()=>eh,PostSwitchToDraftButton:()=>dh,PostSyncStatus:()=>uh,PostTaxonomies:()=>mh,PostTaxonomiesCheck:()=>hh,PostTaxonomiesFlatTermSelector:()=>jm,PostTaxonomiesHierarchicalTermSelector:()=>Lm,PostTaxonomiesPanel:()=>_h,PostTemplatePanel:()=>Gu,PostTextEditor:()=>bh,PostTitle:()=>kh,PostTitleRaw:()=>Ch,PostTrash:()=>jh,PostTrashCheck:()=>Ph,PostTypeSupportCheck:()=>Od,PostURL:()=>Eh,PostURLCheck:()=>Th,PostURLLabel:()=>Bh,PostURLPanel:()=>Nh,PostVisibility:()=>Zp,PostVisibilityCheck:()=>Rh,PostVisibilityLabel:()=>Kp,RichText:()=>Kh,RichTextShortcut:()=>wg,RichTextToolbarButton:()=>kg,ServerSideRender:()=>Wh(),SkipToSelectedBlock:()=>Ng,TableOfContents:()=>Hh,TextEditorGlobalKeyboardShortcuts:()=>Yg,ThemeSupportCheck:()=>bp,TimeToRead:()=>Vh,URLInput:()=>Dg,URLInputButton:()=>Ag,URLPopover:()=>Rg,UnsavedChangesWarning:()=>Gh,VisualEditorGlobalKeyboardShortcuts:()=>Zg,Warning:()=>Mg,WordCount:()=>Oh,WritingFlow:()=>Lg,__unstableRichTextInputEvent:()=>Cg,cleanForSlug:()=>Kg,createCustomColorsHOC:()=>Og,getColorClassName:()=>Fg,getColorObjectByAttributeValues:()=>Vg,getColorObjectByColorValue:()=>zg,getFontSize:()=>Ug,getFontSizeClass:()=>Hg,getTemplatePartIcon:()=>U,mediaUpload:()=>Mr,privateApis:()=>Nb,registerEntityAction:()=>Db,registerEntityField:()=>Rb,store:()=>Ac,storeConfig:()=>Dc,transformStyles:()=>h.transformStyles,unregisterEntityAction:()=>Ab,unregisterEntityField:()=>Mb,useEntitiesSavedStatesIsDirty:()=>Cd,usePostScheduleLabel:()=>gm,usePostURLLabel:()=>Ih,usePostVisibilityLabel:()=>qp,userAutocompleter:()=>Mc,withColorContext:()=>Gg,withColors:()=>$g,withFontSizes:()=>Wg});var e={};s.r(e),s.d(e,{__experimentalGetDefaultTemplatePartAreas:()=>ls,__experimentalGetDefaultTemplateType:()=>cs,__experimentalGetDefaultTemplateTypes:()=>as,__experimentalGetTemplateInfo:()=>ds,__unstableIsEditorReady:()=>et,canInsertBlockType:()=>os,canUserUseUnfilteredHTML:()=>$e,didPostSaveRequestFail:()=>Ee,didPostSaveRequestSucceed:()=>je,getActivePostLock:()=>Ge,getAdjacentBlockClientId:()=>Et,getAutosaveAttribute:()=>me,getBlock:()=>mt,getBlockAttributes:()=>pt,getBlockCount:()=>yt,getBlockHierarchyRootClientId:()=>jt,getBlockIndex:()=>Ut,getBlockInsertionPoint:()=>Xt,getBlockListSettings:()=>rs,getBlockMode:()=>Kt,getBlockName:()=>dt,getBlockOrder:()=>zt,getBlockRootClientId:()=>Pt,getBlockSelectionEnd:()=>vt,getBlockSelectionStart:()=>xt,getBlocks:()=>ht,getBlocksByClientId:()=>bt,getClientIdsOfDescendants:()=>gt,getClientIdsWithDescendants:()=>_t,getCurrentPost:()=>oe,getCurrentPostAttribute:()=>de,getCurrentPostId:()=>ie,getCurrentPostLastRevisionId:()=>le,getCurrentPostRevisionsCount:()=>ae,getCurrentPostType:()=>ne,getCurrentTemplateId:()=>re,getDeviceType:()=>ot,getEditedPostAttribute:()=>pe,getEditedPostContent:()=>De,getEditedPostPreviewLink:()=>Ie,getEditedPostSlug:()=>Le,getEditedPostVisibility:()=>he,getEditorBlocks:()=>Ze,getEditorMode:()=>rt,getEditorSelection:()=>Je,getEditorSelectionEnd:()=>Xe,getEditorSelectionStart:()=>Qe,getEditorSettings:()=>tt,getFirstMultiSelectedBlockClientId:()=>At,getGlobalBlockCount:()=>ft,getInserterItems:()=>ns,getLastMultiSelectedBlockClientId:()=>Rt,getMultiSelectedBlockClientIds:()=>Nt,getMultiSelectedBlocks:()=>Dt,getMultiSelectedBlocksEndClientId:()=>Vt,getMultiSelectedBlocksStartClientId:()=>Ft,getNextBlockClientId:()=>Bt,getPermalink:()=>Me,getPermalinkParts:()=>Oe,getPostEdits:()=>ce,getPostLockUser:()=>He,getPostTypeLabel:()=>us,getPreviousBlockClientId:()=>Tt,getRenderingMode:()=>st,getSelectedBlock:()=>Ct,getSelectedBlockClientId:()=>kt,getSelectedBlockCount:()=>St,getSelectedBlocksInitialCaretPosition:()=>It,getStateBeforeOptimisticTransaction:()=>at,getSuggestedPostFormat:()=>Ne,getTemplate:()=>ts,getTemplateLock:()=>ss,hasChangedContent:()=>J,hasEditorRedo:()=>Q,hasEditorUndo:()=>q,hasInserterItems:()=>is,hasMultiSelection:()=>Wt,hasNonPostEntityChanges:()=>te,hasSelectedBlock:()=>wt,hasSelectedInnerBlock:()=>Gt,inSomeHistory:()=>lt,isAncestorMultiSelected:()=>Ot,isAutosavingPost:()=>Te,isBlockInsertionPointVisible:()=>Jt,isBlockMultiSelected:()=>Lt,isBlockSelected:()=>Ht,isBlockValid:()=>ut,isBlockWithinSelection:()=>$t,isCaretWithinFormattedText:()=>Qt,isCleanNewPost:()=>se,isCurrentPostPending:()=>ge,isCurrentPostPublished:()=>_e,isCurrentPostScheduled:()=>fe,isDeletingPost:()=>ke,isEditedPostAutosaveable:()=>ve,isEditedPostBeingScheduled:()=>Se,isEditedPostDateFloating:()=>we,isEditedPostDirty:()=>ee,isEditedPostEmpty:()=>xe,isEditedPostNew:()=>X,isEditedPostPublishable:()=>be,isEditedPostSaveable:()=>ye,isEditorPanelEnabled:()=>Ke,isEditorPanelOpened:()=>qe,isEditorPanelRemoved:()=>Ye,isFirstMultiSelectedBlock:()=>Mt,isInserterOpened:()=>it,isListViewOpened:()=>nt,isMultiSelecting:()=>Zt,isPermalinkEditable:()=>Re,isPostAutosavingLocked:()=>ze,isPostLockTakeover:()=>Ue,isPostLocked:()=>Fe,isPostSavingLocked:()=>Ve,isPreviewingPost:()=>Be,isPublishSidebarEnabled:()=>We,isPublishSidebarOpened:()=>ps,isPublishingPost:()=>Ae,isSavingNonPostEntityChanges:()=>Pe,isSavingPost:()=>Ce,isSelectionEnabled:()=>Yt,isTyping:()=>qt,isValidTemplate:()=>es});var t={};s.r(t),s.d(t,{__experimentalTearDownEditor:()=>vs,__unstableSaveForPreview:()=>Is,autosave:()=>Bs,clearSelectedBlock:()=>ho,closePublishSidebar:()=>so,createUndoLevel:()=>As,disablePublishSidebar:()=>Ls,editPost:()=>Ps,enablePublishSidebar:()=>Ms,enterFormattedText:()=>Do,exitFormattedText:()=>Ao,hideInsertionPoint:()=>ko,insertBlock:()=>vo,insertBlocks:()=>So,insertDefaultBlock:()=>Ro,lockPostAutosaving:()=>Vs,lockPostSaving:()=>Os,mergeBlocks:()=>jo,moveBlockToPosition:()=>xo,moveBlocksDown:()=>bo,moveBlocksUp:()=>yo,multiSelect:()=>mo,openPublishSidebar:()=>to,receiveBlocks:()=>ro,redo:()=>Ns,refreshPost:()=>Es,removeBlock:()=>To,removeBlocks:()=>Eo,removeEditorPanel:()=>Ys,replaceBlock:()=>fo,replaceBlocks:()=>_o,resetBlocks:()=>io,resetEditorBlocks:()=>Us,resetPost:()=>Ss,savePost:()=>js,selectBlock:()=>co,setDeviceType:()=>$s,setEditedPost:()=>Cs,setIsInserterOpened:()=>Ks,setIsListViewOpened:()=>qs,setRenderingMode:()=>Gs,setTemplateValidity:()=>Co,setupEditor:()=>xs,setupEditorState:()=>ks,showInsertionPoint:()=>wo,startMultiSelect:()=>uo,startTyping:()=>Io,stopMultiSelect:()=>po,stopTyping:()=>No,switchEditorMode:()=>eo,synchronizeTemplate:()=>Po,toggleBlockMode:()=>Bo,toggleDistractionFree:()=>Qs,toggleEditorPanelEnabled:()=>Ws,toggleEditorPanelOpened:()=>Zs,togglePublishSidebar:()=>oo,toggleSelection:()=>go,toggleSpotlightMode:()=>Xs,toggleTopToolbar:()=>Js,trashPost:()=>Ts,undo:()=>Ds,unlockPostAutosaving:()=>zs,unlockPostSaving:()=>Fs,updateBlock:()=>ao,updateBlockAttributes:()=>lo,updateBlockListSettings:()=>Mo,updateEditorSettings:()=>Hs,updatePost:()=>ws,updatePostLock:()=>Rs});var n={};s.r(n),s.d(n,{closeModal:()=>Ra,disableComplementaryArea:()=>Ea,enableComplementaryArea:()=>ja,openModal:()=>Aa,pinItem:()=>Ta,setDefaultComplementaryArea:()=>Pa,setFeatureDefaults:()=>Da,setFeatureValue:()=>Na,toggleFeature:()=>Ia,unpinItem:()=>Ba});var i={};s.r(i),s.d(i,{getActiveComplementaryArea:()=>Ma,isComplementaryAreaLoading:()=>La,isFeatureActive:()=>Fa,isItemPinned:()=>Oa,isModalActive:()=>Va});var r={};s.r(r),s.d(r,{ActionItem:()=>Za,ComplementaryArea:()=>tl,ComplementaryAreaMoreMenuItem:()=>Ka,FullscreenMode:()=>sl,InterfaceSkeleton:()=>al,NavigableRegion:()=>nl,PinnedItems:()=>Qa,store:()=>Ua});var a={};s.r(a),s.d(a,{createTemplate:()=>lc,hideBlockTypes:()=>dc,registerEntityAction:()=>tc,registerEntityField:()=>oc,registerPostTypeSchema:()=>rc,removeTemplates:()=>mc,revertTemplate:()=>pc,saveDirtyEntities:()=>uc,setCurrentTemplateId:()=>ac,setDefaultRenderingMode:()=>hc,setIsReady:()=>ic,showBlockTypes:()=>cc,unregisterEntityAction:()=>sc,unregisterEntityField:()=>nc});var l={};s.r(l),s.d(l,{getDefaultRenderingMode:()=>Nc,getEntityActions:()=>Ec,getEntityFields:()=>Bc,getInserter:()=>Sc,getInserterSidebarToggleRef:()=>kc,getListViewToggleRef:()=>wc,getPostBlocksByName:()=>Ic,getPostIcon:()=>Pc,hasPostMetaChanges:()=>jc,isEntityReady:()=>Tc});const c=window.wp.data,d=window.wp.coreData,u=window.wp.element,p=window.wp.compose,m=window.wp.hooks,h=window.wp.blockEditor,g={...h.SETTINGS_DEFAULTS,richEditingEnabled:!0,codeEditingEnabled:!0,fontLibraryEnabled:!0,enableCustomFields:void 0,defaultRenderingMode:"post-only"};const _=(0,c.combineReducers)({actions:function(e={},t){var s;switch(t.type){case"REGISTER_ENTITY_ACTION":return{...e,[t.kind]:{...e[t.kind],[t.name]:[...(null!==(s=e[t.kind]?.[t.name])&&void 0!==s?s:[]).filter((e=>e.id!==t.config.id)),t.config]}};case"UNREGISTER_ENTITY_ACTION":var o;return{...e,[t.kind]:{...e[t.kind],[t.name]:(null!==(o=e[t.kind]?.[t.name])&&void 0!==o?o:[]).filter((e=>e.id!==t.actionId))}}}return e},fields:function(e={},t){var s,o;switch(t.type){case"REGISTER_ENTITY_FIELD":return{...e,[t.kind]:{...e[t.kind],[t.name]:[...(null!==(s=e[t.kind]?.[t.name])&&void 0!==s?s:[]).filter((e=>e.id!==t.config.id)),t.config]}};case"UNREGISTER_ENTITY_FIELD":return{...e,[t.kind]:{...e[t.kind],[t.name]:(null!==(o=e[t.kind]?.[t.name])&&void 0!==o?o:[]).filter((e=>e.id!==t.fieldId))}}}return e},isReady:function(e={},t){return"SET_IS_READY"===t.type?{...e,[t.kind]:{...e[t.kind],[t.name]:!0}}:e}});function f(e){return e&&"object"==typeof e&&"raw"in e?e.raw:e}const b=(0,c.combineReducers)({postId:function(e=null,t){return"SET_EDITED_POST"===t.type?t.postId:e},postType:function(e=null,t){return"SET_EDITED_POST"===t.type?t.postType:e},templateId:function(e=null,t){return"SET_CURRENT_TEMPLATE_ID"===t.type?t.id:e},saving:function(e={},t){switch(t.type){case"REQUEST_POST_UPDATE_START":case"REQUEST_POST_UPDATE_FINISH":return{pending:"REQUEST_POST_UPDATE_START"===t.type,options:t.options||{}}}return e},deleting:function(e={},t){switch(t.type){case"REQUEST_POST_DELETE_START":case"REQUEST_POST_DELETE_FINISH":return{pending:"REQUEST_POST_DELETE_START"===t.type}}return e},postLock:function(e={isLocked:!1},t){return"UPDATE_POST_LOCK"===t.type?t.lock:e},template:function(e={isValid:!0},t){return"SET_TEMPLATE_VALIDITY"===t.type?{...e,isValid:t.isValid}:e},postSavingLock:function(e={},t){switch(t.type){case"LOCK_POST_SAVING":return{...e,[t.lockName]:!0};case"UNLOCK_POST_SAVING":{const{[t.lockName]:s,...o}=e;return o}}return e},editorSettings:function(e=g,t){return"UPDATE_EDITOR_SETTINGS"===t.type?{...e,...t.settings}:e},postAutosavingLock:function(e={},t){switch(t.type){case"LOCK_POST_AUTOSAVING":return{...e,[t.lockName]:!0};case"UNLOCK_POST_AUTOSAVING":{const{[t.lockName]:s,...o}=e;return o}}return e},renderingMode:function(e="post-only",t){return"SET_RENDERING_MODE"===t.type?t.mode:e},deviceType:function(e="Desktop",t){return"SET_DEVICE_TYPE"===t.type?t.deviceType:e},removedPanels:function(e=[],t){if("REMOVE_PANEL"===t.type)if(!e.includes(t.panelName))return[...e,t.panelName];return e},blockInserterPanel:function(e=!1,t){switch(t.type){case"SET_IS_LIST_VIEW_OPENED":return!t.isOpen&&e;case"SET_IS_INSERTER_OPENED":return t.value}return e},inserterSidebarToggleRef:function(e={current:null}){return e},listViewPanel:function(e=!1,t){switch(t.type){case"SET_IS_INSERTER_OPENED":return!t.value&&e;case"SET_IS_LIST_VIEW_OPENED":return t.isOpen}return e},listViewToggleRef:function(e={current:null}){return e},publishSidebarActive:function(e=!1,t){switch(t.type){case"OPEN_PUBLISH_SIDEBAR":return!0;case"CLOSE_PUBLISH_SIDEBAR":return!1;case"TOGGLE_PUBLISH_SIDEBAR":return!e}return e},dataviews:_}),y=window.wp.blocks,x=window.wp.date,v=window.wp.url,S=window.wp.deprecated;var w=s.n(S);const k=window.wp.preferences,C=new Set(["meta"]),P=/%(?:postname|pagename)%/,j=6e4,E=["title","excerpt","content"],T="wp_template",B="wp_template_part",I="wp_block",N="wp_navigation",D="custom",A=["wp_template","wp_template_part"],R=[...A,"wp_block","wp_navigation"],M=window.wp.primitives,L=window.ReactJSXRuntime,O=(0,L.jsx)(M.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,L.jsx)(M.Path,{d:"M18.5 10.5H10v8h8a.5.5 0 00.5-.5v-7.5zm-10 0h-3V18a.5.5 0 00.5.5h2.5v-8zM6 4h12a2 2 0 012 2v12a2 2 0 01-2 2H6a2 2 0 01-2-2V6a2 2 0 012-2z"})}),F=(0,L.jsx)(M.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,L.jsx)(M.Path,{fillRule:"evenodd",d:"M18 5.5h-8v8h8.5V6a.5.5 0 00-.5-.5zm-9.5 8h-3V6a.5.5 0 01.5-.5h2.5v8zM6 4h12a2 2 0 012 2v12a2 2 0 01-2 2H6a2 2 0 01-2-2V6a2 2 0 012-2z"})}),V=(0,L.jsx)(M.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,L.jsx)(M.Path,{d:"M18 5.5H6a.5.5 0 00-.5.5v3h13V6a.5.5 0 00-.5-.5zm.5 5H10v8h8a.5.5 0 00.5-.5v-7.5zM6 4h12a2 2 0 012 2v12a2 2 0 01-2 2H6a2 2 0 01-2-2V6a2 2 0 012-2z"})}),z=(0,L.jsx)(M.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,L.jsx)(M.Path,{d:"M21.3 10.8l-5.6-5.6c-.7-.7-1.8-.7-2.5 0l-5.6 5.6c-.7.7-.7 1.8 0 2.5l5.6 5.6c.3.3.8.5 1.2.5s.9-.2 1.2-.5l5.6-5.6c.8-.7.8-1.9.1-2.5zm-17.6 1L10 5.5l-1-1-6.3 6.3c-.7.7-.7 1.8 0 2.5L9 19.5l1.1-1.1-6.3-6.3c-.2 0-.2-.2-.1-.3z"})});function U(e){return"header"===e?O:"footer"===e?F:"sidebar"===e?V:z}const H=window.wp.privateApis,{lock:G,unlock:$}=(0,H.__dangerousOptInToUnstableAPIsOnlyForCoreModules)("I acknowledge private features are not for use in themes or plugins and doing so will break in the next version of WordPress.","@wordpress/editor"),W=(0,L.jsx)(M.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,L.jsx)(M.Path,{d:"M18 5.5H6a.5.5 0 00-.5.5v3h13V6a.5.5 0 00-.5-.5zm.5 5H10v8h8a.5.5 0 00.5-.5v-7.5zm-10 0h-3V18a.5.5 0 00.5.5h2.5v-8zM6 4h12a2 2 0 012 2v12a2 2 0 01-2 2H6a2 2 0 01-2-2V6a2 2 0 012-2z"})}),Z={},Y=e=>{var t;if(!e)return Z;const{templateTypes:s,templateAreas:o,template:n}=e,{description:i,slug:r,title:a,area:l}=n,{title:c,description:d}=null!==(t=Object.values(s).find((e=>e.slug===r)))&&void 0!==t?t:Z,u="string"==typeof a?a:a?.rendered,p="string"==typeof i?i:i?.raw,m=o?.map((e=>({...e,icon:U(e.icon)}))),h=m?.find((e=>l===e.area))?.icon||W;return{title:u&&u!==r?u:c||r,description:p||d,icon:h}},K={},q=(0,c.createRegistrySelector)((e=>()=>e(d.store).hasUndo())),Q=(0,c.createRegistrySelector)((e=>()=>e(d.store).hasRedo()));function X(e){return"auto-draft"===oe(e).status}function J(e){return"content"in ce(e)}const ee=(0,c.createRegistrySelector)((e=>t=>{const s=ne(t),o=ie(t);return e(d.store).hasEditsForEntityRecord("postType",s,o)})),te=(0,c.createRegistrySelector)((e=>t=>{const s=e(d.store).__experimentalGetDirtyEntityRecords(),{type:o,id:n}=oe(t);return s.some((e=>"postType"!==e.kind||e.name!==o||e.key!==n))}));function se(e){return!ee(e)&&X(e)}const oe=(0,c.createRegistrySelector)((e=>t=>{const s=ie(t),o=ne(t),n=e(d.store).getRawEntityRecord("postType",o,s);return n||K}));function ne(e){return e.postType}function ie(e){return e.postId}function re(e){return e.templateId}function ae(e){var t;return null!==(t=oe(e)._links?.["version-history"]?.[0]?.count)&&void 0!==t?t:0}function le(e){var t;return null!==(t=oe(e)._links?.["predecessor-version"]?.[0]?.id)&&void 0!==t?t:null}const ce=(0,c.createRegistrySelector)((e=>t=>{const s=ne(t),o=ie(t);return e(d.store).getEntityRecordEdits("postType",s,o)||K}));function de(e,t){switch(t){case"type":return ne(e);case"id":return ie(e);default:const s=oe(e);if(!s.hasOwnProperty(t))break;return f(s[t])}}const ue=(0,c.createSelector)(((e,t)=>{const s=ce(e);return s.hasOwnProperty(t)?{...de(e,t),...s[t]}:de(e,t)}),((e,t)=>[de(e,t),ce(e)[t]]));function pe(e,t){if("content"===t)return De(e);const s=ce(e);return s.hasOwnProperty(t)?C.has(t)?ue(e,t):s[t]:de(e,t)}const me=(0,c.createRegistrySelector)((e=>(t,s)=>{if(!E.includes(s)&&"preview_link"!==s)return;const o=ne(t);if("wp_template"===o)return!1;const n=ie(t),i=e(d.store).getCurrentUser()?.id,r=e(d.store).getAutosave(o,n,i);return r?f(r[s]):void 0}));function he(e){if("private"===pe(e,"status"))return"private";return pe(e,"password")?"password":"public"}function ge(e){return"pending"===oe(e).status}function _e(e,t){const s=t||oe(e);return-1!==["publish","private"].indexOf(s.status)||"future"===s.status&&!(0,x.isInTheFuture)(new Date(Number((0,x.getDate)(s.date))-j))}function fe(e){return"future"===oe(e).status&&!_e(e)}function be(e){const t=oe(e);return ee(e)||-1===["publish","private","future"].indexOf(t.status)}function ye(e){return!Ce(e)&&(!!pe(e,"title")||!!pe(e,"excerpt")||!xe(e)||"native"===u.Platform.OS)}const xe=(0,c.createRegistrySelector)((e=>t=>{const s=ie(t),o=ne(t),n=e(d.store).getEditedEntityRecord("postType",o,s);if("function"!=typeof n.content)return!n.content;const i=pe(t,"blocks");if(0===i.length)return!0;if(i.length>1)return!1;const r=i[0].name;return(r===(0,y.getDefaultBlockName)()||r===(0,y.getFreeformContentHandlerName)())&&!De(t)})),ve=(0,c.createRegistrySelector)((e=>t=>{if(!ye(t))return!1;if(ze(t))return!1;const s=ne(t);if("wp_template"===s)return!1;const o=ie(t),n=e(d.store).hasFetchedAutosaves(s,o),i=e(d.store).getCurrentUser()?.id,r=e(d.store).getAutosave(s,o,i);return!!n&&(!r||(!!J(t)||["title","excerpt","meta"].some((e=>f(r[e])!==pe(t,e)))))}));function Se(e){const t=pe(e,"date"),s=new Date(Number((0,x.getDate)(t))-j);return(0,x.isInTheFuture)(s)}function we(e){const t=pe(e,"date"),s=pe(e,"modified"),o=oe(e).status;return("draft"===o||"auto-draft"===o||"pending"===o)&&(t===s||null===t)}function ke(e){return!!e.deleting.pending}function Ce(e){return!!e.saving.pending}const Pe=(0,c.createRegistrySelector)((e=>t=>{const s=e(d.store).__experimentalGetEntitiesBeingSaved(),{type:o,id:n}=oe(t);return s.some((e=>"postType"!==e.kind||e.name!==o||e.key!==n))})),je=(0,c.createRegistrySelector)((e=>t=>{const s=ne(t),o=ie(t);return!e(d.store).getLastEntitySaveError("postType",s,o)})),Ee=(0,c.createRegistrySelector)((e=>t=>{const s=ne(t),o=ie(t);return!!e(d.store).getLastEntitySaveError("postType",s,o)}));function Te(e){return Ce(e)&&Boolean(e.saving.options?.isAutosave)}function Be(e){return Ce(e)&&Boolean(e.saving.options?.isPreview)}function Ie(e){if(e.saving.pending||Ce(e))return;let t=me(e,"preview_link");t&&"draft"!==oe(e).status||(t=pe(e,"link"),t&&(t=(0,v.addQueryArgs)(t,{preview:!0})));const s=pe(e,"featured_media");return t&&s?(0,v.addQueryArgs)(t,{_thumbnail_id:s}):t}const Ne=(0,c.createRegistrySelector)((e=>()=>{const t=e(h.store).getBlocks();if(t.length>2)return null;let s;if(1===t.length&&(s=t[0].name,"core/embed"===s)){const e=t[0].attributes?.providerNameSlug;["youtube","vimeo"].includes(e)?s="core/video":["spotify","soundcloud"].includes(e)&&(s="core/audio")}switch(2===t.length&&"core/paragraph"===t[1].name&&(s=t[0].name),s){case"core/image":return"image";case"core/quote":case"core/pullquote":return"quote";case"core/gallery":return"gallery";case"core/video":return"video";case"core/audio":return"audio";default:return null}})),De=(0,c.createRegistrySelector)((e=>t=>{const s=ie(t),o=ne(t),n=e(d.store).getEditedEntityRecord("postType",o,s);if(n){if("function"==typeof n.content)return n.content(n);if(n.blocks)return(0,y.__unstableSerializeAndClean)(n.blocks);if(n.content)return n.content}return""}));function Ae(e){return Ce(e)&&!_e(e)&&"publish"===pe(e,"status")}function Re(e){const t=pe(e,"permalink_template");return P.test(t)}function Me(e){const t=Oe(e);if(!t)return null;const{prefix:s,postName:o,suffix:n}=t;return Re(e)?s+o+n:s}function Le(e){return pe(e,"slug")||(0,v.cleanForSlug)(pe(e,"title"))||ie(e)}function Oe(e){const t=pe(e,"permalink_template");if(!t)return null;const s=pe(e,"slug")||pe(e,"generated_slug"),[o,n]=t.split(P);return{prefix:o,postName:s,suffix:n}}function Fe(e){return e.postLock.isLocked}function Ve(e){return Object.keys(e.postSavingLock).length>0}function ze(e){return Object.keys(e.postAutosavingLock).length>0}function Ue(e){return e.postLock.isTakeover}function He(e){return e.postLock.user}function Ge(e){return e.postLock.activePostLock}function $e(e){return Boolean(oe(e)._links?.hasOwnProperty("wp:action-unfiltered-html"))}const We=(0,c.createRegistrySelector)((e=>()=>!!e(k.store).get("core","isPublishSidebarEnabled"))),Ze=(0,c.createSelector)((e=>pe(e,"blocks")||(0,y.parse)(De(e))),(e=>[pe(e,"blocks"),De(e)]));function Ye(e,t){return e.removedPanels.includes(t)}const Ke=(0,c.createRegistrySelector)((e=>(t,s)=>{const o=e(k.store).get("core","inactivePanels");return!Ye(t,s)&&!o?.includes(s)})),qe=(0,c.createRegistrySelector)((e=>(t,s)=>{const o=e(k.store).get("core","openPanels");return!!o?.includes(s)}));function Qe(e){return w()("select('core/editor').getEditorSelectionStart",{since:"5.8",alternative:"select('core/editor').getEditorSelection"}),pe(e,"selection")?.selectionStart}function Xe(e){return w()("select('core/editor').getEditorSelectionStart",{since:"5.8",alternative:"select('core/editor').getEditorSelection"}),pe(e,"selection")?.selectionEnd}function Je(e){return pe(e,"selection")}function et(e){return!!e.postId}function tt(e){return e.editorSettings}function st(e){return e.renderingMode}const ot=(0,c.createRegistrySelector)((e=>t=>$(e(h.store)).isZoomOut()?"Desktop":t.deviceType));function nt(e){return e.listViewPanel}function it(e){return!!e.blockInserterPanel}const rt=(0,c.createRegistrySelector)((e=>()=>{var t;return null!==(t=e(k.store).get("core","editorMode"))&&void 0!==t?t:"visual"}));function at(){return w()("select('core/editor').getStateBeforeOptimisticTransaction",{since:"5.7",hint:"No state history is kept on this store anymore"}),null}function lt(){return w()("select('core/editor').inSomeHistory",{since:"5.7",hint:"No state history is kept on this store anymore"}),!1}function ct(e){return(0,c.createRegistrySelector)((t=>(s,...o)=>(w()("`wp.data.select( 'core/editor' )."+e+"`",{since:"5.3",alternative:"`wp.data.select( 'core/block-editor' )."+e+"`",version:"6.2"}),t(h.store)[e](...o))))}const dt=ct("getBlockName"),ut=ct("isBlockValid"),pt=ct("getBlockAttributes"),mt=ct("getBlock"),ht=ct("getBlocks"),gt=ct("getClientIdsOfDescendants"),_t=ct("getClientIdsWithDescendants"),ft=ct("getGlobalBlockCount"),bt=ct("getBlocksByClientId"),yt=ct("getBlockCount"),xt=ct("getBlockSelectionStart"),vt=ct("getBlockSelectionEnd"),St=ct("getSelectedBlockCount"),wt=ct("hasSelectedBlock"),kt=ct("getSelectedBlockClientId"),Ct=ct("getSelectedBlock"),Pt=ct("getBlockRootClientId"),jt=ct("getBlockHierarchyRootClientId"),Et=ct("getAdjacentBlockClientId"),Tt=ct("getPreviousBlockClientId"),Bt=ct("getNextBlockClientId"),It=ct("getSelectedBlocksInitialCaretPosition"),Nt=ct("getMultiSelectedBlockClientIds"),Dt=ct("getMultiSelectedBlocks"),At=ct("getFirstMultiSelectedBlockClientId"),Rt=ct("getLastMultiSelectedBlockClientId"),Mt=ct("isFirstMultiSelectedBlock"),Lt=ct("isBlockMultiSelected"),Ot=ct("isAncestorMultiSelected"),Ft=ct("getMultiSelectedBlocksStartClientId"),Vt=ct("getMultiSelectedBlocksEndClientId"),zt=ct("getBlockOrder"),Ut=ct("getBlockIndex"),Ht=ct("isBlockSelected"),Gt=ct("hasSelectedInnerBlock"),$t=ct("isBlockWithinSelection"),Wt=ct("hasMultiSelection"),Zt=ct("isMultiSelecting"),Yt=ct("isSelectionEnabled"),Kt=ct("getBlockMode"),qt=ct("isTyping"),Qt=ct("isCaretWithinFormattedText"),Xt=ct("getBlockInsertionPoint"),Jt=ct("isBlockInsertionPointVisible"),es=ct("isValidTemplate"),ts=ct("getTemplate"),ss=ct("getTemplateLock"),os=ct("canInsertBlockType"),ns=ct("getInserterItems"),is=ct("hasInserterItems"),rs=ct("getBlockListSettings"),as=(0,c.createRegistrySelector)((e=>()=>(w()("select('core/editor').__experimentalGetDefaultTemplateTypes",{since:"6.8",alternative:"select('core/core-data').getCurrentTheme()?.default_template_types"}),e(d.store).getCurrentTheme()?.default_template_types))),ls=(0,c.createRegistrySelector)((e=>(0,c.createSelector)((()=>{w()("select('core/editor').__experimentalGetDefaultTemplatePartAreas",{since:"6.8",alternative:"select('core/core-data').getCurrentTheme()?.default_template_part_areas"});return(e(d.store).getCurrentTheme()?.default_template_part_areas||[]).map((e=>({...e,icon:U(e.icon)})))})))),cs=(0,c.createRegistrySelector)((e=>(0,c.createSelector)(((t,s)=>{var o;w()("select('core/editor').__experimentalGetDefaultTemplateType",{since:"6.8"});const n=e(d.store).getCurrentTheme()?.default_template_types;return n&&null!==(o=Object.values(n).find((e=>e.slug===s)))&&void 0!==o?o:K})))),ds=(0,c.createRegistrySelector)((e=>(0,c.createSelector)(((t,s)=>{if(w()("select('core/editor').__experimentalGetTemplateInfo",{since:"6.8"}),!s)return K;const o=e(d.store).getCurrentTheme(),n=o?.default_template_types||[];return Y({template:s,templateAreas:o?.default_template_part_areas||[],templateTypes:n})})))),us=(0,c.createRegistrySelector)((e=>t=>{const s=ne(t),o=e(d.store).getPostType(s);return o?.labels?.singular_name}));function ps(e){return e.publishSidebarActive}const ms=window.wp.a11y,hs=window.wp.apiFetch;var gs=s.n(hs);const _s=window.wp.notices,fs=window.wp.i18n;function bs(e,t){return`wp-autosave-block-editor-post-${t?"auto-draft":e}`}function ys(e,t){window.sessionStorage.removeItem(bs(e,t))}const xs=(e,t,s)=>({dispatch:o})=>{o.setEditedPost(e.type,e.id);if("auto-draft"===e.status&&s){let n;n="content"in t?t.content:e.content.raw;let i=(0,y.parse)(n);i=(0,y.synchronizeBlocksWithTemplate)(i,s),o.resetEditorBlocks(i,{__unstableShouldCreateUndoLevel:!1})}t&&Object.values(t).some((([t,s])=>{var o;return s!==(null!==(o=e[t]?.raw)&&void 0!==o?o:e[t])}))&&o.editPost(t)};function vs(){return w()("wp.data.dispatch( 'core/editor' ).__experimentalTearDownEditor",{since:"6.5"}),{type:"DO_NOTHING"}}function Ss(){return w()("wp.data.dispatch( 'core/editor' ).resetPost",{since:"6.0",version:"6.3",alternative:"Initialize the editor with the setupEditorState action"}),{type:"DO_NOTHING"}}function ws(){return w()("wp.data.dispatch( 'core/editor' ).updatePost",{since:"5.7",alternative:"Use the core entities store instead"}),{type:"DO_NOTHING"}}function ks(e){return w()("wp.data.dispatch( 'core/editor' ).setupEditorState",{since:"6.5",alternative:"wp.data.dispatch( 'core/editor' ).setEditedPost"}),Cs(e.type,e.id)}function Cs(e,t){return{type:"SET_EDITED_POST",postType:e,postId:t}}const Ps=(e,t)=>({select:s,registry:o})=>{const{id:n,type:i}=s.getCurrentPost();o.dispatch(d.store).editEntityRecord("postType",i,n,e,t)},js=(e={})=>async({select:t,dispatch:s,registry:o})=>{if(!t.isEditedPostSaveable())return;const n=t.getEditedPostContent();e.isAutosave||s.editPost({content:n},{undoIgnore:!0});const i=t.getCurrentPost();let r={id:i.id,...o.select(d.store).getEntityRecordNonTransientEdits("postType",i.type,i.id),content:n};s({type:"REQUEST_POST_UPDATE_START",options:e});let a=!1;try{r=await(0,m.applyFiltersAsync)("editor.preSavePost",r,e)}catch(e){a=e}if(!a)try{await o.dispatch(d.store).saveEntityRecord("postType",i.type,r,e)}catch(e){a=e.message&&"unknown_error"!==e.code?e.message:(0,fs.__)("An error occurred while updating.")}if(a||(a=o.select(d.store).getLastEntitySaveError("postType",i.type,i.id)),!a)try{await(0,m.applyFilters)("editor.__unstableSavePost",Promise.resolve(),e)}catch(e){a=e}if(!a)try{await(0,m.doActionAsync)("editor.savePost",{id:i.id},e)}catch(e){a=e}if(s({type:"REQUEST_POST_UPDATE_FINISH",options:e}),a){const e=function(e){const{post:t,edits:s,error:o}=e;if(o&&"rest_autosave_no_changes"===o.code)return[];const n=["publish","private","future"],i=-1!==n.indexOf(t.status),r={publish:(0,fs.__)("Publishing failed."),private:(0,fs.__)("Publishing failed."),future:(0,fs.__)("Scheduling failed.")};let a=i||-1===n.indexOf(s.status)?(0,fs.__)("Updating failed."):r[s.status];return o.message&&!/<\/?[^>]*>/.test(o.message)&&(a=[a,o.message].join(" ")),[a,{id:"editor-save"}]}({post:i,edits:r,error:a});e.length&&o.dispatch(_s.store).createErrorNotice(...e)}else{const s=t.getCurrentPost(),n=function(e){var t;const{previousPost:s,post:o,postType:n}=e;if(e.options?.isAutosave)return[];const i=["publish","private","future"],r=i.includes(s.status),a=i.includes(o.status),l="trash"===o.status&&"trash"!==s.status;let c,d,u=null!==(t=n?.viewable)&&void 0!==t&&t;l?(c=n.labels.item_trashed,u=!1):r||a?r&&!a?(c=n.labels.item_reverted_to_draft,u=!1):c=!r&&a?{publish:n.labels.item_published,private:n.labels.item_published_privately,future:n.labels.item_scheduled}[o.status]:n.labels.item_updated:(c=(0,fs.__)("Draft saved."),d=!0);const p=[];return u&&p.push({label:d?(0,fs.__)("View Preview"):n.labels.view_item,url:o.link}),[c,{id:"editor-save",type:"snackbar",actions:p}]}({previousPost:i,post:s,postType:await o.resolveSelect(d.store).getPostType(s.type),options:e});n.length&&o.dispatch(_s.store).createSuccessNotice(...n),e.isAutosave||o.dispatch(h.store).__unstableMarkLastChangeAsPersistent()}};function Es(){return w()("wp.data.dispatch( 'core/editor' ).refreshPost",{since:"6.0",version:"6.3",alternative:"Use the core entities store instead"}),{type:"DO_NOTHING"}}const Ts=()=>async({select:e,dispatch:t,registry:s})=>{const o=e.getCurrentPostType(),n=await s.resolveSelect(d.store).getPostType(o),{rest_base:i,rest_namespace:r="wp/v2"}=n;t({type:"REQUEST_POST_DELETE_START"});try{const s=e.getCurrentPost();await gs()({path:`/${r}/${i}/${s.id}`,method:"DELETE"}),await t.savePost()}catch(e){s.dispatch(_s.store).createErrorNotice(...(a={error:e},[a.error.message&&"unknown_error"!==a.error.code?a.error.message:(0,fs.__)("Trashing failed"),{id:"editor-trash-fail"}]))}var a;t({type:"REQUEST_POST_DELETE_FINISH"})},Bs=({local:e=!1,...t}={})=>async({select:s,dispatch:o})=>{const n=s.getCurrentPost();if("wp_template"!==n.type)if(e){const e=s.isEditedPostNew(),t=s.getEditedPostAttribute("title"),o=s.getEditedPostAttribute("content"),i=s.getEditedPostAttribute("excerpt");!function(e,t,s,o,n){window.sessionStorage.setItem(bs(e,t),JSON.stringify({post_title:s,content:o,excerpt:n}))}(n.id,e,t,o,i)}else await o.savePost({isAutosave:!0,...t})},Is=({forceIsAutosaveable:e}={})=>async({select:t,dispatch:s})=>{if((e||t.isEditedPostAutosaveable())&&!t.isPostLocked()){["draft","auto-draft"].includes(t.getEditedPostAttribute("status"))?await s.savePost({isPreview:!0}):await s.autosave({isPreview:!0})}return t.getEditedPostPreviewLink()},Ns=()=>({registry:e})=>{e.dispatch(d.store).redo()},Ds=()=>({registry:e})=>{e.dispatch(d.store).undo()};function As(){return w()("wp.data.dispatch( 'core/editor' ).createUndoLevel",{since:"6.0",version:"6.3",alternative:"Use the core entities store instead"}),{type:"DO_NOTHING"}}function Rs(e){return{type:"UPDATE_POST_LOCK",lock:e}}const Ms=()=>({registry:e})=>{e.dispatch(k.store).set("core","isPublishSidebarEnabled",!0)},Ls=()=>({registry:e})=>{e.dispatch(k.store).set("core","isPublishSidebarEnabled",!1)};function Os(e){return{type:"LOCK_POST_SAVING",lockName:e}}function Fs(e){return{type:"UNLOCK_POST_SAVING",lockName:e}}function Vs(e){return{type:"LOCK_POST_AUTOSAVING",lockName:e}}function zs(e){return{type:"UNLOCK_POST_AUTOSAVING",lockName:e}}const Us=(e,t={})=>({select:s,dispatch:o,registry:n})=>{const{__unstableShouldCreateUndoLevel:i,selection:r}=t,a={blocks:e,selection:r};if(!1!==i){const{id:e,type:t}=s.getCurrentPost();if(n.select(d.store).getEditedEntityRecord("postType",t,e).blocks===a.blocks)return void n.dispatch(d.store).__unstableCreateUndoLevel("postType",t,e);a.content=({blocks:e=[]})=>(0,y.__unstableSerializeAndClean)(e)}o.editPost(a)};function Hs(e){return{type:"UPDATE_EDITOR_SETTINGS",settings:e}}const Gs=e=>({dispatch:t,registry:s,select:o})=>{o.__unstableIsEditorReady()&&(s.dispatch(h.store).clearSelectedBlock(),t.editPost({selection:void 0},{undoIgnore:!0})),t({type:"SET_RENDERING_MODE",mode:e})};function $s(e){return{type:"SET_DEVICE_TYPE",deviceType:e}}const Ws=e=>({registry:t})=>{var s;const o=null!==(s=t.select(k.store).get("core","inactivePanels"))&&void 0!==s?s:[];let n;n=!!o?.includes(e)?o.filter((t=>t!==e)):[...o,e],t.dispatch(k.store).set("core","inactivePanels",n)},Zs=e=>({registry:t})=>{var s;const o=null!==(s=t.select(k.store).get("core","openPanels"))&&void 0!==s?s:[];let n;n=!!o?.includes(e)?o.filter((t=>t!==e)):[...o,e],t.dispatch(k.store).set("core","openPanels",n)};function Ys(e){return{type:"REMOVE_PANEL",panelName:e}}const Ks=e=>({dispatch:t,registry:s})=>{"object"==typeof e&&e.hasOwnProperty("rootClientId")&&e.hasOwnProperty("insertionIndex")&&$(s.dispatch(h.store)).setInsertionPoint({rootClientId:e.rootClientId,index:e.insertionIndex}),t({type:"SET_IS_INSERTER_OPENED",value:e})};function qs(e){return{type:"SET_IS_LIST_VIEW_OPENED",isOpen:e}}const Qs=({createNotice:e=!0}={})=>({dispatch:t,registry:s})=>{const o=s.select(k.store).get("core","distractionFree");o&&s.dispatch(k.store).set("core","fixedToolbar",!1),o||s.batch((()=>{s.dispatch(k.store).set("core","fixedToolbar",!0),t.setIsInserterOpened(!1),t.setIsListViewOpened(!1),$(s.dispatch(h.store)).resetZoomLevel()})),s.batch((()=>{s.dispatch(k.store).set("core","distractionFree",!o),e&&s.dispatch(_s.store).createInfoNotice(o?(0,fs.__)("Distraction free mode deactivated."):(0,fs.__)("Distraction free mode activated."),{id:"core/editor/distraction-free-mode/notice",type:"snackbar",actions:[{label:(0,fs.__)("Undo"),onClick:()=>{s.batch((()=>{s.dispatch(k.store).set("core","fixedToolbar",o),s.dispatch(k.store).toggle("core","distractionFree")}))}}]})}))},Xs=()=>({registry:e})=>{e.dispatch(k.store).toggle("core","focusMode");const t=e.select(k.store).get("core","focusMode");e.dispatch(_s.store).createInfoNotice(t?(0,fs.__)("Spotlight mode activated."):(0,fs.__)("Spotlight mode deactivated."),{id:"core/editor/toggle-spotlight-mode/notice",type:"snackbar",actions:[{label:(0,fs.__)("Undo"),onClick:()=>{e.dispatch(k.store).toggle("core","focusMode")}}]})},Js=()=>({registry:e})=>{e.dispatch(k.store).toggle("core","fixedToolbar");const t=e.select(k.store).get("core","fixedToolbar");e.dispatch(_s.store).createInfoNotice(t?(0,fs.__)("Top toolbar activated."):(0,fs.__)("Top toolbar deactivated."),{id:"core/editor/toggle-top-toolbar/notice",type:"snackbar",actions:[{label:(0,fs.__)("Undo"),onClick:()=>{e.dispatch(k.store).toggle("core","fixedToolbar")}}]})},eo=e=>({dispatch:t,registry:s})=>{if(s.dispatch(k.store).set("core","editorMode",e),"visual"!==e&&(s.dispatch(h.store).clearSelectedBlock(),$(s.dispatch(h.store)).resetZoomLevel()),"visual"===e)(0,ms.speak)((0,fs.__)("Visual editor selected"),"assertive");else if("text"===e){s.select(k.store).get("core","distractionFree")&&t.toggleDistractionFree(),(0,ms.speak)((0,fs.__)("Code editor selected"),"assertive")}};function to(){return{type:"OPEN_PUBLISH_SIDEBAR"}}function so(){return{type:"CLOSE_PUBLISH_SIDEBAR"}}function oo(){return{type:"TOGGLE_PUBLISH_SIDEBAR"}}const no=e=>(...t)=>({registry:s})=>{w()("`wp.data.dispatch( 'core/editor' )."+e+"`",{since:"5.3",alternative:"`wp.data.dispatch( 'core/block-editor' )."+e+"`",version:"6.2"}),s.dispatch(h.store)[e](...t)},io=no("resetBlocks"),ro=no("receiveBlocks"),ao=no("updateBlock"),lo=no("updateBlockAttributes"),co=no("selectBlock"),uo=no("startMultiSelect"),po=no("stopMultiSelect"),mo=no("multiSelect"),ho=no("clearSelectedBlock"),go=no("toggleSelection"),_o=no("replaceBlocks"),fo=no("replaceBlock"),bo=no("moveBlocksDown"),yo=no("moveBlocksUp"),xo=no("moveBlockToPosition"),vo=no("insertBlock"),So=no("insertBlocks"),wo=no("showInsertionPoint"),ko=no("hideInsertionPoint"),Co=no("setTemplateValidity"),Po=no("synchronizeTemplate"),jo=no("mergeBlocks"),Eo=no("removeBlocks"),To=no("removeBlock"),Bo=no("toggleBlockMode"),Io=no("startTyping"),No=no("stopTyping"),Do=no("enterFormattedText"),Ao=no("exitFormattedText"),Ro=no("insertDefaultBlock"),Mo=no("updateBlockListSettings"),Lo=window.wp.htmlEntities;function Oo(e){return!!e&&(e.source===D&&(Boolean(e?.plugin)||e?.has_theme_file))}const Fo=(0,L.jsx)(M.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,L.jsx)(M.Path,{d:"M19.5 4.5h-7V6h4.44l-5.97 5.97 1.06 1.06L18 7.06v4.44h1.5v-7Zm-13 1a2 2 0 0 0-2 2v10a2 2 0 0 0 2 2h10a2 2 0 0 0 2-2v-3H17v3a.5.5 0 0 1-.5.5h-10a.5.5 0 0 1-.5-.5v-10a.5.5 0 0 1 .5-.5h3V5.5h-3Z"})}),Vo={id:"view-post",label:(0,fs._x)("View","verb"),isPrimary:!0,icon:Fo,isEligible:e=>"trash"!==e.status,callback(e,{onActionPerformed:t}){const s=e[0];window.open(s?.link,"_blank"),t&&t(e)}},zo={id:"view-post-revisions",context:"list",label(e){var t;const s=null!==(t=e[0]._links?.["version-history"]?.[0]?.count)&&void 0!==t?t:0;return(0,fs.sprintf)((0,fs.__)("View revisions (%s)"),s)},isEligible(e){var t,s;if("trash"===e.status)return!1;const o=null!==(t=e?._links?.["predecessor-version"]?.[0]?.id)&&void 0!==t?t:null,n=null!==(s=e?._links?.["version-history"]?.[0]?.count)&&void 0!==s?s:0;return!!o&&n>1},callback(e,{onActionPerformed:t}){const s=e[0],o=(0,v.addQueryArgs)("revision.php",{revision:s?._links?.["predecessor-version"]?.[0]?.id});document.location.href=o,t&&t(e)}},Uo=window.wp.components,Ho=(0,L.jsx)(M.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,L.jsx)(M.Path,{d:"M16.7 7.1l-6.3 8.5-3.3-2.5-.9 1.2 4.5 3.4L17.9 8z"})});var Go=function(){return Go=Object.assign||function(e){for(var t,s=1,o=arguments.length;s<o;s++)for(var n in t=arguments[s])Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n]);return e},Go.apply(this,arguments)};Object.create;Object.create;"function"==typeof SuppressedError&&SuppressedError;function $o(e){return e.toLowerCase()}var Wo=[/([a-z0-9])([A-Z])/g,/([A-Z])([A-Z][a-z])/g],Zo=/[^A-Z0-9]+/gi;function Yo(e,t,s){return t instanceof RegExp?e.replace(t,s):t.reduce((function(e,t){return e.replace(t,s)}),e)}function Ko(e,t){return void 0===t&&(t={}),function(e,t){void 0===t&&(t={});for(var s=t.splitRegexp,o=void 0===s?Wo:s,n=t.stripRegexp,i=void 0===n?Zo:n,r=t.transform,a=void 0===r?$o:r,l=t.delimiter,c=void 0===l?" ":l,d=Yo(Yo(e,o,"$1\0$2"),i,"\0"),u=0,p=d.length;"\0"===d.charAt(u);)u++;for(;"\0"===d.charAt(p-1);)p--;return d.slice(u,p).split("\0").map(a).join(c)}(e,Go({delimiter:"."},t))}function qo(e,t){return void 0===t&&(t={}),Ko(e,Go({delimiter:"-"},t))}function Qo(e,t){return`fields-create-template-part-modal__area-option-${e}-${t}`}function Xo(e,t){return`fields-create-template-part-modal__area-option-description-${e}-${t}`}function Jo({modalTitle:e,...t}){const s=(0,c.useSelect)((e=>e(d.store).getPostType("wp_template_part")?.labels?.add_new_item),[]);return(0,L.jsx)(Uo.Modal,{title:e||s,onRequestClose:t.closeModal,overlayClassName:"fields-create-template-part-modal",focusOnMount:"firstContentElement",size:"medium",children:(0,L.jsx)(tn,{...t})})}const en=e=>"header"===e?O:"footer"===e?F:"sidebar"===e?V:z;function tn({defaultArea:e="uncategorized",blocks:t=[],confirmLabel:s=(0,fs.__)("Add"),closeModal:o,onCreate:n,onError:i,defaultTitle:r=""}){const{createErrorNotice:a}=(0,c.useDispatch)(_s.store),{saveEntityRecord:l}=(0,c.useDispatch)(d.store),m=null!==(h=(0,c.useSelect)((e=>e(d.store).getEntityRecords("postType","wp_template_part",{per_page:-1})),[]))&&void 0!==h?h:[];var h;const[g,_]=(0,u.useState)(r),[f,b]=(0,u.useState)(e),[x,v]=(0,u.useState)(!1),S=(0,p.useInstanceId)(Jo),w=(0,c.useSelect)((e=>e(d.store).getCurrentTheme()?.default_template_part_areas),[]);async function k(){if(g&&!x)try{v(!0);const e=((e,t)=>{const s=e.toLowerCase(),o=t.map((e=>e.title.rendered.toLowerCase()));if(!o.includes(s))return e;let n=2;for(;o.includes(`${s} ${n}`);)n++;return`${e} ${n}`})(g,m),s=(e=>qo(e).replace(/[^\w-]+/g,"")||"wp-custom-part")(e),o=await l("postType","wp_template_part",{slug:s,title:e,content:(0,y.serialize)(t),area:f},{throwOnError:!0});await n(o)}catch(e){const t=e instanceof Error&&"code"in e&&e.message&&"unknown_error"!==e.code?e.message:(0,fs.__)("An error occurred while creating the template part.");a(t,{type:"snackbar"}),i?.()}finally{v(!1)}}return(0,L.jsx)("form",{onSubmit:async e=>{e.preventDefault(),await k()},children:(0,L.jsxs)(Uo.__experimentalVStack,{spacing:"4",children:[(0,L.jsx)(Uo.TextControl,{__next40pxDefaultSize:!0,__nextHasNoMarginBottom:!0,label:(0,fs.__)("Name"),value:g,onChange:_,required:!0}),(0,L.jsxs)("fieldset",{children:[(0,L.jsx)(Uo.BaseControl.VisualLabel,{as:"legend",children:(0,fs.__)("Area")}),(0,L.jsx)("div",{className:"fields-create-template-part-modal__area-radio-group",children:(null!=w?w:[]).map((e=>{const t=en(e.icon);return(0,L.jsxs)("div",{className:"fields-create-template-part-modal__area-radio-wrapper",children:[(0,L.jsx)("input",{type:"radio",id:Qo(e.area,S),name:`fields-create-template-part-modal__area-${S}`,value:e.area,checked:f===e.area,onChange:()=>{b(e.area)},"aria-describedby":Xo(e.area,S)}),(0,L.jsx)(Uo.Icon,{icon:t,className:"fields-create-template-part-modal__area-radio-icon"}),(0,L.jsx)("label",{htmlFor:Qo(e.area,S),className:"fields-create-template-part-modal__area-radio-label",children:e.label}),(0,L.jsx)(Uo.Icon,{icon:Ho,className:"fields-create-template-part-modal__area-radio-checkmark"}),(0,L.jsx)("p",{className:"fields-create-template-part-modal__area-radio-description",id:Xo(e.area,S),children:e.description})]},e.area)}))})]}),(0,L.jsxs)(Uo.__experimentalHStack,{justify:"right",children:[(0,L.jsx)(Uo.Button,{__next40pxDefaultSize:!0,variant:"tertiary",onClick:()=>{o()},children:(0,fs.__)("Cancel")}),(0,L.jsx)(Uo.Button,{__next40pxDefaultSize:!0,variant:"primary",type:"submit","aria-disabled":!g||x,isBusy:x,children:s})]})]})})}function sn(e){return"wp_template"===e.type||"wp_template_part"===e.type}function on(e){return"string"==typeof e.title?(0,Lo.decodeEntities)(e.title):e.title&&"rendered"in e.title?(0,Lo.decodeEntities)(e.title.rendered):e.title&&"raw"in e.title?(0,Lo.decodeEntities)(e.title.raw):""}function nn(e){return!!e&&([e.source,e.source].includes("custom")&&!Boolean("wp_template"===e.type&&e?.plugin)&&!e.has_theme_file)}const rn={id:"duplicate-template-part",label:(0,fs._x)("Duplicate","action label"),isEligible:e=>"wp_template_part"===e.type,modalHeader:(0,fs._x)("Duplicate template part","action label"),RenderModal:({items:e,closeModal:t})=>{const[s]=e,o=(0,u.useMemo)((()=>{var e;return null!==(e=s.blocks)&&void 0!==e?e:(0,y.parse)("string"==typeof s.content?s.content:s.content.raw,{__unstableSkipMigrationLogs:!0})}),[s.content,s.blocks]),{createSuccessNotice:n}=(0,c.useDispatch)(_s.store);return(0,L.jsx)(tn,{blocks:o,defaultArea:s.area,defaultTitle:(0,fs.sprintf)((0,fs._x)("%s (Copy)","template part"),on(s)),onCreate:function(e){n((0,fs.sprintf)((0,fs._x)('"%s" duplicated.',"template part"),on(e)),{type:"snackbar",id:"edit-site-patterns-success"}),t?.()},onError:t,confirmLabel:(0,fs._x)("Duplicate","action label"),closeModal:null!=t?t:()=>{}})}},an=rn,ln=window.wp.patterns,{lock:cn,unlock:dn}=(0,H.__dangerousOptInToUnstableAPIsOnlyForCoreModules)("I acknowledge private features are not for use in themes or plugins and doing so will break in the next version of WordPress.","@wordpress/fields"),{CreatePatternModalContents:un,useDuplicatePatternProps:pn}=dn(ln.privateApis),mn={id:"duplicate-pattern",label:(0,fs._x)("Duplicate","action label"),isEligible:e=>"wp_template_part"!==e.type,modalHeader:(0,fs._x)("Duplicate pattern","action label"),RenderModal:({items:e,closeModal:t})=>{const[s]=e,o=pn({pattern:s,onSuccess:()=>t?.()});return(0,L.jsx)(un,{onClose:t,confirmLabel:(0,fs._x)("Duplicate","action label"),...o})}},hn=mn,{PATTERN_TYPES:gn}=dn(ln.privateApis),_n={id:"rename-post",label:(0,fs.__)("Rename"),isEligible:e=>"trash"!==e.status&&(["wp_template","wp_template_part",...Object.values(gn)].includes(e.type)?function(e){return"wp_template"===e.type}(e)?nn(e)&&e.is_custom&&e.permissions?.update:function(e){return"wp_template_part"===e.type}(e)?"custom"===e.source&&!e?.has_theme_file&&e.permissions?.update:e.type===gn.user&&e.permissions?.update:e.permissions?.update),RenderModal:({items:e,closeModal:t,onActionPerformed:s})=>{const[o]=e,[n,i]=(0,u.useState)((()=>on(o))),{editEntityRecord:r,saveEditedEntityRecord:a}=(0,c.useDispatch)(d.store),{createSuccessNotice:l,createErrorNotice:p}=(0,c.useDispatch)(_s.store);return(0,L.jsx)("form",{onSubmit:async function(c){c.preventDefault();try{await r("postType",o.type,o.id,{title:n}),i(""),t?.(),await a("postType",o.type,o.id,{throwOnError:!0}),l((0,fs.__)("Name updated"),{type:"snackbar"}),s?.(e)}catch(e){const t=e,s=t.message&&"unknown_error"!==t.code?t.message:(0,fs.__)("An error occurred while updating the name");p(s,{type:"snackbar"})}},children:(0,L.jsxs)(Uo.__experimentalVStack,{spacing:"5",children:[(0,L.jsx)(Uo.TextControl,{__nextHasNoMarginBottom:!0,__next40pxDefaultSize:!0,label:(0,fs.__)("Name"),value:n,onChange:i,required:!0}),(0,L.jsxs)(Uo.__experimentalHStack,{justify:"right",children:[(0,L.jsx)(Uo.Button,{__next40pxDefaultSize:!0,variant:"tertiary",onClick:()=>{t?.()},children:(0,fs.__)("Cancel")}),(0,L.jsx)(Uo.Button,{__next40pxDefaultSize:!0,variant:"primary",type:"submit",children:(0,fs.__)("Save")})]})]})})}},fn=_n;const bn={sort:function(e,t,s){return"asc"===s?e-t:t-e},isValid:function(e,t){if(""===e)return!1;if(!Number.isInteger(Number(e)))return!1;if(t?.elements){const s=t?.elements.map((e=>e.value));if(!s.includes(Number(e)))return!1}return!0},Edit:"integer"};const yn={sort:function(e,t,s){return"asc"===s?e.localeCompare(t):t.localeCompare(e)},isValid:function(e,t){if(t?.elements){const s=t?.elements?.map((e=>e.value));if(!s.includes(e))return!1}return!0},Edit:"text"};const xn={sort:function(e,t,s){const o=new Date(e).getTime(),n=new Date(t).getTime();return"asc"===s?o-n:n-o},isValid:function(e,t){if(t?.elements){const s=t?.elements.map((e=>e.value));if(!s.includes(e))return!1}return!0},Edit:"datetime"};const vn={datetime:function({data:e,field:t,onChange:s,hideLabelFromVision:o}){const{id:n,label:i}=t,r=t.getValue({item:e}),a=(0,u.useCallback)((e=>s({[n]:e})),[n,s]);return(0,L.jsxs)("fieldset",{className:"dataviews-controls__datetime",children:[!o&&(0,L.jsx)(Uo.BaseControl.VisualLabel,{as:"legend",children:i}),o&&(0,L.jsx)(Uo.VisuallyHidden,{as:"legend",children:i}),(0,L.jsx)(Uo.TimePicker,{currentTime:r,onChange:a,hideLabelFromVision:!0})]})},integer:function({data:e,field:t,onChange:s,hideLabelFromVision:o}){var n;const{id:i,label:r,description:a}=t,l=null!==(n=t.getValue({item:e}))&&void 0!==n?n:"",c=(0,u.useCallback)((e=>s({[i]:Number(e)})),[i,s]);return(0,L.jsx)(Uo.__experimentalNumberControl,{label:r,help:a,value:l,onChange:c,__next40pxDefaultSize:!0,hideLabelFromVision:o})},radio:function({data:e,field:t,onChange:s,hideLabelFromVision:o}){const{id:n,label:i}=t,r=t.getValue({item:e}),a=(0,u.useCallback)((e=>s({[n]:e})),[n,s]);return t.elements?(0,L.jsx)(Uo.RadioControl,{label:i,onChange:a,options:t.elements,selected:r,hideLabelFromVision:o}):null},select:function({data:e,field:t,onChange:s,hideLabelFromVision:o}){var n,i;const{id:r,label:a}=t,l=null!==(n=t.getValue({item:e}))&&void 0!==n?n:"",c=(0,u.useCallback)((e=>s({[r]:e})),[r,s]),d=[{label:(0,fs.__)("Select item"),value:""},...null!==(i=t?.elements)&&void 0!==i?i:[]];return(0,L.jsx)(Uo.SelectControl,{label:a,value:l,options:d,onChange:c,__next40pxDefaultSize:!0,__nextHasNoMarginBottom:!0,hideLabelFromVision:o})},text:function({data:e,field:t,onChange:s,hideLabelFromVision:o}){const{id:n,label:i,placeholder:r}=t,a=t.getValue({item:e}),l=(0,u.useCallback)((e=>s({[n]:e})),[n,s]);return(0,L.jsx)(Uo.TextControl,{label:i,placeholder:r,value:null!=a?a:"",onChange:l,__next40pxDefaultSize:!0,__nextHasNoMarginBottom:!0,hideLabelFromVision:o})}};function Sn(e){if(Object.keys(vn).includes(e))return vn[e];throw"Control "+e+" not found"}function wn(e){return e.map((e=>{var t,s,o,n;const i="integer"===(r=e.type)?bn:"text"===r?yn:"datetime"===r?xn:{sort:(e,t,s)=>"number"==typeof e&&"number"==typeof t?"asc"===s?e-t:t-e:"asc"===s?e.localeCompare(t):t.localeCompare(e),isValid:(e,t)=>{if(t?.elements){const s=t?.elements?.map((e=>e.value));if(!s.includes(e))return!1}return!0},Edit:()=>null};var r;const a=e.getValue||(l=e.id,({item:e})=>{const t=l.split(".");let s=e;for(const e of t)s=s.hasOwnProperty(e)?s[e]:void 0;return s});var l;const c=null!==(t=e.sort)&&void 0!==t?t:function(e,t,s){return i.sort(a({item:e}),a({item:t}),s)},d=null!==(s=e.isValid)&&void 0!==s?s:function(e,t){return i.isValid(a({item:e}),t)},u=function(e,t){return"function"==typeof e.Edit?e.Edit:"string"==typeof e.Edit?Sn(e.Edit):e.elements?Sn("select"):"string"==typeof t.Edit?Sn(t.Edit):t.Edit}(e,i),p=e.render||(e.elements?({item:t})=>{const s=a({item:t});return e?.elements?.find((e=>e.value===s))?.label||a({item:t})}:a);return{...e,label:e.label||e.id,header:e.header||e.label||e.id,getValue:a,render:p,sort:c,isValid:d,Edit:u,enableHiding:null===(o=e.enableHiding)||void 0===o||o,enableSorting:null===(n=e.enableSorting)||void 0===n||n}}))}function kn(e,t,s){return wn(t.filter((({id:e})=>!!s.fields?.includes(e)))).every((t=>t.isValid(e,{elements:t.elements})))}const Cn=(0,u.createContext)({fields:[]});function Pn({fields:e,children:t}){return(0,L.jsx)(Cn.Provider,{value:{fields:e},children:t})}const jn=Cn;function En(e){return void 0!==e.children}function Tn({title:e}){return(0,L.jsx)(Uo.__experimentalVStack,{className:"dataforms-layouts-regular__header",spacing:4,children:(0,L.jsxs)(Uo.__experimentalHStack,{alignment:"center",children:[(0,L.jsx)(Uo.__experimentalHeading,{level:2,size:13,children:e}),(0,L.jsx)(Uo.__experimentalSpacer,{})]})})}const Bn=(0,L.jsx)(M.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,L.jsx)(M.Path,{d:"M12 13.06l3.712 3.713 1.061-1.06L13.061 12l3.712-3.712-1.06-1.06L12 10.938 8.288 7.227l-1.061 1.06L10.939 12l-3.712 3.712 1.06 1.061L12 13.061z"})});function In({title:e,onClose:t}){return(0,L.jsx)(Uo.__experimentalVStack,{className:"dataforms-layouts-panel__dropdown-header",spacing:4,children:(0,L.jsxs)(Uo.__experimentalHStack,{alignment:"center",children:[e&&(0,L.jsx)(Uo.__experimentalHeading,{level:2,size:13,children:e}),(0,L.jsx)(Uo.__experimentalSpacer,{}),t&&(0,L.jsx)(Uo.Button,{label:(0,fs.__)("Close"),icon:Bn,onClick:t,size:"small"})]})})}function Nn({fieldDefinition:e,popoverAnchor:t,labelPosition:s="side",data:o,onChange:n,field:i}){const r=En(i)?i.label:e?.label,a=(0,u.useMemo)((()=>En(i)?{type:"regular",fields:i.children.map((e=>"string"==typeof e?{id:e}:e))}:{type:"regular",fields:[{id:i.id}]}),[i]),l=(0,u.useMemo)((()=>({anchor:t,placement:"left-start",offset:36,shift:!0})),[t]);return(0,L.jsx)(Uo.Dropdown,{contentClassName:"dataforms-layouts-panel__field-dropdown",popoverProps:l,focusOnMount:!0,toggleProps:{size:"compact",variant:"tertiary",tooltipPosition:"middle left"},renderToggle:({isOpen:t,onToggle:n})=>(0,L.jsx)(Uo.Button,{className:"dataforms-layouts-panel__field-control",size:"compact",variant:["none","top"].includes(s)?"link":"tertiary","aria-expanded":t,"aria-label":(0,fs.sprintf)((0,fs._x)("Edit %s","field"),r),onClick:n,children:(0,L.jsx)(e.render,{item:o})}),renderContent:({onClose:e})=>(0,L.jsxs)(L.Fragment,{children:[(0,L.jsx)(In,{title:r,onClose:e}),(0,L.jsx)(An,{data:o,form:a,onChange:n,children:(e,t)=>{var s;return(0,L.jsx)(e,{data:o,field:t,onChange:n,hideLabelFromVision:(null!==(s=a?.fields)&&void 0!==s?s:[]).length<2},t.id)}})]})})}const Dn=[{type:"regular",component:function({data:e,field:t,onChange:s,hideLabelFromVision:o}){var n;const{fields:i}=(0,u.useContext)(jn),r=(0,u.useMemo)((()=>En(t)?{fields:t.children.map((e=>"string"==typeof e?{id:e}:e)),type:"regular"}:{type:"regular",fields:[]}),[t]);if(En(t))return(0,L.jsxs)(L.Fragment,{children:[!o&&t.label&&(0,L.jsx)(Tn,{title:t.label}),(0,L.jsx)(An,{data:e,form:r,onChange:s})]});const a=null!==(n=t.labelPosition)&&void 0!==n?n:"top",l=i.find((e=>e.id===t.id));return l?"side"===a?(0,L.jsxs)(Uo.__experimentalHStack,{className:"dataforms-layouts-regular__field",children:[(0,L.jsx)("div",{className:"dataforms-layouts-regular__field-label",children:l.label}),(0,L.jsx)("div",{className:"dataforms-layouts-regular__field-control",children:(0,L.jsx)(l.Edit,{data:e,field:l,onChange:s,hideLabelFromVision:!0},l.id)})]}):(0,L.jsx)("div",{className:"dataforms-layouts-regular__field",children:(0,L.jsx)(l.Edit,{data:e,field:l,onChange:s,hideLabelFromVision:"none"===a||o})}):null}},{type:"panel",component:function({data:e,field:t,onChange:s}){var o;const{fields:n}=(0,u.useContext)(jn),i=n.find((e=>{if(En(t)){const s=t.children.filter((e=>"string"==typeof e||!En(e))),o="string"==typeof s[0]?s[0]:s[0].id;return e.id===o}return e.id===t.id})),r=null!==(o=t.labelPosition)&&void 0!==o?o:"side",[a,l]=(0,u.useState)(null);if(!i)return null;const c=En(t)?t.label:i?.label;return"top"===r?(0,L.jsxs)(Uo.__experimentalVStack,{className:"dataforms-layouts-panel__field",spacing:0,children:[(0,L.jsx)("div",{className:"dataforms-layouts-panel__field-label",style:{paddingBottom:0},children:c}),(0,L.jsx)("div",{className:"dataforms-layouts-panel__field-control",children:(0,L.jsx)(Nn,{field:t,popoverAnchor:a,fieldDefinition:i,data:e,onChange:s,labelPosition:r})})]}):"none"===r?(0,L.jsx)("div",{className:"dataforms-layouts-panel__field",children:(0,L.jsx)(Nn,{field:t,popoverAnchor:a,fieldDefinition:i,data:e,onChange:s,labelPosition:r})}):(0,L.jsxs)(Uo.__experimentalHStack,{ref:l,className:"dataforms-layouts-panel__field",children:[(0,L.jsx)("div",{className:"dataforms-layouts-panel__field-label",children:c}),(0,L.jsx)("div",{className:"dataforms-layouts-panel__field-control",children:(0,L.jsx)(Nn,{field:t,popoverAnchor:a,fieldDefinition:i,data:e,onChange:s,labelPosition:r})})]})}}];function An({data:e,form:t,onChange:s,children:o}){const{fields:n}=(0,u.useContext)(jn);const i=(0,u.useMemo)((()=>function(e){var t,s,o;let n="regular";["regular","panel"].includes(null!==(t=e.type)&&void 0!==t?t:"")&&(n=e.type);const i=null!==(s=e.labelPosition)&&void 0!==s?s:"regular"===n?"top":"side";return(null!==(o=e.fields)&&void 0!==o?o:[]).map((e=>{var t,s;if("string"==typeof e)return{id:e,layout:n,labelPosition:i};const o=null!==(t=e.layout)&&void 0!==t?t:n,r=null!==(s=e.labelPosition)&&void 0!==s?s:"regular"===o?"top":"side";return{...e,layout:o,labelPosition:r}}))}(t)),[t]);return(0,L.jsx)(Uo.__experimentalVStack,{spacing:2,children:i.map((t=>{const i=(r=t.layout,Dn.find((e=>e.type===r)))?.component;var r;if(!i)return null;const a=En(t)?void 0:function(e){const t="string"==typeof e?e:e.id;return n.find((e=>e.id===t))}(t);return a&&a.isVisible&&!a.isVisible(e)?null:o?o(i,t):(0,L.jsx)(i,{data:e,field:t,onChange:s},t.id)}))})}function Rn({data:e,form:t,fields:s,onChange:o}){const n=(0,u.useMemo)((()=>wn(s)),[s]);return t.fields?(0,L.jsx)(Pn,{fields:n,children:(0,L.jsx)(An,{data:e,form:t,onChange:o})}):null}const Mn=[{id:"menu_order",type:"integer",label:(0,fs.__)("Order"),description:(0,fs.__)("Determines the order of pages.")}],Ln={fields:["menu_order"]};const On={id:"order-pages",label:(0,fs.__)("Order"),isEligible:({status:e})=>"trash"!==e,RenderModal:function({items:e,closeModal:t,onActionPerformed:s}){const[o,n]=(0,u.useState)(e[0]),i=o.menu_order,{editEntityRecord:r,saveEditedEntityRecord:a}=(0,c.useDispatch)(d.store),{createSuccessNotice:l,createErrorNotice:p}=(0,c.useDispatch)(_s.store),m=!kn(o,Mn,Ln);return(0,L.jsx)("form",{onSubmit:async function(n){if(n.preventDefault(),kn(o,Mn,Ln))try{await r("postType",o.type,o.id,{menu_order:i}),t?.(),await a("postType",o.type,o.id,{throwOnError:!0}),l((0,fs.__)("Order updated."),{type:"snackbar"}),s?.(e)}catch(e){const t=e,s=t.message&&"unknown_error"!==t.code?t.message:(0,fs.__)("An error occurred while updating the order");p(s,{type:"snackbar"})}},children:(0,L.jsxs)(Uo.__experimentalVStack,{spacing:"5",children:[(0,L.jsx)("div",{children:(0,fs.__)("Determines the order of pages. Pages with the same order value are sorted alphabetically. Negative order values are supported.")}),(0,L.jsx)(Rn,{data:o,fields:Mn,form:Ln,onChange:e=>n({...o,...e})}),(0,L.jsxs)(Uo.__experimentalHStack,{justify:"right",children:[(0,L.jsx)(Uo.Button,{__next40pxDefaultSize:!0,variant:"tertiary",onClick:()=>{t?.()},children:(0,fs.__)("Cancel")}),(0,L.jsx)(Uo.Button,{__next40pxDefaultSize:!0,variant:"primary",type:"submit",accessibleWhenDisabled:!0,disabled:m,children:(0,fs.__)("Save")})]})]})})}},Fn=On;"stream"in Blob.prototype||Object.defineProperty(Blob.prototype,"stream",{value(){return new Response(this).body}}),"setBigUint64"in DataView.prototype||Object.defineProperty(DataView.prototype,"setBigUint64",{value(e,t,s){const o=Number(0xffffffffn&t),n=Number(t>>32n);this.setUint32(e+(s?0:4),o,s),this.setUint32(e+(s?4:0),n,s)}});var Vn=e=>new DataView(new ArrayBuffer(e)),zn=e=>new Uint8Array(e.buffer||e),Un=e=>(new TextEncoder).encode(String(e)),Hn=e=>Math.min(4294967295,Number(e)),Gn=e=>Math.min(65535,Number(e));function $n(e,t,s){void 0===t||t instanceof Date||(t=new Date(t));const o=void 0!==e;if(s||(s=o?436:509),e instanceof File)return{isFile:o,t:t||new Date(e.lastModified),bytes:e.stream(),mode:s};if(e instanceof Response)return{isFile:o,t:t||new Date(e.headers.get("Last-Modified")||Date.now()),bytes:e.body,mode:s};if(void 0===t)t=new Date;else if(isNaN(t))throw new Error("Invalid modification date.");if(!o)return{isFile:o,t,mode:s};if("string"==typeof e)return{isFile:o,t,bytes:Un(e),mode:s};if(e instanceof Blob)return{isFile:o,t,bytes:e.stream(),mode:s};if(e instanceof Uint8Array||e instanceof ReadableStream)return{isFile:o,t,bytes:e,mode:s};if(e instanceof ArrayBuffer||ArrayBuffer.isView(e))return{isFile:o,t,bytes:zn(e),mode:s};if(Symbol.asyncIterator in e)return{isFile:o,t,bytes:Wn(e[Symbol.asyncIterator]()),mode:s};throw new TypeError("Unsupported input format.")}function Wn(e,t=e){return new ReadableStream({async pull(t){let s=0;for(;t.desiredSize>s;){const o=await e.next();if(!o.value){t.close();break}{const e=Zn(o.value);t.enqueue(e),s+=e.byteLength}}},cancel(e){t.throw?.(e)}})}function Zn(e){return"string"==typeof e?Un(e):e instanceof Uint8Array?e:zn(e)}function Yn(e,t,s){let[o,n]=function(e){return e?e instanceof Uint8Array?[e,1]:ArrayBuffer.isView(e)||e instanceof ArrayBuffer?[zn(e),1]:[Un(e),0]:[void 0,0]}(t);if(e instanceof File)return{i:qn(o||Un(e.name)),o:BigInt(e.size),u:n};if(e instanceof Response){const t=e.headers.get("content-disposition"),i=t&&t.match(/;\s*filename\*?\s*=\s*(?:UTF-\d+''|)["']?([^;"'\r\n]*)["']?(?:;|$)/i),r=i&&i[1]||e.url&&new URL(e.url).pathname.split("/").findLast(Boolean),a=r&&decodeURIComponent(r),l=s||+e.headers.get("content-length");return{i:qn(o||Un(a)),o:BigInt(l),u:n}}return o=qn(o,void 0!==e||void 0!==s),"string"==typeof e?{i:o,o:BigInt(Un(e).length),u:n}:e instanceof Blob?{i:o,o:BigInt(e.size),u:n}:e instanceof ArrayBuffer||ArrayBuffer.isView(e)?{i:o,o:BigInt(e.byteLength),u:n}:{i:o,o:Kn(e,s),u:n}}function Kn(e,t){return t>-1?BigInt(t):e?void 0:0n}function qn(e,t=1){if(!e||e.every((e=>47===e)))throw new Error("The file must have a name.");if(t)for(;47===e[e.length-1];)e=e.subarray(0,-1);else 47!==e[e.length-1]&&(e=new Uint8Array([...e,47]));return e}var Qn=new Uint32Array(256);for(let e=0;e<256;++e){let t=e;for(let e=0;e<8;++e)t=t>>>1^(1&t&&3988292384);Qn[e]=t}function Xn(e,t=0){t=~t;for(var s=0,o=e.length;s<o;s++)t=t>>>8^Qn[255&t^e[s]];return~t>>>0}function Jn(e,t,s=0){const o=e.getSeconds()>>1|e.getMinutes()<<5|e.getHours()<<11,n=e.getDate()|e.getMonth()+1<<5|e.getFullYear()-1980<<9;t.setUint16(s,o,1),t.setUint16(s+2,n,1)}function ei({i:e,u:t},s){return 8*(!t||(s??function(e){try{ti.decode(e)}catch{return 0}return 1}(e)))}var ti=new TextDecoder("utf8",{fatal:1});function si(e,t=0){const s=Vn(30);return s.setUint32(0,1347093252),s.setUint32(4,754976768|t),Jn(e.t,s,10),s.setUint16(26,e.i.length,1),zn(s)}async function*oi(e){let{bytes:t}=e;if("then"in t&&(t=await t),t instanceof Uint8Array)yield t,e.l=Xn(t,0),e.o=BigInt(t.length);else{e.o=0n;const s=t.getReader();for(;;){const{value:t,done:o}=await s.read();if(o)break;e.l=Xn(t,e.l),e.o+=BigInt(t.length),yield t}}}function ni(e,t){const s=Vn(16+(t?8:0));return s.setUint32(0,1347094280),s.setUint32(4,e.isFile?e.l:0,1),t?(s.setBigUint64(8,e.o,1),s.setBigUint64(16,e.o,1)):(s.setUint32(8,Hn(e.o),1),s.setUint32(12,Hn(e.o),1)),zn(s)}function ii(e,t,s=0,o=0){const n=Vn(46);return n.setUint32(0,1347092738),n.setUint32(4,755182848),n.setUint16(8,2048|s),Jn(e.t,n,12),n.setUint32(16,e.isFile?e.l:0,1),n.setUint32(20,Hn(e.o),1),n.setUint32(24,Hn(e.o),1),n.setUint16(28,e.i.length,1),n.setUint16(30,o,1),n.setUint16(40,e.mode|(e.isFile?32768:16384),1),n.setUint32(42,Hn(t),1),zn(n)}function ri(e,t,s){const o=Vn(s);return o.setUint16(0,1,1),o.setUint16(2,s-4,1),16&s&&(o.setBigUint64(4,e.o,1),o.setBigUint64(12,e.o,1)),o.setBigUint64(s-8,t,1),zn(o)}function ai(e){return e instanceof File||e instanceof Response?[[e],[e]]:[[e.input,e.name,e.size],[e.input,e.lastModified,e.mode]]}function li(e,t={}){const s={"Content-Type":"application/zip","Content-Disposition":"attachment"};return("bigint"==typeof t.length||Number.isInteger(t.length))&&t.length>0&&(s["Content-Length"]=String(t.length)),t.metadata&&(s["Content-Length"]=String((e=>function(e){let t=BigInt(22),s=0n,o=0;for(const n of e){if(!n.i)throw new Error("Every file must have a non-empty name.");if(void 0===n.o)throw new Error(`Missing size for file "${(new TextDecoder).decode(n.i)}".`);const e=n.o>=0xffffffffn,i=s>=0xffffffffn;s+=BigInt(46+n.i.length+(e&&8))+n.o,t+=BigInt(n.i.length+46+(12*i|28*e)),o||(o=e)}return(o||s>=0xffffffffn)&&(t+=BigInt(76)),t+s}(function*(e){for(const t of e)yield Yn(...ai(t)[0])}(e)))(t.metadata))),new Response(ci(e,t),{headers:s})}function ci(e,t={}){const s=function(e){const t=e[Symbol.iterator in e?Symbol.iterator:Symbol.asyncIterator]();return{async next(){const e=await t.next();if(e.done)return e;const[s,o]=ai(e.value);return{done:0,value:Object.assign($n(...o),Yn(...s))}},throw:t.throw?.bind(t),[Symbol.asyncIterator](){return this}}}(e);return Wn(async function*(e,t){const s=[];let o=0n,n=0n,i=0;for await(const r of e){const e=ei(r,t.buffersAreUTF8);yield si(r,e),yield new Uint8Array(r.i),r.isFile&&(yield*oi(r));const a=r.o>=0xffffffffn,l=12*(o>=0xffffffffn)|28*a;yield ni(r,a),s.push(ii(r,o,e,l)),s.push(r.i),l&&s.push(ri(r,o,l)),a&&(o+=8n),n++,o+=BigInt(46+r.i.length)+r.o,i||(i=a)}let r=0n;for(const e of s)yield e,r+=BigInt(e.length);if(i||o>=0xffffffffn){const e=Vn(76);e.setUint32(0,1347094022),e.setBigUint64(4,BigInt(44),1),e.setUint32(12,755182848),e.setBigUint64(24,n,1),e.setBigUint64(32,n,1),e.setBigUint64(40,r,1),e.setBigUint64(48,o,1),e.setUint32(56,1347094023),e.setBigUint64(64,o+r,1),e.setUint32(72,1,1),yield zn(e)}const a=Vn(22);a.setUint32(0,1347093766),a.setUint16(8,Gn(n),1),a.setUint16(10,Gn(n),1),a.setUint32(12,Hn(r),1),a.setUint32(16,Hn(o),1),yield zn(a)}(s,t),s)}const di=window.wp.blob,ui=(0,L.jsx)(M.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,L.jsx)(M.Path,{d:"M18 11.3l-1-1.1-4 4V3h-1.5v11.3L7 10.2l-1 1.1 6.2 5.8 5.8-5.8zm.5 3.7v3.5h-13V15H4v5h16v-5h-1.5z"})});function pi(e){return JSON.stringify({__file:e.type,title:on(e),content:"string"==typeof e.content?e.content:e.content?.raw,syncStatus:e.wp_pattern_sync_status},null,2)}const mi={id:"export-pattern",label:(0,fs.__)("Export as JSON"),icon:ui,supportsBulk:!0,isEligible:e=>"wp_block"===e.type,callback:async e=>{if(1===e.length)return(0,di.downloadBlob)(`${qo(on(e[0])||e[0].slug)}.json`,pi(e[0]),"application/json");const t={},s=e.map((e=>{const s=qo(on(e)||e.slug);return t[s]=(t[s]||0)+1,{name:s+(t[s]>1?"-"+(t[s]-1):"")+".json",lastModified:new Date,input:pi(e)}}));return(0,di.downloadBlob)((0,fs.__)("patterns-export")+".zip",await li(s).blob(),"application/zip")}},hi=(0,L.jsx)(M.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,L.jsx)(M.Path,{d:"M5.5 12h1.75l-2.5 3-2.5-3H4a8 8 0 113.134 6.35l.907-1.194A6.5 6.5 0 105.5 12zm9.53 1.97l-2.28-2.28V8.5a.75.75 0 00-1.5 0V12a.747.747 0 00.218.529l1.282-.84-1.28.842 2.5 2.5a.75.75 0 101.06-1.061z"})}),gi={id:"restore",label:(0,fs.__)("Restore"),isPrimary:!0,icon:hi,supportsBulk:!0,isEligible:e=>!sn(e)&&"wp_block"!==e.type&&"trash"===e.status&&e.permissions?.update,async callback(e,{registry:t,onActionPerformed:s}){const{createSuccessNotice:o,createErrorNotice:n}=t.dispatch(_s.store),{editEntityRecord:i,saveEditedEntityRecord:r}=t.dispatch(d.store);await Promise.allSettled(e.map((e=>i("postType",e.type,e.id,{status:"draft"}))));const a=await Promise.allSettled(e.map((e=>r("postType",e.type,e.id,{throwOnError:!0}))));if(a.every((({status:e})=>"fulfilled"===e))){let t;t=1===e.length?(0,fs.sprintf)((0,fs.__)('"%s" has been restored.'),on(e[0])):"page"===e[0].type?(0,fs.sprintf)((0,fs.__)("%d pages have been restored."),e.length):(0,fs.sprintf)((0,fs.__)("%d posts have been restored."),e.length),o(t,{type:"snackbar",id:"restore-post-action"}),s&&s(e)}else{let e;if(1===a.length){const t=a[0];e=t.reason?.message?t.reason.message:(0,fs.__)("An error occurred while restoring the post.")}else{const t=new Set,s=a.filter((({status:e})=>"rejected"===e));for(const e of s){const s=e;s.reason?.message&&t.add(s.reason.message)}e=0===t.size?(0,fs.__)("An error occurred while restoring the posts."):1===t.size?(0,fs.sprintf)((0,fs.__)("An error occurred while restoring the posts: %s"),[...t][0]):(0,fs.sprintf)((0,fs.__)("Some errors occurred while restoring the posts: %s"),[...t].join(","))}n(e,{type:"snackbar"})}}},_i=async(e,{allowUndo:t=!0}={})=>{const s="edit-site-template-reverted";var o;if((0,c.dispatch)(_s.store).removeNotice(s),(o=e)&&"custom"===o.source&&(Boolean(o?.plugin)||o?.has_theme_file))try{const o=(0,c.select)(d.store).getEntityConfig("postType",e.type);if(!o)return void(0,c.dispatch)(_s.store).createErrorNotice((0,fs.__)("The editor has encountered an unexpected error. Please reload."),{type:"snackbar"});const n=(0,v.addQueryArgs)(`${o.baseURL}/${e.id}`,{context:"edit",source:e.origin}),i=await gs()({path:n});if(!i)return void(0,c.dispatch)(_s.store).createErrorNotice((0,fs.__)("The editor has encountered an unexpected error. Please reload."),{type:"snackbar"});const r=({blocks:e=[]})=>(0,y.__unstableSerializeAndClean)(e),a=(0,c.select)(d.store).getEditedEntityRecord("postType",e.type,e.id);(0,c.dispatch)(d.store).editEntityRecord("postType",e.type,e.id,{content:r,blocks:a.blocks,source:"custom"},{undoIgnore:!0});const l=(0,y.parse)(i?.content?.raw);if((0,c.dispatch)(d.store).editEntityRecord("postType",e.type,i.id,{content:r,blocks:l,source:"theme"}),t){const t=()=>{(0,c.dispatch)(d.store).editEntityRecord("postType",e.type,a.id,{content:r,blocks:a.blocks,source:"custom"})};(0,c.dispatch)(_s.store).createSuccessNotice((0,fs.__)("Template reset."),{type:"snackbar",id:s,actions:[{label:(0,fs.__)("Undo"),onClick:t}]})}}catch(e){const t=e.message&&"unknown_error"!==e.code?e.message:(0,fs.__)("Template revert failed. Please reload.");(0,c.dispatch)(_s.store).createErrorNotice(t,{type:"snackbar"})}else(0,c.dispatch)(_s.store).createErrorNotice((0,fs.__)("This template is not revertable."),{type:"snackbar"})},fi={id:"reset-post",label:(0,fs.__)("Reset"),isEligible:e=>sn(e)&&"custom"===e?.source&&(Boolean("wp_template"===e.type&&e?.plugin)||e?.has_theme_file),icon:hi,supportsBulk:!0,hideModalHeader:!0,RenderModal:({items:e,closeModal:t,onActionPerformed:s})=>{const[o,n]=(0,u.useState)(!1),{saveEditedEntityRecord:i}=(0,c.useDispatch)(d.store),{createSuccessNotice:r,createErrorNotice:a}=(0,c.useDispatch)(_s.store);return(0,L.jsxs)(Uo.__experimentalVStack,{spacing:"5",children:[(0,L.jsx)(Uo.__experimentalText,{children:(0,fs.__)("Reset to default and clear all customizations?")}),(0,L.jsxs)(Uo.__experimentalHStack,{justify:"right",children:[(0,L.jsx)(Uo.Button,{__next40pxDefaultSize:!0,variant:"tertiary",onClick:t,disabled:o,accessibleWhenDisabled:!0,children:(0,fs.__)("Cancel")}),(0,L.jsx)(Uo.Button,{__next40pxDefaultSize:!0,variant:"primary",onClick:async()=>{n(!0),await(async()=>{try{for(const t of e)await _i(t,{allowUndo:!1}),await i("postType",t.type,t.id);r(e.length>1?(0,fs.sprintf)((0,fs.__)("%s items reset."),e.length):(0,fs.sprintf)((0,fs.__)('"%s" reset.'),on(e[0])),{type:"snackbar",id:"revert-template-action"})}catch(t){let s;s="wp_template"===e[0].type?1===e.length?(0,fs.__)("An error occurred while reverting the template."):(0,fs.__)("An error occurred while reverting the templates."):1===e.length?(0,fs.__)("An error occurred while reverting the template part."):(0,fs.__)("An error occurred while reverting the template parts.");const o=t,n=o.message&&"unknown_error"!==o.code?o.message:s;a(n,{type:"snackbar"})}})(),s?.(e),n(!1),t?.()},isBusy:o,disabled:o,accessibleWhenDisabled:!0,children:(0,fs.__)("Reset")})]})]})}},bi=fi,yi=(0,L.jsx)(M.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,L.jsx)(M.Path,{fillRule:"evenodd",clipRule:"evenodd",d:"M12 5.5A2.25 2.25 0 0 0 9.878 7h4.244A2.251 2.251 0 0 0 12 5.5ZM12 4a3.751 3.751 0 0 0-3.675 3H5v1.5h1.27l.818 8.997a2.75 2.75 0 0 0 2.739 2.501h4.347a2.75 2.75 0 0 0 2.738-2.5L17.73 8.5H19V7h-3.325A3.751 3.751 0 0 0 12 4Zm4.224 4.5H7.776l.806 8.861a1.25 1.25 0 0 0 1.245 1.137h4.347a1.25 1.25 0 0 0 1.245-1.137l.805-8.861Z"})});function xi(e){const t=new Set;if(1===e.length){const s=e[0];s.reason?.message&&t.add(s.reason.message)}else{const s=e.filter((({status:e})=>"rejected"===e));for(const e of s){const s=e;s.reason?.message&&t.add(s.reason.message)}}return t}const{PATTERN_TYPES:vi}=dn(ln.privateApis),Si={id:"delete-post",label:(0,fs.__)("Delete"),isPrimary:!0,icon:yi,isEligible:e=>sn(e)?nn(e):e.type===vi.user,supportsBulk:!0,hideModalHeader:!0,RenderModal:({items:e,closeModal:t,onActionPerformed:s})=>{const[o,n]=(0,u.useState)(!1),i=e.every((e=>sn(e)&&e?.has_theme_file));return(0,L.jsxs)(Uo.__experimentalVStack,{spacing:"5",children:[(0,L.jsx)(Uo.__experimentalText,{children:e.length>1?(0,fs.sprintf)((0,fs._n)("Delete %d item?","Delete %d items?",e.length),e.length):(0,fs.sprintf)((0,fs._x)('Delete "%s"?',"template part"),on(e[0]))}),(0,L.jsxs)(Uo.__experimentalHStack,{justify:"right",children:[(0,L.jsx)(Uo.Button,{variant:"tertiary",onClick:t,disabled:o,accessibleWhenDisabled:!0,__next40pxDefaultSize:!0,children:(0,fs.__)("Cancel")}),(0,L.jsx)(Uo.Button,{variant:"primary",onClick:async()=>{n(!0);const o={success:{messages:{getMessage:e=>i?(0,fs.sprintf)((0,fs.__)('"%s" reset.'),(0,Lo.decodeEntities)(on(e))):(0,fs.sprintf)((0,fs._x)('"%s" deleted.',"template part"),(0,Lo.decodeEntities)(on(e))),getBatchMessage:()=>i?(0,fs.__)("Items reset."):(0,fs.__)("Items deleted.")}},error:{messages:{getMessage:e=>1===e.size?[...e][0]:i?(0,fs.__)("An error occurred while reverting the item."):(0,fs.__)("An error occurred while deleting the item."),getBatchMessage:e=>0===e.size?i?(0,fs.__)("An error occurred while reverting the items."):(0,fs.__)("An error occurred while deleting the items."):1===e.size?i?(0,fs.sprintf)((0,fs.__)("An error occurred while reverting the items: %s"),[...e][0]):(0,fs.sprintf)((0,fs.__)("An error occurred while deleting the items: %s"),[...e][0]):i?(0,fs.sprintf)((0,fs.__)("Some errors occurred while reverting the items: %s"),[...e].join(",")):(0,fs.sprintf)((0,fs.__)("Some errors occurred while deleting the items: %s"),[...e].join(","))}}};await(async(e,t,s)=>{const{createSuccessNotice:o,createErrorNotice:n}=(0,c.dispatch)(_s.store),{deleteEntityRecord:i}=(0,c.dispatch)(d.store),r=await Promise.allSettled(e.map((e=>i("postType",e.type,e.id,{force:!0},{throwOnError:!0}))));if(r.every((({status:e})=>"fulfilled"===e))){var a;let n;n=1===r.length?t.success.messages.getMessage(e[0]):t.success.messages.getBatchMessage(e),o(n,{type:null!==(a=t.success.type)&&void 0!==a?a:"snackbar",id:t.success.id}),s.onActionPerformed?.(e)}else{var l;const e=xi(r);let o="";o=1===r.length?t.error.messages.getMessage(e):t.error.messages.getBatchMessage(e),n(o,{type:null!==(l=t.error.type)&&void 0!==l?l:"snackbar",id:t.error.id}),s.onActionError?.()}})(e,o,{onActionPerformed:s}),n(!1),t?.()},isBusy:o,disabled:o,accessibleWhenDisabled:!0,__next40pxDefaultSize:!0,children:(0,fs.__)("Delete")})]})]})}},wi=Si,ki={id:"move-to-trash",label:(0,fs.__)("Move to trash"),isPrimary:!0,icon:yi,isEligible:e=>!sn(e)&&"wp_block"!==e.type&&(!!e.status&&!["auto-draft","trash"].includes(e.status)&&e.permissions?.delete),supportsBulk:!0,hideModalHeader:!0,RenderModal:({items:e,closeModal:t,onActionPerformed:s})=>{const[o,n]=(0,u.useState)(!1),{createSuccessNotice:i,createErrorNotice:r}=(0,c.useDispatch)(_s.store),{deleteEntityRecord:a}=(0,c.useDispatch)(d.store);return(0,L.jsxs)(Uo.__experimentalVStack,{spacing:"5",children:[(0,L.jsx)(Uo.__experimentalText,{children:1===e.length?(0,fs.sprintf)((0,fs.__)('Are you sure you want to move "%s" to the trash?'),on(e[0])):(0,fs.sprintf)((0,fs._n)("Are you sure you want to move %d item to the trash ?","Are you sure you want to move %d items to the trash ?",e.length),e.length)}),(0,L.jsxs)(Uo.__experimentalHStack,{justify:"right",children:[(0,L.jsx)(Uo.Button,{__next40pxDefaultSize:!0,variant:"tertiary",onClick:t,disabled:o,accessibleWhenDisabled:!0,children:(0,fs.__)("Cancel")}),(0,L.jsx)(Uo.Button,{__next40pxDefaultSize:!0,variant:"primary",onClick:async()=>{n(!0);const o=await Promise.allSettled(e.map((e=>a("postType",e.type,e.id.toString(),{},{throwOnError:!0}))));if(o.every((({status:e})=>"fulfilled"===e))){let t;t=1===o.length?(0,fs.sprintf)((0,fs.__)('"%s" moved to the trash.'),on(e[0])):(0,fs.sprintf)((0,fs._n)("%s item moved to the trash.","%s items moved to the trash.",e.length),e.length),i(t,{type:"snackbar",id:"move-to-trash-action"})}else{let e;if(1===o.length){const t=o[0];e=t.reason?.message?t.reason.message:(0,fs.__)("An error occurred while moving the item to the trash.")}else{const t=new Set,s=o.filter((({status:e})=>"rejected"===e));for(const e of s){const s=e;s.reason?.message&&t.add(s.reason.message)}e=0===t.size?(0,fs.__)("An error occurred while moving the items to the trash."):1===t.size?(0,fs.sprintf)((0,fs.__)("An error occurred while moving the item to the trash: %s"),[...t][0]):(0,fs.sprintf)((0,fs.__)("Some errors occurred while moving the items to the trash: %s"),[...t].join(","))}r(e,{type:"snackbar"})}s&&s(e),n(!1),t?.()},isBusy:o,disabled:o,accessibleWhenDisabled:!0,children:(0,fs._x)("Trash","verb")})]})]})}},Ci=ki,Pi={id:"permanently-delete",label:(0,fs.__)("Permanently delete"),supportsBulk:!0,icon:yi,isEligible(e){if(sn(e)||"wp_block"===e.type)return!1;const{status:t,permissions:s}=e;return"trash"===t&&s?.delete},hideModalHeader:!0,RenderModal:({items:e,closeModal:t,onActionPerformed:s})=>{const[o,n]=(0,u.useState)(!1),{createSuccessNotice:i,createErrorNotice:r}=(0,c.useDispatch)(_s.store),{deleteEntityRecord:a}=(0,c.useDispatch)(d.store);return(0,L.jsxs)(Uo.__experimentalVStack,{spacing:"5",children:[(0,L.jsx)(Uo.__experimentalText,{children:e.length>1?(0,fs.sprintf)((0,fs._n)("Are you sure you want to permanently delete %d item?","Are you sure you want to permanently delete %d items?",e.length),e.length):(0,fs.sprintf)((0,fs.__)('Are you sure you want to permanently delete "%s"?'),(0,Lo.decodeEntities)(on(e[0])))}),(0,L.jsxs)(Uo.__experimentalHStack,{justify:"right",children:[(0,L.jsx)(Uo.Button,{variant:"tertiary",onClick:t,disabled:o,accessibleWhenDisabled:!0,__next40pxDefaultSize:!0,children:(0,fs.__)("Cancel")}),(0,L.jsx)(Uo.Button,{variant:"primary",onClick:async()=>{n(!0);const o=await Promise.allSettled(e.map((e=>a("postType",e.type,e.id,{force:!0},{throwOnError:!0}))));if(o.every((({status:e})=>"fulfilled"===e))){let t;t=1===o.length?(0,fs.sprintf)((0,fs.__)('"%s" permanently deleted.'),on(e[0])):(0,fs.__)("The items were permanently deleted."),i(t,{type:"snackbar",id:"permanently-delete-post-action"}),s?.(e)}else{let e;if(1===o.length){const t=o[0];e=t.reason?.message?t.reason.message:(0,fs.__)("An error occurred while permanently deleting the item.")}else{const t=new Set,s=o.filter((({status:e})=>"rejected"===e));for(const e of s){const s=e;s.reason?.message&&t.add(s.reason.message)}e=0===t.size?(0,fs.__)("An error occurred while permanently deleting the items."):1===t.size?(0,fs.sprintf)((0,fs.__)("An error occurred while permanently deleting the items: %s"),[...t][0]):(0,fs.sprintf)((0,fs.__)("Some errors occurred while permanently deleting the items: %s"),[...t].join(","))}r(e,{type:"snackbar"})}n(!1),t?.()},isBusy:o,disabled:o,accessibleWhenDisabled:!0,__next40pxDefaultSize:!0,children:(0,fs.__)("Delete permanently")})]})]})}},ji=Pi,Ei=window.wp.mediaUtils,Ti=(0,L.jsx)(M.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,L.jsx)(M.Path,{d:"M5 11.25h14v1.5H5z"})}),Bi={id:"featured_media",type:"media",label:(0,fs.__)("Featured Image"),Edit:({data:e,field:t,onChange:s})=>{const{id:o}=t,n=t.getValue({item:e}),i=(0,c.useSelect)((e=>{const{getEntityRecord:t}=e(d.store);return t("root","media",n)}),[n]),r=(0,u.useCallback)((e=>s({[o]:e})),[o,s]),a=i?.source_url,l=i?.title?.rendered,p=(0,u.useRef)(null);return(0,L.jsx)("fieldset",{className:"fields-controls__featured-image",children:(0,L.jsx)("div",{className:"fields-controls__featured-image-container",children:(0,L.jsx)(Ei.MediaUpload,{onSelect:e=>{r(e.id)},allowedTypes:["image"],render:({open:e})=>(0,L.jsx)("div",{ref:p,role:"button",tabIndex:-1,onClick:()=>{e()},onKeyDown:e,children:(0,L.jsxs)(Uo.__experimentalGrid,{rowGap:0,columnGap:8,templateColumns:"24px 1fr 24px",children:[a&&(0,L.jsxs)(L.Fragment,{children:[(0,L.jsx)("img",{className:"fields-controls__featured-image-image",alt:"",width:24,height:24,src:a}),(0,L.jsx)("span",{className:"fields-controls__featured-image-title",children:l})]}),!a&&(0,L.jsxs)(L.Fragment,{children:[(0,L.jsx)("span",{className:"fields-controls__featured-image-placeholder",style:{width:"24px",height:"24px"}}),(0,L.jsx)("span",{className:"fields-controls__featured-image-title",children:(0,fs.__)("Choose an image…")})]}),a&&(0,L.jsx)(L.Fragment,{children:(0,L.jsx)(Uo.Button,{size:"small",className:"fields-controls__featured-image-remove-button",icon:Ti,onClick:e=>{e.stopPropagation(),r(0)}})})]})})})})})},render:({item:e})=>{const t=e.featured_media,s=(0,c.useSelect)((e=>{const{getEntityRecord:s}=e(d.store);return t?s("root","media",t):null}),[t]),o=s?.source_url;return o?(0,L.jsx)("img",{className:"fields-controls__featured-image-image",src:o,alt:""}):(0,L.jsx)("span",{className:"fields-controls__featured-image-placeholder"})},enableSorting:!1},Ii=Bi;function Ni(e){var t,s,o="";if("string"==typeof e||"number"==typeof e)o+=e;else if("object"==typeof e)if(Array.isArray(e)){var n=e.length;for(t=0;t<n;t++)e[t]&&(s=Ni(e[t]))&&(o&&(o+=" "),o+=s)}else for(s in e)e[s]&&(o&&(o+=" "),o+=s);return o}const Di=function(){for(var e,t,s=0,o="",n=arguments.length;s<n;s++)(e=arguments[s])&&(t=Ni(e))&&(o&&(o+=" "),o+=t);return o},Ai=(0,L.jsx)(M.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,L.jsx)(M.Path,{fillRule:"evenodd",d:"M7.25 16.437a6.5 6.5 0 1 1 9.5 0V16A2.75 2.75 0 0 0 14 13.25h-4A2.75 2.75 0 0 0 7.25 16v.437Zm1.5 1.193a6.47 6.47 0 0 0 3.25.87 6.47 6.47 0 0 0 3.25-.87V16c0-.69-.56-1.25-1.25-1.25h-4c-.69 0-1.25.56-1.25 1.25v1.63ZM4 12a8 8 0 1 1 16 0 8 8 0 0 1-16 0Zm10-2a2 2 0 1 1-4 0 2 2 0 0 1 4 0Z",clipRule:"evenodd"})});const Ri=function({item:e}){const{text:t,imageUrl:s}=(0,c.useSelect)((t=>{const{getEntityRecord:s}=t(d.store);let o;return e.author&&(o=s("root","user",e.author)),{imageUrl:o?.avatar_urls?.[48],text:o?.name}}),[e]),[o,n]=(0,u.useState)(!1);return(0,L.jsxs)(Uo.__experimentalHStack,{alignment:"left",spacing:0,children:[!!s&&(0,L.jsx)("div",{className:Di("page-templates-author-field__avatar",{"is-loaded":o}),children:(0,L.jsx)("img",{onLoad:()=>n(!0),alt:(0,fs.__)("Author avatar"),src:s})}),!s&&(0,L.jsx)("div",{className:"page-templates-author-field__icon",children:(0,L.jsx)(Uo.Icon,{icon:Ai})}),(0,L.jsx)("span",{className:"page-templates-author-field__name",children:t})]})},Mi={label:(0,fs.__)("Author"),id:"author",type:"integer",elements:[],render:Ri,sort:(e,t,s)=>{const o=e._embedded?.author?.[0]?.name||"",n=t._embedded?.author?.[0]?.name||"";return"asc"===s?o.localeCompare(n):n.localeCompare(o)}},Li=Mi,Oi=(0,L.jsx)(M.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,L.jsx)(M.Path,{fillRule:"evenodd",clipRule:"evenodd",d:"M12 18.5a6.5 6.5 0 1 1 0-13 6.5 6.5 0 0 1 0 13ZM4 12a8 8 0 1 1 16 0 8 8 0 0 1-16 0Zm8 4a4 4 0 0 0 4-4H8a4 4 0 0 0 4 4Z"})}),Fi=(0,L.jsx)(M.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,L.jsx)(M.Path,{fillRule:"evenodd",clipRule:"evenodd",d:"M12 18.5a6.5 6.5 0 1 1 0-13 6.5 6.5 0 0 1 0 13ZM4 12a8 8 0 1 1 16 0 8 8 0 0 1-16 0Zm9 1V8h-1.5v3.5h-2V13H13Z"})}),Vi=(0,L.jsx)(M.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,L.jsx)(M.Path,{fillRule:"evenodd",clipRule:"evenodd",d:"M12 18.5a6.5 6.5 0 1 1 0-13 6.5 6.5 0 0 1 0 13ZM4 12a8 8 0 1 1 16 0 8 8 0 0 1-16 0Zm8 4a4 4 0 0 1-4-4h4V8a4 4 0 0 1 0 8Z"})}),zi=(0,L.jsx)(M.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,L.jsx)(M.Path,{fillRule:"evenodd",clipRule:"evenodd",d:"M12 18.5A6.5 6.5 0 0 1 6.93 7.931l9.139 9.138A6.473 6.473 0 0 1 12 18.5Zm5.123-2.498a6.5 6.5 0 0 0-9.124-9.124l9.124 9.124ZM4 12a8 8 0 1 1 16 0 8 8 0 0 1-16 0Z"})}),Ui=(0,L.jsx)(M.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,L.jsx)(M.Path,{fillRule:"evenodd",clipRule:"evenodd",d:"M12 18.5a6.5 6.5 0 1 1 0-13 6.5 6.5 0 0 1 0 13ZM4 12a8 8 0 1 1 16 0 8 8 0 0 1-16 0Zm11.53-1.47-1.06-1.06L11 12.94l-1.47-1.47-1.06 1.06L11 15.06l4.53-4.53Z"})}),Hi=[{value:"draft",label:(0,fs.__)("Draft"),icon:Oi,description:(0,fs.__)("Not ready to publish.")},{value:"future",label:(0,fs.__)("Scheduled"),icon:Fi,description:(0,fs.__)("Publish automatically on a chosen date.")},{value:"pending",label:(0,fs.__)("Pending Review"),icon:Vi,description:(0,fs.__)("Waiting for review before publishing.")},{value:"private",label:(0,fs.__)("Private"),icon:zi,description:(0,fs.__)("Only visible to site admins and editors.")},{value:"publish",label:(0,fs.__)("Published"),icon:Ui,description:(0,fs.__)("Visible to everyone.")},{value:"trash",label:(0,fs.__)("Trash"),icon:yi}];const Gi=function({item:e}){const t=Hi.find((({value:t})=>t===e.status)),s=t?.label||e.status,o=t?.icon;return(0,L.jsxs)(Uo.__experimentalHStack,{alignment:"left",spacing:0,children:[o&&(0,L.jsx)("div",{className:"edit-site-post-list__status-icon",children:(0,L.jsx)(Uo.Icon,{icon:o})}),(0,L.jsx)("span",{children:s})]})},$i={label:(0,fs.__)("Status"),id:"status",type:"text",elements:Hi,render:Gi,Edit:"radio",enableSorting:!1,filterBy:{operators:["isAny"]}},Wi=e=>(0,x.dateI18n)((0,x.getSettings)().formats.datetimeAbbreviated,(0,x.getDate)(e)),Zi=({item:e})=>{var t,s,o,n;var i;if(["draft","private"].includes(null!==(t=e.status)&&void 0!==t?t:""))return(0,u.createInterpolateElement)((0,fs.sprintf)((0,fs.__)("<span>Modified: <time>%s</time></span>"),Wi(null!==(i=e.date)&&void 0!==i?i:null)),{span:(0,L.jsx)("span",{}),time:(0,L.jsx)("time",{})});var r;if("future"===e.status)return(0,u.createInterpolateElement)((0,fs.sprintf)((0,fs.__)("<span>Scheduled: <time>%s</time></span>"),Wi(null!==(r=e.date)&&void 0!==r?r:null)),{span:(0,L.jsx)("span",{}),time:(0,L.jsx)("time",{})});var a;if("publish"===e.status)return(0,u.createInterpolateElement)((0,fs.sprintf)((0,fs.__)("<span>Published: <time>%s</time></span>"),Wi(null!==(a=e.date)&&void 0!==a?a:null)),{span:(0,L.jsx)("span",{}),time:(0,L.jsx)("time",{})});const l=(0,x.getDate)(null!==(s=e.modified)&&void 0!==s?s:null)>(0,x.getDate)(null!==(o=e.date)&&void 0!==o?o:null)?e.modified:e.date;return"pending"===e.status?(0,u.createInterpolateElement)((0,fs.sprintf)((0,fs.__)("<span>Modified: <time>%s</time></span>"),Wi(null!=l?l:null)),{span:(0,L.jsx)("span",{}),time:(0,L.jsx)("time",{})}):(0,L.jsx)("time",{children:Wi(null!==(n=e.date)&&void 0!==n?n:null)})},Yi={id:"date",type:"datetime",label:(0,fs.__)("Date"),render:Zi},Ki=(0,L.jsx)(M.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,L.jsx)(M.Path,{fillRule:"evenodd",clipRule:"evenodd",d:"M5.625 5.5h9.75c.069 0 .125.056.125.125v9.75a.125.125 0 0 1-.125.125h-9.75a.125.125 0 0 1-.125-.125v-9.75c0-.069.056-.125.125-.125ZM4 5.625C4 4.728 4.728 4 5.625 4h9.75C16.273 4 17 4.728 17 5.625v9.75c0 .898-.727 1.625-1.625 1.625h-9.75A1.625 1.625 0 0 1 4 15.375v-9.75Zm14.5 11.656v-9H20v9C20 18.8 18.77 20 17.251 20H6.25v-1.5h11.001c.69 0 1.249-.528 1.249-1.219Z"})}),qi=e=>"object"!=typeof e?"":e.slug||(0,v.cleanForSlug)(on(e))||e.id.toString(),Qi=({field:e,onChange:t,data:s})=>{const{id:o}=e,n=e.getValue({item:s})||qi(s),i=s.permalink_template||"",r=/%(?:postname|pagename)%/,[a,l]=i.split(r),d=a,m=l,h=r.test(i),g=(0,u.useRef)(n),_=n||g.current,f=h?`${d}${_}${m}`:(0,v.safeDecodeURIComponent)(s.link||"");(0,u.useEffect)((()=>{n&&void 0===g.current&&(g.current=n)}),[n]);const b=(0,u.useCallback)((e=>t({[o]:e})),[o,t]),{createNotice:y}=(0,c.useDispatch)(_s.store),x=(0,p.useCopyToClipboard)(f,(()=>{y("info",(0,fs.__)("Copied Permalink to clipboard."),{isDismissible:!0,type:"snackbar"})})),S="editor-post-url__slug-description-"+(0,p.useInstanceId)(Qi);return(0,L.jsxs)("fieldset",{className:"fields-controls__slug",children:[h&&(0,L.jsxs)(Uo.__experimentalVStack,{children:[(0,L.jsxs)(Uo.__experimentalVStack,{spacing:"0px",children:[(0,L.jsx)("span",{children:(0,fs.__)("Customize the last part of the Permalink.")}),(0,L.jsx)(Uo.ExternalLink,{href:"https://wordpress.org/documentation/article/page-post-settings-sidebar/#permalink",children:(0,fs.__)("Learn more")})]}),(0,L.jsx)(Uo.__experimentalInputControl,{__next40pxDefaultSize:!0,prefix:(0,L.jsx)(Uo.__experimentalInputControlPrefixWrapper,{children:"/"}),suffix:(0,L.jsx)(Uo.Button,{__next40pxDefaultSize:!0,icon:Ki,ref:x,label:(0,fs.__)("Copy")}),label:(0,fs.__)("Link"),hideLabelFromVision:!0,value:n,autoComplete:"off",spellCheck:"false",type:"text",className:"fields-controls__slug-input",onChange:e=>{b(e)},onBlur:()=>{""===n&&b(g.current)},"aria-describedby":S}),(0,L.jsxs)("div",{className:"fields-controls__slug-help",children:[(0,L.jsx)("span",{className:"fields-controls__slug-help-visual-label",children:(0,fs.__)("Permalink:")}),(0,L.jsxs)(Uo.ExternalLink,{className:"fields-controls__slug-help-link",href:f,children:[(0,L.jsx)("span",{className:"fields-controls__slug-help-prefix",children:d}),(0,L.jsx)("span",{className:"fields-controls__slug-help-slug",children:_}),(0,L.jsx)("span",{className:"fields-controls__slug-help-suffix",children:m})]})]})]}),!h&&(0,L.jsx)(Uo.ExternalLink,{className:"fields-controls__slug-help",href:f,children:f})]})},Xi=Qi,Ji=({item:e})=>{const t=qi(e),s=(0,u.useRef)(t);(0,u.useEffect)((()=>{t&&void 0===s.current&&(s.current=t)}),[t]);return`${t||s.current}`},er={id:"slug",type:"text",label:(0,fs.__)("Slug"),Edit:Xi,render:Ji};var tr=s(9681),sr=s.n(tr);function or(e){return"object"==typeof e.title&&"rendered"in e.title&&e.title.rendered?(0,Lo.decodeEntities)(e.title.rendered):`#${e?.id} (${(0,fs.__)("no title")})`}const nr=(e,t)=>{const s=sr()(e||"").toLowerCase(),o=sr()(t||"").toLowerCase();return s===o?0:s.startsWith(o)?s.length:1/0};function ir({data:e,onChangeControl:t}){const[s,o]=(0,u.useState)(null),n=e.parent,i=e.id,r=e.type,{parentPostTitle:a,pageItems:l,isHierarchical:m}=(0,c.useSelect)((e=>{const{getEntityRecord:t,getEntityRecords:o,getPostType:a}=e(d.store),l=a(r),c=l?.hierarchical&&l.viewable,u=n?t("postType",r,n):null,p={per_page:100,exclude:i,parent_exclude:i,orderby:"menu_order",order:"asc",_fields:"id,title,parent",...null!==s&&{search:s}};return{isHierarchical:c,parentPostTitle:u?or(u):"",pageItems:c?o("postType",r,p):null}}),[s,n,i,r]),h=(0,u.useMemo)((()=>{const e=(t,o=0)=>{const n=t.map((t=>[{value:t.id,label:"— ".repeat(o)+(0,Lo.decodeEntities)(t.name),rawName:t.name},...e(t.children||[],o+1)])).sort((([e],[t])=>nr(e.rawName,null!=s?s:"")>=nr(t.rawName,null!=s?s:"")?1:-1));return n.flat()};if(!l)return[];let t=l.map((e=>{var t;return{id:e.id,parent:null!==(t=e.parent)&&void 0!==t?t:null,name:or(e)}}));s||(t=function(e){const t=e.map((e=>({children:[],...e})));if(t.some((({parent:e})=>null==e)))return t;const s=t.reduce(((e,t)=>{const{parent:s}=t;return e[s]||(e[s]=[]),e[s].push(t),e}),{}),o=e=>e.map((e=>{const t=s[e.id];return{...e,children:t&&t.length?o(t):[]}}));return o(s[0]||[])}(t));const o=e(t),i=o.find((e=>e.value===n));return n&&a&&!i&&o.unshift({value:n,label:a,rawName:""}),o.map((e=>({...e,value:e.value.toString()})))}),[l,s,a,n]);if(!m)return null;return(0,L.jsx)(Uo.ComboboxControl,{__nextHasNoMarginBottom:!0,__next40pxDefaultSize:!0,label:(0,fs.__)("Parent"),help:(0,fs.__)("Choose a parent page."),value:n?.toString(),options:h,onFilterValueChange:(0,p.debounce)((e=>{o(e)}),300),onChange:e=>{var s;if(e)return t(null!==(s=parseInt(e,10))&&void 0!==s?s:0);t(0)},hideLabelFromVision:!0})}const rr={id:"parent",type:"text",label:(0,fs.__)("Parent"),Edit:({data:e,field:t,onChange:s})=>{const{id:o}=t,n=(0,c.useSelect)((e=>e(d.store).getEntityRecord("root","__unstableBase")?.home),[]),i=(0,u.useCallback)((e=>s({[o]:e})),[o,s]);return(0,L.jsx)("fieldset",{className:"fields-controls__parent",children:(0,L.jsxs)("div",{children:[(0,u.createInterpolateElement)((0,fs.sprintf)((0,fs.__)('Child pages inherit characteristics from their parent, such as URL structure. For instance, if "Pricing" is a child of "Services", its URL would be %1$s<wbr />/services<wbr />/pricing.'),(0,v.filterURLForDisplay)(n).replace(/([/.])/g,"<wbr />$1")),{wbr:(0,L.jsx)("wbr",{})}),(0,L.jsx)("p",{children:(0,u.createInterpolateElement)((0,fs.__)("They also show up as sub-items in the default navigation menu. <a>Learn more.</a>"),{a:(0,L.jsx)(Uo.ExternalLink,{href:(0,fs.__)("https://wordpress.org/documentation/article/page-post-settings-sidebar/#page-attributes"),children:void 0})})}),(0,L.jsx)(ir,{data:e,onChangeControl:i})]})})},render:({item:e})=>{const t=(0,c.useSelect)((t=>{const{getEntityRecord:s}=t(d.store);return e?.parent?s("postType",e.type,e.parent):null}),[e.parent,e.type]);return t?(0,L.jsx)(L.Fragment,{children:or(t)}):(0,L.jsx)(L.Fragment,{children:(0,fs.__)("None")})},enableSorting:!0},ar={id:"comment_status",label:(0,fs.__)("Discussion"),type:"text",Edit:"radio",enableSorting:!1,filterBy:{operators:[]},elements:[{value:"open",label:(0,fs.__)("Open"),description:(0,fs.__)("Visitors can add new comments and replies.")},{value:"closed",label:(0,fs.__)("Closed"),description:(0,fs.__)("Visitors cannot add new comments or replies. Existing comments remain visible.")}]},lr=[],cr={id:"template",type:"text",label:(0,fs.__)("Template"),Edit:({data:e,field:t,onChange:s})=>{const{id:o}=t,n=e.type,i="number"==typeof e.id?e.id:parseInt(e.id,10),r=e.slug,{canSwitchTemplate:a,templates:l}=(0,c.useSelect)((e=>{var t;const s=null!==(t=e(d.store).getEntityRecords("postType","wp_template",{per_page:-1,post_type:n}))&&void 0!==t?t:lr,{getHomePage:o,getPostsPageId:r}=dn(e(d.store)),a=r()===+i,l="page"===n&&o()?.postId===+i;return{templates:s,canSwitchTemplate:!a&&!l}}),[i,n]),m=(0,u.useMemo)((()=>a?l.filter((t=>t.is_custom&&t.slug!==e.template&&!!t.content.raw)).map((e=>({name:e.slug,blocks:(0,y.parse)(e.content.raw),title:(0,Lo.decodeEntities)(e.title.rendered),id:e.id}))):[]),[a,e.template,l]),g=(0,p.useAsyncList)(m),_=t.getValue({item:e}),f=l.find((e=>e.slug===_)),b=(0,c.useSelect)((e=>{if(f)return f;let t;if(t=r?"page"===n?`${n}-${r}`:`single-${n}-${r}`:"page"===n?"page":`single-${n}`,n){const s=e(d.store).getDefaultTemplateId({slug:t});return e(d.store).getEntityRecord("postType","wp_template",s)}}),[f,n,r]),[x,v]=(0,u.useState)(!1),S=(0,u.useCallback)((e=>s({[o]:e})),[o,s]);return(0,L.jsxs)("fieldset",{className:"fields-controls__template",children:[(0,L.jsx)(Uo.Dropdown,{popoverProps:{placement:"bottom-start"},renderToggle:({onToggle:e})=>(0,L.jsx)(Uo.Button,{__next40pxDefaultSize:!0,variant:"tertiary",size:"compact",onClick:e,children:b?on(b):""}),renderContent:({onToggle:e})=>(0,L.jsxs)(Uo.MenuGroup,{children:[(0,L.jsx)(Uo.MenuItem,{onClick:()=>{v(!0),e()},children:(0,fs.__)("Change template")}),""!==_&&(0,L.jsx)(Uo.MenuItem,{onClick:()=>{S(""),e()},children:(0,fs.__)("Use default template")})]})}),x&&(0,L.jsx)(Uo.Modal,{title:(0,fs.__)("Choose a template"),onRequestClose:()=>v(!1),overlayClassName:"fields-controls__template-modal",isFullScreen:!0,children:(0,L.jsx)("div",{className:"fields-controls__template-content",children:(0,L.jsx)(h.__experimentalBlockPatternsList,{label:(0,fs.__)("Templates"),blockPatterns:m,shownPatterns:g,onClickPattern:e=>{S(e.name),v(!1)}})})})]})},enableSorting:!1},dr=cr;const ur={id:"password",type:"text",Edit:function({data:e,onChange:t,field:s}){const[o,n]=(0,u.useState)(!!s.getValue({item:e}));return(0,L.jsxs)(Uo.__experimentalVStack,{as:"fieldset",spacing:4,className:"fields-controls__password",children:[(0,L.jsx)(Uo.CheckboxControl,{__nextHasNoMarginBottom:!0,label:(0,fs.__)("Password protected"),help:(0,fs.__)("Only visible to those who know the password"),checked:o,onChange:e=>{n(e),e||t({password:""})}}),o&&(0,L.jsx)("div",{className:"fields-controls__password-input",children:(0,L.jsx)(Uo.TextControl,{label:(0,fs.__)("Password"),onChange:e=>t({password:e}),value:s.getValue({item:e})||"",placeholder:(0,fs.__)("Use a secure password"),type:"text",__next40pxDefaultSize:!0,__nextHasNoMarginBottom:!0,maxLength:255})})]})},enableSorting:!1,enableHiding:!1,isVisible:e=>"private"!==e.status};function pr({item:e,className:t,children:s}){const o=on(e);return(0,L.jsxs)(Uo.__experimentalHStack,{className:Di("fields-field__title",t),alignment:"center",justify:"flex-start",children:[(0,L.jsx)("span",{children:o||(0,fs.__)("(no title)")}),s]})}function mr({item:e}){return(0,L.jsx)(pr,{item:e})}const{Badge:hr}=dn(Uo.privateApis);const gr={type:"text",id:"title",label:(0,fs.__)("Title"),placeholder:(0,fs.__)("No title"),getValue:({item:e})=>on(e),render:function({item:e}){const{frontPageId:t,postsPageId:s}=(0,c.useSelect)((e=>{const{getEntityRecord:t}=e(d.store),s=t("root","site");return{frontPageId:s?.page_on_front,postsPageId:s?.page_for_posts}}),[]);return(0,L.jsx)(pr,{item:e,className:"fields-field__page-title",children:[t,s].includes(e.id)&&(0,L.jsx)(hr,{children:e.id===t?(0,fs.__)("Homepage"):(0,fs.__)("Posts Page")})})},enableHiding:!1,enableGlobalSearch:!0},_r={type:"text",label:(0,fs.__)("Template"),placeholder:(0,fs.__)("No title"),id:"title",getValue:({item:e})=>on(e),render:mr,enableHiding:!1,enableGlobalSearch:!0};const fr=(0,u.forwardRef)((function({icon:e,size:t=24,...s},o){return(0,u.cloneElement)(e,{width:t,height:t,...s,ref:o})})),br=(0,L.jsx)(M.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,L.jsx)(M.Path,{fillRule:"evenodd",clipRule:"evenodd",d:"M15 11h-.2V9c0-1.5-1.2-2.8-2.8-2.8S9.2 7.5 9.2 9v2H9c-.6 0-1 .4-1 1v4c0 .6.4 1 1 1h6c.6 0 1-.4 1-1v-4c0-.6-.4-1-1-1zm-1.8 0h-2.5V9c0-.7.6-1.2 1.2-1.2s1.2.6 1.2 1.2v2z"})}),{PATTERN_TYPES:yr}=dn(ln.privateApis);const xr={type:"text",id:"title",label:(0,fs.__)("Title"),placeholder:(0,fs.__)("No title"),getValue:({item:e})=>on(e),render:function({item:e}){return(0,L.jsx)(pr,{item:e,className:"fields-field__pattern-title",children:e.type===yr.theme&&(0,L.jsx)(Uo.Tooltip,{placement:"top",text:(0,fs.__)("This pattern cannot be edited."),children:(0,L.jsx)(fr,{icon:br,size:24})})})},enableHiding:!1,enableGlobalSearch:!0},vr={type:"text",id:"title",label:(0,fs.__)("Title"),placeholder:(0,fs.__)("No title"),getValue:({item:e})=>on(e),render:mr,enableHiding:!1,enableGlobalSearch:!0};const Sr=(0,p.createHigherOrderComponent)((e=>({useSubRegistry:t=!0,...s})=>{const o=(0,c.useRegistry)(),[n]=(0,u.useState)((()=>new WeakMap)),i=function(e,t,s){if(!s)return t;let o=e.get(t);return o||(o=(0,c.createRegistry)({"core/block-editor":h.storeConfig},t),o.registerStore("core/editor",Dc),e.set(t,o)),o}(n,o,t);return i===o?(0,L.jsx)(e,{registry:o,...s}):(0,L.jsx)(c.RegistryProvider,{value:i,children:(0,L.jsx)(e,{registry:i,...s})})}),"withRegistryProvider"),wr=(e,t)=>`<a ${kr(e)}>${t}</a>`,kr=e=>`href="${e}" target="_blank" rel="noreferrer noopener"`,Cr=e=>{const{title:t,foreign_landing_url:s,creator:o,creator_url:n,license:i,license_version:r,license_url:a}=e,l=((e,t)=>{let s=e.trim();return"pdm"!==e&&(s=e.toUpperCase().replace("SAMPLING","Sampling")),t&&(s+=` ${t}`),["pdm","cc0"].includes(e)||(s=`CC ${s}`),s})(i,r),c=(0,Lo.decodeEntities)(o);let d;return d=c?t?(0,fs.sprintf)((0,fs._x)('"%1$s" by %2$s/ %3$s',"caption"),wr(s,(0,Lo.decodeEntities)(t)),n?wr(n,c):c,a?wr(`${a}?ref=openverse`,l):l):(0,fs.sprintf)((0,fs._x)("<a %1$s>Work</a> by %2$s/ %3$s","caption"),kr(s),n?wr(n,c):c,a?wr(`${a}?ref=openverse`,l):l):t?(0,fs.sprintf)((0,fs._x)('"%1$s"/ %2$s',"caption"),wr(s,(0,Lo.decodeEntities)(t)),a?wr(`${a}?ref=openverse`,l):l):(0,fs.sprintf)((0,fs._x)("<a %1$s>Work</a>/ %2$s","caption"),kr(s),a?wr(`${a}?ref=openverse`,l):l),d.replace(/\s{2}/g," ")},Pr=async(e={})=>(await(0,c.resolveSelect)(d.store).getMediaItems({...e,orderBy:e?.search?"relevance":"date"})).map((e=>({...e,alt:e.alt_text,url:e.source_url,previewUrl:e.media_details?.sizes?.medium?.source_url,caption:e.caption?.raw}))),jr=[{name:"images",labels:{name:(0,fs.__)("Images"),search_items:(0,fs.__)("Search images")},mediaType:"image",fetch:async(e={})=>Pr({...e,media_type:"image"})},{name:"videos",labels:{name:(0,fs.__)("Videos"),search_items:(0,fs.__)("Search videos")},mediaType:"video",fetch:async(e={})=>Pr({...e,media_type:"video"})},{name:"audio",labels:{name:(0,fs.__)("Audio"),search_items:(0,fs.__)("Search audio")},mediaType:"audio",fetch:async(e={})=>Pr({...e,media_type:"audio"})},{name:"openverse",labels:{name:(0,fs.__)("Openverse"),search_items:(0,fs.__)("Search Openverse")},mediaType:"image",async fetch(e={}){const t={...e,mature:!1,excluded_source:"flickr,inaturalist,wikimedia",license:"pdm,cc0"},s={per_page:"page_size",search:"q"},o=new URL("https://api.openverse.org/v1/images/");Object.entries(t).forEach((([e,t])=>{const n=s[e]||e;o.searchParams.set(n,t)}));const n=await window.fetch(o,{headers:{"User-Agent":"WordPress/inserter-media-fetch"}});return(await n.json()).results.map((e=>({...e,title:e.title?.toLowerCase().startsWith("file:")?e.title.slice(5):e.title,sourceId:e.id,id:void 0,caption:Cr(e),previewUrl:e.thumbnail})))},getReportUrl:({sourceId:e})=>`https://wordpress.org/openverse/image/${e}/report/`,isExternalResource:!0}],Er={randomUUID:"undefined"!=typeof crypto&&crypto.randomUUID&&crypto.randomUUID.bind(crypto)};let Tr;const Br=new Uint8Array(16);function Ir(){if(!Tr&&(Tr="undefined"!=typeof crypto&&crypto.getRandomValues&&crypto.getRandomValues.bind(crypto),!Tr))throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");return Tr(Br)}const Nr=[];for(let e=0;e<256;++e)Nr.push((e+256).toString(16).slice(1));function Dr(e,t=0){return Nr[e[t+0]]+Nr[e[t+1]]+Nr[e[t+2]]+Nr[e[t+3]]+"-"+Nr[e[t+4]]+Nr[e[t+5]]+"-"+Nr[e[t+6]]+Nr[e[t+7]]+"-"+Nr[e[t+8]]+Nr[e[t+9]]+"-"+Nr[e[t+10]]+Nr[e[t+11]]+Nr[e[t+12]]+Nr[e[t+13]]+Nr[e[t+14]]+Nr[e[t+15]]}const Ar=function(e,t,s){if(Er.randomUUID&&!t&&!e)return Er.randomUUID();const o=(e=e||{}).random||(e.rng||Ir)();if(o[6]=15&o[6]|64,o[8]=63&o[8]|128,t){s=s||0;for(let e=0;e<16;++e)t[s+e]=o[e];return t}return Dr(o)},Rr=()=>{};function Mr({additionalData:e={},allowedTypes:t,filesList:s,maxUploadFileSize:o,onError:n=Rr,onFileChange:i,onSuccess:r,multiple:a=!0}){const{getCurrentPost:l,getEditorSettings:d}=(0,c.select)(Ac),{lockPostAutosaving:u,unlockPostAutosaving:p,lockPostSaving:m,unlockPostSaving:h}=(0,c.dispatch)(Ac),g=d().allowedMimeTypes,_=`image-upload-${Ar()}`;let f=!1;o=o||d().maxUploadFileSize;const b=l(),y="number"==typeof b?.id?b.id:b?.wp_id,x=y?{post:y}:{},v=()=>{h(_),p(_),f=!1};(0,Ei.uploadMedia)({allowedTypes:t,filesList:s,onFileChange:e=>{f?v():(m(_),u(_),f=!0),i?.(e)},onSuccess:r,additionalData:{...x,...e},maxUploadFileSize:o,onError:({message:e})=>{v(),n(e)},wpAllowedMimeTypes:g,multiple:a})}const{sideloadMedia:Lr}=$(Ei.privateApis),Or=Lr;var Fr=s(66),Vr=s.n(Fr);
/*!
 * is-plain-object <https://github.com/jonschlinkert/is-plain-object>
 *
 * Copyright (c) 2014-2017, Jon Schlinkert.
 * Released under the MIT License.
 */
function zr(e){return"[object Object]"===Object.prototype.toString.call(e)}function Ur(e){var t,s;return!1!==zr(e)&&(void 0===(t=e.constructor)||!1!==zr(s=t.prototype)&&!1!==s.hasOwnProperty("isPrototypeOf"))}const{GlobalStylesContext:Hr,cleanEmptyObject:Gr}=$(h.privateApis);function $r(e,t){return Vr()(e,t,{isMergeableObject:Ur,customMerge:e=>{if("backgroundImage"===e)return(e,t)=>t}})}function Wr(){const[e,t,s]=function(){const{globalStylesId:e,isReady:t,settings:s,styles:o,_links:n}=(0,c.useSelect)((e=>{const{getEntityRecord:t,getEditedEntityRecord:s,hasFinishedResolution:o,canUser:n}=e(d.store),i=e(d.store).__experimentalGetCurrentGlobalStylesId();let r;const a=i?n("update",{kind:"root",name:"globalStyles",id:i}):null;i&&"boolean"==typeof a&&(r=a?s("root","globalStyles",i):t("root","globalStyles",i,{context:"view"}));let l=!1;return o("__experimentalGetCurrentGlobalStylesId")&&(l=!i||(a?o("getEditedEntityRecord",["root","globalStyles",i]):o("getEntityRecord",["root","globalStyles",i,{context:"view"}]))),{globalStylesId:i,isReady:l,settings:r?.settings,styles:r?.styles,_links:r?._links}}),[]),{getEditedEntityRecord:i}=(0,c.useSelect)(d.store),{editEntityRecord:r}=(0,c.useDispatch)(d.store);return[t,(0,u.useMemo)((()=>({settings:null!=s?s:{},styles:null!=o?o:{},_links:null!=n?n:{}})),[s,o,n]),(0,u.useCallback)(((t,s={})=>{var o,n,a;const l=i("root","globalStyles",e),c={styles:null!==(o=l?.styles)&&void 0!==o?o:{},settings:null!==(n=l?.settings)&&void 0!==n?n:{},_links:null!==(a=l?._links)&&void 0!==a?a:{}},d="function"==typeof t?t(c):t;r("root","globalStyles",e,{styles:Gr(d.styles)||{},settings:Gr(d.settings)||{},_links:Gr(d._links)||{}},s)}),[e,r,i])]}(),[o,n]=function(){const e=(0,c.useSelect)((e=>e(d.store).__experimentalGetCurrentThemeBaseGlobalStyles()),[]);return[!!e,e]}(),i=(0,u.useMemo)((()=>n&&t?$r(n,t):{}),[t,n]);return(0,u.useMemo)((()=>({isReady:e&&o,user:t,base:n,merged:i,setUserConfig:s})),[i,t,n,s,e,o])}const Zr={};function Yr(e){const{RECEIVE_INTERMEDIATE_RESULTS:t}=$(d.privateApis),{getEntityRecords:s}=e(d.store);return s("postType","wp_block",{per_page:-1,[t]:!0})}const Kr=["__experimentalBlockDirectory","__experimentalDiscussionSettings","__experimentalFeatures","__experimentalGlobalStylesBaseStyles","alignWide","blockInspectorTabs","maxUploadFileSize","allowedMimeTypes","bodyPlaceholder","canLockBlocks","canUpdateBlockBindings","capabilities","clearBlockSelection","codeEditingEnabled","colors","disableCustomColors","disableCustomFontSizes","disableCustomSpacingSizes","disableCustomGradients","disableLayoutStyles","enableCustomLineHeight","enableCustomSpacing","enableCustomUnits","enableOpenverseMediaCategory","fontSizes","gradients","generateAnchors","onNavigateToEntityRecord","imageDefaultSize","imageDimensions","imageEditing","imageSizes","isPreviewMode","isRTL","locale","maxWidth","postContentAttributes","postsPerPage","readOnly","styles","titlePlaceholder","supportsLayout","widgetTypesToHideFromLegacyWidgetBlock","__unstableHasCustomAppender","__unstableResolvedAssets","__unstableIsBlockBasedTheme"],{globalStylesDataKey:qr,globalStylesLinksDataKey:Qr,selectBlockPatternsKey:Xr,reusableBlocksSelectKey:Jr,sectionRootClientIdKey:ea}=$(h.privateApis);const ta=function(e,t,s,o){var n,i,r,a;const l=(0,p.useViewportMatch)("medium"),{allowRightClickOverrides:m,blockTypes:g,focusMode:_,hasFixedToolbar:f,isDistractionFree:b,keepCaretInsideBlock:x,hasUploadPermissions:v,hiddenBlockTypes:S,canUseUnfilteredHTML:w,userCanCreatePages:C,pageOnFront:P,pageForPosts:j,userPatternCategories:E,restBlockPatternCategories:T,sectionRootClientId:B}=(0,c.useSelect)((e=>{var n;const{canUser:i,getRawEntityRecord:r,getEntityRecord:a,getUserPatternCategories:c,getBlockPatternCategories:u}=e(d.store),{get:p}=e(k.store),{getBlockTypes:m}=e(y.store),{getBlocksByName:g,getBlockAttributes:_}=e(h.store),f=i("read",{kind:"root",name:"site"})?a("root","site"):void 0;return{allowRightClickOverrides:p("core","allowRightClickOverrides"),blockTypes:m(),canUseUnfilteredHTML:r("postType",t,s)?._links?.hasOwnProperty("wp:action-unfiltered-html"),focusMode:p("core","focusMode"),hasFixedToolbar:p("core","fixedToolbar")||!l,hiddenBlockTypes:p("core","hiddenBlockTypes"),isDistractionFree:p("core","distractionFree"),keepCaretInsideBlock:p("core","keepCaretInsideBlock"),hasUploadPermissions:null===(n=i("create",{kind:"root",name:"media"}))||void 0===n||n,userCanCreatePages:i("create",{kind:"postType",name:"page"}),pageOnFront:f?.page_on_front,pageForPosts:f?.page_for_posts,userPatternCategories:c(),restBlockPatternCategories:u(),sectionRootClientId:"template-locked"===o?null!==(x=g("core/post-content")?.[0])&&void 0!==x?x:"":null!==(b=g("core/group").find((e=>"main"===_(e)?.tagName)))&&void 0!==b?b:""};var b,x}),[t,s,l,o]),{merged:I}=Wr(),N=null!==(n=I.styles)&&void 0!==n?n:Zr,D=null!==(i=I._links)&&void 0!==i?i:Zr,A=null!==(r=e.__experimentalAdditionalBlockPatterns)&&void 0!==r?r:e.__experimentalBlockPatterns,R=null!==(a=e.__experimentalAdditionalBlockPatternCategories)&&void 0!==a?a:e.__experimentalBlockPatternCategories,M=(0,u.useMemo)((()=>[...A||[]].filter((({postTypes:e})=>!e||Array.isArray(e)&&e.includes(t)))),[A,t]),L=(0,u.useMemo)((()=>[...R||[],...T||[]].filter(((e,t,s)=>t===s.findIndex((t=>e.name===t.name))))),[R,T]),{undo:O,setIsInserterOpened:F}=(0,c.useDispatch)(Ac),{saveEntityRecord:V}=(0,c.useDispatch)(d.store),z=(0,u.useCallback)((e=>C?V("postType","page",e):Promise.reject({message:(0,fs.__)("You do not have permission to create Pages.")})),[V,C]),U=(0,u.useMemo)((()=>{if(S&&S.length>0){return(!0===e.allowedBlockTypes?g.map((({name:e})=>e)):e.allowedBlockTypes||[]).filter((e=>!S.includes(e)))}return e.allowedBlockTypes}),[e.allowedBlockTypes,S,g]),H=!1===e.focusMode;return(0,u.useMemo)((()=>{const s={...Object.fromEntries(Object.entries(e).filter((([e])=>Kr.includes(e)))),[qr]:N,[Qr]:D,allowedBlockTypes:U,allowRightClickOverrides:m,focusMode:_&&!H,hasFixedToolbar:f,isDistractionFree:b,keepCaretInsideBlock:x,mediaUpload:v?Mr:void 0,mediaSideload:v?Or:void 0,__experimentalBlockPatterns:M,[Xr]:e=>{const{hasFinishedResolution:s,getBlockPatternsForPostType:o}=$(e(d.store)),n=o(t);return s("getBlockPatterns")?n:void 0},[Jr]:Yr,__experimentalBlockPatternCategories:L,__experimentalUserPatternCategories:E,__experimentalFetchLinkSuggestions:(t,s)=>(0,d.__experimentalFetchLinkSuggestions)(t,s,e),inserterMediaCategories:jr,__experimentalFetchRichUrlData:d.__experimentalFetchUrlData,__experimentalCanUserUseUnfilteredHTML:w,__experimentalUndo:O,outlineMode:!b&&"wp_template"===t,__experimentalCreatePageEntity:z,__experimentalUserCanCreatePages:C,pageOnFront:P,pageForPosts:j,__experimentalPreferPatternsOnRoot:"wp_template"===t,templateLock:"wp_navigation"===t?"insert":e.templateLock,template:"wp_navigation"===t?[["core/navigation",{},[]]]:e.template,__experimentalSetIsInserterOpened:F,[ea]:B,editorTool:"post-only"===o&&"wp_template"!==t?"edit":void 0};return s}),[U,m,_,H,f,b,x,e,v,E,M,L,w,O,z,C,P,j,t,F,B,N,D,o])},sa=["core/post-title","core/post-featured-image","core/post-content"];function oa(){const e=(0,u.useMemo)((()=>[...(0,m.applyFilters)("editor.postContentBlockTypes",sa)]),[]),t=(0,c.useSelect)((t=>{const{getPostBlocksByName:s}=$(t(Ac));return s(e)}),[e]);return t}function na(){const e=oa(),{templateParts:t,isNavigationMode:s}=(0,c.useSelect)((e=>{const{getBlocksByName:t,isNavigationMode:s}=e(h.store);return{templateParts:t("core/template-part"),isNavigationMode:s()}}),[]),o=(0,c.useSelect)((e=>{const{getBlockOrder:s}=e(h.store);return t.flatMap((e=>s(e)))}),[t]),n=(0,c.useRegistry)();return(0,u.useEffect)((()=>{const{setBlockEditingMode:e,unsetBlockEditingMode:t}=n.dispatch(h.store);return e("","disabled"),()=>{t("")}}),[n]),(0,u.useEffect)((()=>{const{setBlockEditingMode:t,unsetBlockEditingMode:s}=n.dispatch(h.store);return n.batch((()=>{for(const s of e)t(s,"contentOnly")})),()=>{n.batch((()=>{for(const t of e)s(t)}))}}),[e,n]),(0,u.useEffect)((()=>{const{setBlockEditingMode:e,unsetBlockEditingMode:o}=n.dispatch(h.store);return n.batch((()=>{if(!s)for(const s of t)e(s,"contentOnly")})),()=>{n.batch((()=>{if(!s)for(const e of t)o(e)}))}}),[t,s,n]),(0,u.useEffect)((()=>{const{setBlockEditingMode:e,unsetBlockEditingMode:t}=n.dispatch(h.store);return n.batch((()=>{for(const t of o)e(t,"disabled")})),()=>{n.batch((()=>{for(const e of o)t(e)}))}}),[o,n]),null}function ia(){const e=(0,c.useSelect)((e=>e(h.store).getBlockOrder()?.[0]),[]),{setBlockEditingMode:t,unsetBlockEditingMode:s}=(0,c.useDispatch)(h.store);(0,u.useEffect)((()=>{if(e)return t(e,"contentOnly"),()=>{s(e)}}),[e,s,t])}const ra=["wp_block","wp_template","wp_template_part"];const aa=(0,L.jsxs)(M.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:[(0,L.jsx)(M.Path,{d:"m16 15.5h-8v-1.5h8zm-7.5-2.5h-2v-2h2zm3 0h-2v-2h2zm3 0h-2v-2h2zm3 0h-2v-2h2zm-9-3h-2v-2h2zm3 0h-2v-2h2zm3 0h-2v-2h2zm3 0h-2v-2h2z"}),(0,L.jsx)(M.Path,{d:"m18.5 6.5h-13a.5.5 0 0 0 -.5.5v9.5a.5.5 0 0 0 .5.5h13a.5.5 0 0 0 .5-.5v-9.5a.5.5 0 0 0 -.5-.5zm-13-1.5h13a2 2 0 0 1 2 2v9.5a2 2 0 0 1 -2 2h-13a2 2 0 0 1 -2-2v-9.5a2 2 0 0 1 2-2z"})]}),la=(0,L.jsx)(M.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,L.jsx)(M.Path,{d:"M3 6h11v1.5H3V6Zm3.5 5.5h11V13h-11v-1.5ZM21 17H10v1.5h11V17Z"})}),ca=(0,L.jsx)(M.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,L.jsx)(M.Path,{d:"M20.8 10.7l-4.3-4.3-1.1 1.1 4.3 4.3c.******* 0 .4l-4.3 4.3 1.1 1.1 4.3-4.3c.7-.8.7-1.9 0-2.6zM4.2 11.8l4.3-4.3-1-1-4.3 4.3c-.7.7-.7 1.8 0 2.5l4.3 4.3 1.1-1.1-4.3-4.3c-.2-.1-.2-.3-.1-.4z"})}),da=(0,L.jsx)(M.SVG,{width:"24",height:"24",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,L.jsx)(M.Path,{fillRule:"evenodd",clipRule:"evenodd",d:"M18 4H6c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zM8.5 18.5H6c-.3 0-.5-.2-.5-.5V6c0-.3.2-.5.5-.5h2.5v13zm10-.5c0 .3-.2.5-.5.5h-8v-13h8c.3 0 .5.2.5.5v12z"})}),ua=(0,L.jsx)(M.SVG,{width:"24",height:"24",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,L.jsx)(M.Path,{fillRule:"evenodd",clipRule:"evenodd",d:"M18 4H6c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm-4 14.5H6c-.3 0-.5-.2-.5-.5V6c0-.3.2-.5.5-.5h8v13zm4.5-.5c0 .3-.2.5-.5.5h-2.5v-13H18c.3 0 .5.2.5.5v12z"})}),pa=(0,L.jsx)(M.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,L.jsx)(M.Path,{d:"M19 8h-1V6h-5v2h-2V6H6v2H5c-1.1 0-2 .9-2 2v8c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2v-8c0-1.1-.9-2-2-2zm.5 10c0 .3-.2.5-.5.5H5c-.3 0-.5-.2-.5-.5v-8c0-.3.2-.5.5-.5h14c.3 0 .5.2.5.5v8z"})}),ma=(0,L.jsx)(M.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,L.jsx)(M.Path,{d:"M11.1 15.8H20v-1.5h-8.9v1.5zm0-8.6v1.5H20V7.2h-8.9zM6 13c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm0-7c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z"})}),ha=(0,L.jsx)(M.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,L.jsx)(M.Path,{d:"m19 7-3-3-8.5 8.5-1 4 4-1L19 7Zm-7 11.5H5V20h7v-1.5Z"})}),ga=(0,L.jsx)(M.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,L.jsx)(M.Path,{d:"M21.3 10.8l-5.6-5.6c-.7-.7-1.8-.7-2.5 0l-5.6 5.6c-.7.7-.7 1.8 0 2.5l5.6 5.6c.3.3.8.5 1.2.5s.9-.2 1.2-.5l5.6-5.6c.8-.7.8-1.9.1-2.5zm-1 1.4l-5.6 5.6c-.1.1-.3.1-.4 0l-5.6-5.6c-.1-.1-.1-.3 0-.4l5.6-5.6s.1-.1.2-.1.1 0 .2.1l5.6 5.6c.******* 0 .4zm-16.6-.4L10 5.5l-1-1-6.3 6.3c-.7.7-.7 1.8 0 2.5L9 19.5l1.1-1.1-6.3-6.3c-.2 0-.2-.2-.1-.3z"})}),_a=(0,L.jsxs)(M.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:[(0,L.jsx)(M.Path,{d:"M15.5 7.5h-7V9h7V7.5Zm-7 3.5h7v1.5h-7V11Zm7 3.5h-7V16h7v-1.5Z"}),(0,L.jsx)(M.Path,{d:"M17 4H7a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h10a2 2 0 0 0 2-2V6a2 2 0 0 0-2-2ZM7 5.5h10a.5.5 0 0 1 .5.5v12a.5.5 0 0 1-.5.5H7a.5.5 0 0 1-.5-.5V6a.5.5 0 0 1 .5-.5Z"})]}),fa=(0,L.jsx)(M.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,L.jsx)(M.Path,{d:"M15.1 4.8l-3-2.5V4c-4.4 0-8 3.6-8 8 0 3.7 2.5 6.9 6 7.7.3.1.6.1 1 .2l.2-1.5c-.4 0-.7-.1-1.1-.2l-.1.2v-.2c-2.6-.8-4.5-3.3-4.5-6.2 0-3.6 2.9-6.5 6.5-6.5v1.8l3-2.5zM20 11c-.2-1.4-.7-2.7-1.6-3.8l-1.2.8c.7.9 1.1 2 1.3 3.1L20 11zm-1.5 1.8c-.1.5-.2 1.1-.4 1.6s-.5 1-.8 1.5l1.2.9c.4-.5.8-1.1 1-1.8s.5-1.3.5-2l-1.5-.2zm-5.6 5.6l.2 1.5c1.4-.2 2.7-.7 3.8-1.6l-.9-1.1c-.9.7-2 1.1-3.1 1.2z"})}),ba=(0,L.jsx)(M.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,L.jsx)(M.Path,{d:"M12 4V2.2L9 4.8l3 2.5V5.5c3.6 0 6.5 2.9 6.5 6.5 0 2.9-1.9 5.3-4.5 6.2v.2l-.1-.2c-.4.1-.7.2-1.1.2l.2 1.5c.3 0 .6-.1 1-.2 3.5-.9 6-4 6-7.7 0-4.4-3.6-8-8-8zm-7.9 7l1.5.2c.1-1.2.5-2.3 1.2-3.2l-1.1-.9C4.8 8.2 4.3 9.6 4.1 11zm1.5 1.8l-1.5.2c.1.7.3 1.4.5 2 .3.7.6 1.3 1 1.8l1.2-.8c-.3-.5-.6-1-.8-1.5s-.4-1.1-.4-1.7zm1.5 5.5c1.1.9 2.4 1.4 3.8 1.6l.2-1.5c-1.1-.1-2.2-.5-3.1-1.2l-.9 1.1z"})}),ya=window.wp.commands,xa=(0,L.jsx)(M.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,L.jsx)(M.Path,{d:"M11.776 4.454a.25.25 0 01.448 0l2.069 4.192a.25.25 0 00.188.137l4.626.672a.25.25 0 01.139.426l-3.348 3.263a.25.25 0 00-.072.222l.79 4.607a.25.25 0 01-.362.263l-4.138-2.175a.25.25 0 00-.232 0l-4.138 2.175a.25.25 0 01-.363-.263l.79-4.607a.25.25 0 00-.071-.222L4.754 9.881a.25.25 0 01.139-.426l4.626-.672a.25.25 0 00.188-.137l2.069-4.192z"})}),va=(0,L.jsx)(M.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,L.jsx)(M.Path,{fillRule:"evenodd",d:"M9.706 8.646a.25.25 0 01-.188.137l-4.626.672a.25.25 0 00-.139.427l3.348 3.262a.25.25 0 01.072.222l-.79 4.607a.25.25 0 00.362.264l4.138-2.176a.25.25 0 01.233 0l4.137 2.175a.25.25 0 00.363-.263l-.79-4.607a.25.25 0 01.072-.222l3.347-3.262a.25.25 0 00-.139-.427l-4.626-.672a.25.25 0 01-.188-.137l-2.069-4.192a.25.25 0 00-.448 0L9.706 8.646zM12 7.39l-.948 1.921a1.75 1.75 0 01-1.317.957l-2.12.308 1.534 1.495c.412.402.6.982.503 1.55l-.362 2.11 1.896-.997a1.75 1.75 0 011.629 0l1.895.997-.362-2.11a1.75 1.75 0 01.504-1.55l1.533-1.495-2.12-.308a1.75 1.75 0 01-1.317-.957L12 7.39z",clipRule:"evenodd"})}),Sa=window.wp.viewport,wa=window.wp.plugins;function ka(e){return["core/edit-post","core/edit-site"].includes(e)?(w()(`${e} interface scope`,{alternative:"core interface scope",hint:"core/edit-post and core/edit-site are merging.",version:"6.6"}),"core"):e}function Ca(e,t){return"core"===e&&"edit-site/template"===t?(w()("edit-site/template sidebar",{alternative:"edit-post/document",version:"6.6"}),"edit-post/document"):"core"===e&&"edit-site/block-inspector"===t?(w()("edit-site/block-inspector sidebar",{alternative:"edit-post/block",version:"6.6"}),"edit-post/block"):t}const Pa=(e,t)=>({type:"SET_DEFAULT_COMPLEMENTARY_AREA",scope:e=ka(e),area:t=Ca(e,t)}),ja=(e,t)=>({registry:s,dispatch:o})=>{if(!t)return;e=ka(e),t=Ca(e,t);s.select(k.store).get(e,"isComplementaryAreaVisible")||s.dispatch(k.store).set(e,"isComplementaryAreaVisible",!0),o({type:"ENABLE_COMPLEMENTARY_AREA",scope:e,area:t})},Ea=e=>({registry:t})=>{e=ka(e);t.select(k.store).get(e,"isComplementaryAreaVisible")&&t.dispatch(k.store).set(e,"isComplementaryAreaVisible",!1)},Ta=(e,t)=>({registry:s})=>{if(!t)return;e=ka(e),t=Ca(e,t);const o=s.select(k.store).get(e,"pinnedItems");!0!==o?.[t]&&s.dispatch(k.store).set(e,"pinnedItems",{...o,[t]:!0})},Ba=(e,t)=>({registry:s})=>{if(!t)return;e=ka(e),t=Ca(e,t);const o=s.select(k.store).get(e,"pinnedItems");s.dispatch(k.store).set(e,"pinnedItems",{...o,[t]:!1})};function Ia(e,t){return function({registry:s}){w()("dispatch( 'core/interface' ).toggleFeature",{since:"6.0",alternative:"dispatch( 'core/preferences' ).toggle"}),s.dispatch(k.store).toggle(e,t)}}function Na(e,t,s){return function({registry:o}){w()("dispatch( 'core/interface' ).setFeatureValue",{since:"6.0",alternative:"dispatch( 'core/preferences' ).set"}),o.dispatch(k.store).set(e,t,!!s)}}function Da(e,t){return function({registry:s}){w()("dispatch( 'core/interface' ).setFeatureDefaults",{since:"6.0",alternative:"dispatch( 'core/preferences' ).setDefaults"}),s.dispatch(k.store).setDefaults(e,t)}}function Aa(e){return{type:"OPEN_MODAL",name:e}}function Ra(){return{type:"CLOSE_MODAL"}}const Ma=(0,c.createRegistrySelector)((e=>(t,s)=>{s=ka(s);const o=e(k.store).get(s,"isComplementaryAreaVisible");if(void 0!==o)return!1===o?null:t?.complementaryAreas?.[s]})),La=(0,c.createRegistrySelector)((e=>(t,s)=>{s=ka(s);const o=e(k.store).get(s,"isComplementaryAreaVisible"),n=t?.complementaryAreas?.[s];return o&&void 0===n})),Oa=(0,c.createRegistrySelector)((e=>(t,s,o)=>{var n;o=Ca(s=ka(s),o);const i=e(k.store).get(s,"pinnedItems");return null===(n=i?.[o])||void 0===n||n})),Fa=(0,c.createRegistrySelector)((e=>(t,s,o)=>(w()("select( 'core/interface' ).isFeatureActive( scope, featureName )",{since:"6.0",alternative:"select( 'core/preferences' ).get( scope, featureName )"}),!!e(k.store).get(s,o))));function Va(e,t){return e.activeModal===t}const za=(0,c.combineReducers)({complementaryAreas:function(e={},t){switch(t.type){case"SET_DEFAULT_COMPLEMENTARY_AREA":{const{scope:s,area:o}=t;return e[s]?e:{...e,[s]:o}}case"ENABLE_COMPLEMENTARY_AREA":{const{scope:s,area:o}=t;return{...e,[s]:o}}}return e},activeModal:function(e=null,t){switch(t.type){case"OPEN_MODAL":return t.name;case"CLOSE_MODAL":return null}return e}}),Ua=(0,c.createReduxStore)("core/interface",{reducer:za,actions:n,selectors:i});function Ha({as:e=Uo.Button,scope:t,identifier:s,icon:o,selectedIcon:n,name:i,shortcut:r,...a}){const l=e,d=(0,wa.usePluginContext)(),u=o||d.icon,p=s||`${d.name}/${i}`,m=(0,c.useSelect)((e=>e(Ua).getActiveComplementaryArea(t)===p),[p,t]),{enableComplementaryArea:h,disableComplementaryArea:g}=(0,c.useDispatch)(Ua);return(0,L.jsx)(l,{icon:n&&m?n:u,"aria-controls":p.replace("/",":"),"aria-checked":(_=a.role,["checkbox","option","radio","switch","menuitemcheckbox","menuitemradio","treeitem"].includes(_)?m:void 0),onClick:()=>{m?g(t):h(t,p)},shortcut:r,...a});var _}(0,c.register)(Ua);const Ga=({children:e,className:t,toggleButtonProps:s})=>{const o=(0,L.jsx)(Ha,{icon:Bn,...s});return(0,L.jsxs)("div",{className:Di("components-panel__header","interface-complementary-area-header",t),tabIndex:-1,children:[e,o]})},$a=()=>{};function Wa({name:e,as:t=Uo.Button,onClick:s,...o}){return(0,L.jsx)(Uo.Fill,{name:e,children:({onClick:e})=>(0,L.jsx)(t,{onClick:s||e?(...t)=>{(s||$a)(...t),(e||$a)(...t)}:void 0,...o})})}Wa.Slot=function({name:e,as:t=Uo.MenuGroup,fillProps:s={},bubblesVirtually:o,...n}){return(0,L.jsx)(Uo.Slot,{name:e,bubblesVirtually:o,fillProps:s,children:e=>{if(!u.Children.toArray(e).length)return null;const s=[];u.Children.forEach(e,(({props:{__unstableExplicitMenuItem:e,__unstableTarget:t}})=>{t&&e&&s.push(t)}));const o=u.Children.map(e,(e=>!e.props.__unstableExplicitMenuItem&&s.includes(e.props.__unstableTarget)?null:e));return(0,L.jsx)(t,{...n,children:o})}})};const Za=Wa,Ya=({__unstableExplicitMenuItem:e,__unstableTarget:t,...s})=>(0,L.jsx)(Uo.MenuItem,{...s});function Ka({scope:e,target:t,__unstableExplicitMenuItem:s,...o}){return(0,L.jsx)(Ha,{as:o=>(0,L.jsx)(Za,{__unstableExplicitMenuItem:s,__unstableTarget:`${e}/${t}`,as:Ya,name:`${e}/plugin-more-menu`,...o}),role:"menuitemcheckbox",selectedIcon:Ho,name:t,scope:e,...o})}function qa({scope:e,...t}){return(0,L.jsx)(Uo.Fill,{name:`PinnedItems/${e}`,...t})}qa.Slot=function({scope:e,className:t,...s}){return(0,L.jsx)(Uo.Slot,{name:`PinnedItems/${e}`,...s,children:e=>e?.length>0&&(0,L.jsx)("div",{className:Di(t,"interface-pinned-items"),children:e})})};const Qa=qa;const Xa={open:{width:280},closed:{width:0},mobileOpen:{width:"100vw"}};function Ja({activeArea:e,isActive:t,scope:s,children:o,className:n,id:i}){const r=(0,p.useReducedMotion)(),a=(0,p.useViewportMatch)("medium","<"),l=(0,p.usePrevious)(e),c=(0,p.usePrevious)(t),[,d]=(0,u.useState)({});(0,u.useEffect)((()=>{d({})}),[t]);const m={type:"tween",duration:r||a||l&&e&&e!==l?0:.3,ease:[.6,0,.4,1]};return(0,L.jsx)(Uo.Fill,{name:`ComplementaryArea/${s}`,children:(0,L.jsx)(Uo.__unstableAnimatePresence,{initial:!1,children:(c||t)&&(0,L.jsx)(Uo.__unstableMotion.div,{variants:Xa,initial:"closed",animate:a?"mobileOpen":"open",exit:"closed",transition:m,className:"interface-complementary-area__fill",children:(0,L.jsx)("div",{id:i,className:n,style:{width:a?"100vw":280},children:o})})})})}function el({children:e,className:t,closeLabel:s=(0,fs.__)("Close plugin"),identifier:o,header:n,headerClassName:i,icon:r,isPinnable:a=!0,panelClassName:l,scope:d,name:m,title:h,toggleShortcut:g,isActiveByDefault:_}){const f=(0,wa.usePluginContext)(),b=r||f.icon,y=o||`${f.name}/${m}`,[x,v]=(0,u.useState)(!1),{isLoading:S,isActive:w,isPinned:C,activeArea:P,isSmall:j,isLarge:E,showIconLabels:T}=(0,c.useSelect)((e=>{const{getActiveComplementaryArea:t,isComplementaryAreaLoading:s,isItemPinned:o}=e(Ua),{get:n}=e(k.store),i=t(d);return{isLoading:s(d),isActive:i===y,isPinned:o(d,y),activeArea:i,isSmall:e(Sa.store).isViewportMatch("< medium"),isLarge:e(Sa.store).isViewportMatch("large"),showIconLabels:n("core","showIconLabels")}}),[y,d]),B=(0,p.useViewportMatch)("medium","<");!function(e,t,s,o,n){const i=(0,u.useRef)(!1),r=(0,u.useRef)(!1),{enableComplementaryArea:a,disableComplementaryArea:l}=(0,c.useDispatch)(Ua);(0,u.useEffect)((()=>{o&&n&&!i.current?(l(e),r.current=!0):r.current&&!n&&i.current?(r.current=!1,a(e,t)):r.current&&s&&s!==t&&(r.current=!1),n!==i.current&&(i.current=n)}),[o,n,e,t,s,l,a])}(d,y,P,w,j);const{enableComplementaryArea:I,disableComplementaryArea:N,pinItem:D,unpinItem:A}=(0,c.useDispatch)(Ua);if((0,u.useEffect)((()=>{_&&void 0===P&&!j?I(d,y):void 0===P&&j&&N(d,y),v(!0)}),[P,_,d,y,j,I,N]),x)return(0,L.jsxs)(L.Fragment,{children:[a&&(0,L.jsx)(Qa,{scope:d,children:C&&(0,L.jsx)(Ha,{scope:d,identifier:y,isPressed:w&&(!T||E),"aria-expanded":w,"aria-disabled":S,label:h,icon:T?Ho:b,showTooltip:!T,variant:T?"tertiary":void 0,size:"compact",shortcut:g})}),m&&a&&(0,L.jsx)(Ka,{target:m,scope:d,icon:b,children:h}),(0,L.jsxs)(Ja,{activeArea:P,isActive:w,className:Di("interface-complementary-area",t),scope:d,id:y.replace("/",":"),children:[(0,L.jsx)(Ga,{className:i,closeLabel:s,onClose:()=>N(d),toggleButtonProps:{label:s,size:"compact",shortcut:g,scope:d,identifier:y},children:n||(0,L.jsxs)(L.Fragment,{children:[(0,L.jsx)("h2",{className:"interface-complementary-area-header__title",children:h}),a&&!B&&(0,L.jsx)(Uo.Button,{className:"interface-complementary-area__pin-unpin-item",icon:C?xa:va,label:C?(0,fs.__)("Unpin from toolbar"):(0,fs.__)("Pin to toolbar"),onClick:()=>(C?A:D)(d,y),isPressed:C,"aria-expanded":C,size:"compact"})]})}),(0,L.jsx)(Uo.Panel,{className:l,children:e})]})]})}el.Slot=function({scope:e,...t}){return(0,L.jsx)(Uo.Slot,{name:`ComplementaryArea/${e}`,...t})};const tl=el,sl=({isActive:e})=>((0,u.useEffect)((()=>{let e=!1;return document.body.classList.contains("sticky-menu")&&(e=!0,document.body.classList.remove("sticky-menu")),()=>{e&&document.body.classList.add("sticky-menu")}}),[]),(0,u.useEffect)((()=>(e?document.body.classList.add("is-fullscreen-mode"):document.body.classList.remove("is-fullscreen-mode"),()=>{e&&document.body.classList.remove("is-fullscreen-mode")})),[e]),null),ol=(0,u.forwardRef)((({children:e,className:t,ariaLabel:s,as:o="div",...n},i)=>(0,L.jsx)(o,{ref:i,className:Di("interface-navigable-region",t),"aria-label":s,role:"region",tabIndex:"-1",...n,children:e})));ol.displayName="NavigableRegion";const nl=ol,il={type:"tween",duration:.25,ease:[.6,0,.4,1]};const rl={hidden:{opacity:1,marginTop:-60},visible:{opacity:1,marginTop:0},distractionFreeHover:{opacity:1,marginTop:0,transition:{...il,delay:.2,delayChildren:.2}},distractionFreeHidden:{opacity:0,marginTop:-60},distractionFreeDisabled:{opacity:0,marginTop:0,transition:{...il,delay:.8,delayChildren:.8}}};const al=(0,u.forwardRef)((function({isDistractionFree:e,footer:t,header:s,editorNotices:o,sidebar:n,secondarySidebar:i,content:r,actions:a,labels:l,className:c},d){const[m,h]=(0,p.useResizeObserver)(),g=(0,p.useViewportMatch)("medium","<"),_={type:"tween",duration:(0,p.useReducedMotion)()?0:.25,ease:[.6,0,.4,1]};!function(e){(0,u.useEffect)((()=>{const t=document&&document.querySelector(`html:not(.${e})`);if(t)return t.classList.toggle(e),()=>{t.classList.toggle(e)}}),[e])}("interface-interface-skeleton__html-container");const f={...{header:(0,fs._x)("Header","header landmark area"),body:(0,fs.__)("Content"),secondarySidebar:(0,fs.__)("Block Library"),sidebar:(0,fs._x)("Settings","settings landmark area"),actions:(0,fs.__)("Publish"),footer:(0,fs.__)("Footer")},...l};return(0,L.jsxs)("div",{ref:d,className:Di(c,"interface-interface-skeleton",!!t&&"has-footer"),children:[(0,L.jsxs)("div",{className:"interface-interface-skeleton__editor",children:[(0,L.jsx)(Uo.__unstableAnimatePresence,{initial:!1,children:!!s&&(0,L.jsx)(nl,{as:Uo.__unstableMotion.div,className:"interface-interface-skeleton__header","aria-label":f.header,initial:e&&!g?"distractionFreeHidden":"hidden",whileHover:e&&!g?"distractionFreeHover":"visible",animate:e&&!g?"distractionFreeDisabled":"visible",exit:e&&!g?"distractionFreeHidden":"hidden",variants:rl,transition:_,children:s})}),e&&(0,L.jsx)("div",{className:"interface-interface-skeleton__header",children:o}),(0,L.jsxs)("div",{className:"interface-interface-skeleton__body",children:[(0,L.jsx)(Uo.__unstableAnimatePresence,{initial:!1,children:!!i&&(0,L.jsx)(nl,{className:"interface-interface-skeleton__secondary-sidebar",ariaLabel:f.secondarySidebar,as:Uo.__unstableMotion.div,initial:"closed",animate:"open",exit:"closed",variants:{open:{width:h.width},closed:{width:0}},transition:_,children:(0,L.jsxs)(Uo.__unstableMotion.div,{style:{position:"absolute",width:g?"100vw":"fit-content",height:"100%",left:0},variants:{open:{x:0},closed:{x:"-100%"}},transition:_,children:[m,i]})})}),(0,L.jsx)(nl,{className:"interface-interface-skeleton__content",ariaLabel:f.body,children:r}),!!n&&(0,L.jsx)(nl,{className:"interface-interface-skeleton__sidebar",ariaLabel:f.sidebar,children:n}),!!a&&(0,L.jsx)(nl,{className:"interface-interface-skeleton__actions",ariaLabel:f.actions,children:a})]})]}),!!t&&(0,L.jsx)(nl,{className:"interface-interface-skeleton__footer",ariaLabel:f.footer,children:t})]})})),{RenamePatternModal:ll}=$(ln.privateApis),cl="editor/pattern-rename";function dl(){const{record:e,postType:t}=(0,c.useSelect)((e=>{const{getCurrentPostType:t,getCurrentPostId:s}=e(Ac),{getEditedEntityRecord:o}=e(d.store),n=t();return{record:o("postType",n,s()),postType:n}}),[]),{closeModal:s}=(0,c.useDispatch)(Ua);return(0,c.useSelect)((e=>e(Ua).isModalActive(cl)))&&t===I?(0,L.jsx)(ll,{onClose:s,pattern:e}):null}const{DuplicatePatternModal:ul}=$(ln.privateApis),pl="editor/pattern-duplicate";function ml(){const{record:e,postType:t}=(0,c.useSelect)((e=>{const{getCurrentPostType:t,getCurrentPostId:s}=e(Ac),{getEditedEntityRecord:o}=e(d.store),n=t();return{record:o("postType",n,s()),postType:n}}),[]),{closeModal:s}=(0,c.useDispatch)(Ua);return(0,c.useSelect)((e=>e(Ua).isModalActive(pl)))&&t===I?(0,L.jsx)(ul,{onClose:s,onSuccess:()=>s(),pattern:e}):null}const{BlockRemovalWarningModal:hl}=$(h.privateApis),gl=["core/post-content","core/post-template","core/query"],_l=[{postTypes:["wp_template","wp_template_part"],callback(e){if(e.filter((({name:e})=>gl.includes(e))).length)return(0,fs._n)("Deleting this block will stop your post or page content from displaying on this template. It is not recommended.","Some of the deleted blocks will stop your post or page content from displaying on this template. It is not recommended.",e.length)}},{postTypes:["wp_block"],callback(e){if(e.filter((({attributes:e})=>e?.metadata?.bindings&&Object.values(e.metadata.bindings).some((e=>"core/pattern-overrides"===e.source)))).length)return(0,fs._n)("The deleted block allows instance overrides. Removing it may result in content not displaying where this pattern is used. Are you sure you want to proceed?","Some of the deleted blocks allow instance overrides. Removing them may result in content not displaying where this pattern is used. Are you sure you want to proceed?",e.length)}}];function fl(){const e=(0,c.useSelect)((e=>e(Ac).getCurrentPostType()),[]),t=(0,u.useMemo)((()=>_l.filter((t=>t.postTypes.includes(e)))),[e]);return hl&&t?(0,L.jsx)(hl,{rules:t}):null}function bl({blockPatterns:e,onChoosePattern:t}){const{editEntityRecord:s}=(0,c.useDispatch)(d.store),{postType:o,postId:n}=(0,c.useSelect)((e=>{const{getCurrentPostType:t,getCurrentPostId:s}=e(Ac);return{postType:t(),postId:s()}}),[]);return(0,L.jsx)(h.__experimentalBlockPatternsList,{blockPatterns:e,onClickPattern:(e,i)=>{s("postType",o,n,{blocks:i,content:({blocks:e=[]})=>(0,y.__unstableSerializeAndClean)(e)}),t()}})}function yl({onClose:e}){const[t,s]=(0,u.useState)(!0),{set:o}=(0,c.useDispatch)(k.store),n=function(){const{blockPatternsWithPostContentBlockType:e,postType:t}=(0,c.useSelect)((e=>{const{getPatternsByBlockTypes:t,getBlocksByName:s}=e(h.store),{getCurrentPostType:o,getRenderingMode:n}=e(Ac);return{blockPatternsWithPostContentBlockType:t("core/post-content","post-only"===n()?"":s("core/post-content")?.[0]),postType:o()}}),[]);return(0,u.useMemo)((()=>e?.length?e.filter((e=>"page"===t&&!e.postTypes||Array.isArray(e.postTypes)&&e.postTypes.includes(t))):[]),[t,e])}();if(!(n.length>0))return null;function i(){e(),o("core","enableChoosePatternModal",t)}return(0,L.jsxs)(Uo.Modal,{className:"editor-start-page-options__modal",title:(0,fs.__)("Choose a pattern"),isFullScreen:!0,onRequestClose:i,children:[(0,L.jsx)("div",{className:"editor-start-page-options__modal-content",children:(0,L.jsx)(bl,{blockPatterns:n,onChoosePattern:i})}),(0,L.jsx)(Uo.Flex,{className:"editor-start-page-options__modal__actions",justify:"flex-end",expanded:!1,children:(0,L.jsx)(Uo.FlexItem,{children:(0,L.jsx)(Uo.ToggleControl,{__nextHasNoMarginBottom:!0,checked:t,label:(0,fs.__)("Show starter patterns"),help:(0,fs.__)("Shows starter patterns when creating a new page."),onChange:e=>{s(e)}})})})]})}function xl(){const[e,t]=(0,u.useState)(!1),{isEditedPostDirty:s,isEditedPostEmpty:o}=(0,c.useSelect)(Ac),{isModalActive:n}=(0,c.useSelect)(Ua),{enabled:i,postId:r}=(0,c.useSelect)((e=>{const{getCurrentPostId:t,getCurrentPostType:s}=e(Ac),o=e(k.store).get("core","enableChoosePatternModal");return{postId:t(),enabled:o&&T!==s()}}),[]);return(0,u.useEffect)((()=>{const e=!s()&&o(),r=n("editor/preferences");i&&e&&!r&&t(!0)}),[i,r,s,o,n]),e?(0,L.jsx)(yl,{onClose:()=>t(!1)}):null}const vl=window.wp.keyboardShortcuts,Sl=[{keyCombination:{modifier:"primary",character:"b"},description:(0,fs.__)("Make the selected text bold.")},{keyCombination:{modifier:"primary",character:"i"},description:(0,fs.__)("Make the selected text italic.")},{keyCombination:{modifier:"primary",character:"k"},description:(0,fs.__)("Convert the selected text into a link.")},{keyCombination:{modifier:"primaryShift",character:"k"},description:(0,fs.__)("Remove a link.")},{keyCombination:{character:"[["},description:(0,fs.__)("Insert a link to a post or page.")},{keyCombination:{modifier:"primary",character:"u"},description:(0,fs.__)("Underline the selected text.")},{keyCombination:{modifier:"access",character:"d"},description:(0,fs.__)("Strikethrough the selected text.")},{keyCombination:{modifier:"access",character:"x"},description:(0,fs.__)("Make the selected text inline code.")},{keyCombination:{modifier:"access",character:"0"},aliases:[{modifier:"access",character:"7"}],description:(0,fs.__)("Convert the current heading to a paragraph.")},{keyCombination:{modifier:"access",character:"1-6"},description:(0,fs.__)("Convert the current paragraph or heading to a heading of level 1 to 6.")},{keyCombination:{modifier:"primaryShift",character:"SPACE"},description:(0,fs.__)("Add non breaking space.")}],wl=window.wp.keycodes;function kl({keyCombination:e,forceAriaLabel:t}){const s=e.modifier?wl.displayShortcutList[e.modifier](e.character):e.character,o=e.modifier?wl.shortcutAriaLabel[e.modifier](e.character):e.character;return(0,L.jsx)("kbd",{className:"editor-keyboard-shortcut-help-modal__shortcut-key-combination","aria-label":t||o,children:(Array.isArray(s)?s:[s]).map(((e,t)=>"+"===e?(0,L.jsx)(u.Fragment,{children:e},t):(0,L.jsx)("kbd",{className:"editor-keyboard-shortcut-help-modal__shortcut-key",children:e},t)))})}const Cl=function({description:e,keyCombination:t,aliases:s=[],ariaLabel:o}){return(0,L.jsxs)(L.Fragment,{children:[(0,L.jsx)("div",{className:"editor-keyboard-shortcut-help-modal__shortcut-description",children:e}),(0,L.jsxs)("div",{className:"editor-keyboard-shortcut-help-modal__shortcut-term",children:[(0,L.jsx)(kl,{keyCombination:t,forceAriaLabel:o}),s.map(((e,t)=>(0,L.jsx)(kl,{keyCombination:e,forceAriaLabel:o},t)))]})]})};const Pl=function({name:e}){const{keyCombination:t,description:s,aliases:o}=(0,c.useSelect)((t=>{const{getShortcutKeyCombination:s,getShortcutDescription:o,getShortcutAliases:n}=t(vl.store);return{keyCombination:s(e),aliases:n(e),description:o(e)}}),[e]);return t?(0,L.jsx)(Cl,{keyCombination:t,description:s,aliases:o}):null},jl="editor/keyboard-shortcut-help",El=({shortcuts:e})=>(0,L.jsx)("ul",{className:"editor-keyboard-shortcut-help-modal__shortcut-list",role:"list",children:e.map(((e,t)=>(0,L.jsx)("li",{className:"editor-keyboard-shortcut-help-modal__shortcut",children:"string"==typeof e?(0,L.jsx)(Pl,{name:e}):(0,L.jsx)(Cl,{...e})},t)))}),Tl=({title:e,shortcuts:t,className:s})=>(0,L.jsxs)("section",{className:Di("editor-keyboard-shortcut-help-modal__section",s),children:[!!e&&(0,L.jsx)("h2",{className:"editor-keyboard-shortcut-help-modal__section-title",children:e}),(0,L.jsx)(El,{shortcuts:t})]}),Bl=({title:e,categoryName:t,additionalShortcuts:s=[]})=>{const o=(0,c.useSelect)((e=>e(vl.store).getCategoryShortcuts(t)),[t]);return(0,L.jsx)(Tl,{title:e,shortcuts:o.concat(s)})};const Il=function(){const e=(0,c.useSelect)((e=>e(Ua).isModalActive(jl)),[]),{openModal:t,closeModal:s}=(0,c.useDispatch)(Ua),o=()=>{e?s():t(jl)};return(0,vl.useShortcut)("core/editor/keyboard-shortcuts",o),e?(0,L.jsxs)(Uo.Modal,{className:"editor-keyboard-shortcut-help-modal",title:(0,fs.__)("Keyboard shortcuts"),closeButtonLabel:(0,fs.__)("Close"),onRequestClose:o,children:[(0,L.jsx)(Tl,{className:"editor-keyboard-shortcut-help-modal__main-shortcuts",shortcuts:["core/editor/keyboard-shortcuts"]}),(0,L.jsx)(Bl,{title:(0,fs.__)("Global shortcuts"),categoryName:"global"}),(0,L.jsx)(Bl,{title:(0,fs.__)("Selection shortcuts"),categoryName:"selection"}),(0,L.jsx)(Bl,{title:(0,fs.__)("Block shortcuts"),categoryName:"block",additionalShortcuts:[{keyCombination:{character:"/"},description:(0,fs.__)("Change the block type after adding a new paragraph."),ariaLabel:(0,fs.__)("Forward-slash")}]}),(0,L.jsx)(Tl,{title:(0,fs.__)("Text formatting"),shortcuts:Sl}),(0,L.jsx)(Bl,{title:(0,fs.__)("List View shortcuts"),categoryName:"list-view"})]}):null};function Nl({clientId:e,onClose:t}){const s=oa(),{entity:o,onNavigateToEntityRecord:n,canEditTemplates:i}=(0,c.useSelect)((t=>{const{getBlockParentsByBlockName:o,getSettings:n,getBlockAttributes:i,getBlockParents:r}=t(h.store),{getCurrentTemplateId:a,getRenderingMode:l}=t(Ac),c=o(e,"core/block",!0)[0];let u;if(c?u=t(d.store).getEntityRecord("postType","wp_block",i(c).ref):"template-locked"!==l()||r(e).some((e=>s.includes(e)))||(u=t(d.store).getEntityRecord("postType","wp_template",a())),!u)return{};return{canEditTemplates:t(d.store).canUser("create",{kind:"postType",name:"wp_template"}),entity:u,onNavigateToEntityRecord:n().onNavigateToEntityRecord}}),[e,s]);if(!o)return(0,L.jsx)(Dl,{clientId:e,onClose:t});const r="wp_block"===o.type;let a=r?(0,fs.__)("Edit the pattern to move, delete, or make further changes to this block."):(0,fs.__)("Edit the template to move, delete, or make further changes to this block.");return i||(a=(0,fs.__)("Only users with permissions to edit the template can move or delete this block")),(0,L.jsxs)(L.Fragment,{children:[(0,L.jsx)(h.__unstableBlockSettingsMenuFirstItem,{children:(0,L.jsx)(Uo.MenuItem,{onClick:()=>{n({postId:o.id,postType:o.type})},disabled:!i,children:r?(0,fs.__)("Edit pattern"):(0,fs.__)("Edit template")})}),(0,L.jsx)(Uo.__experimentalText,{variant:"muted",as:"p",className:"editor-content-only-settings-menu__description",children:a})]})}function Dl({clientId:e,onClose:t}){const{contentLockingParent:s}=(0,c.useSelect)((t=>{const{getContentLockingParent:s}=$(t(h.store));return{contentLockingParent:s(e)}}),[e]),o=(0,h.useBlockDisplayInformation)(s),n=(0,c.useDispatch)(h.store);if(!o?.title)return null;const{modifyContentLockBlock:i}=$(n);return(0,L.jsxs)(L.Fragment,{children:[(0,L.jsx)(h.__unstableBlockSettingsMenuFirstItem,{children:(0,L.jsx)(Uo.MenuItem,{onClick:()=>{i(s),t()},children:(0,fs._x)("Unlock","Unlock content locked blocks")})}),(0,L.jsx)(Uo.__experimentalText,{variant:"muted",as:"p",className:"editor-content-only-settings-menu__description",children:(0,fs.__)("Temporarily unlock the parent block to edit, delete or make further changes to this block.")})]})}function Al(){return(0,L.jsx)(h.BlockSettingsMenuControls,{children:({selectedClientIds:e,onClose:t})=>1===e.length&&(0,L.jsx)(Nl,{clientId:e[0],onClose:t})})}function Rl(e){const{slug:t,patterns:s}=(0,c.useSelect)((e=>{const{getCurrentPostType:t,getCurrentPostId:s}=e(Ac),{getEntityRecord:o,getBlockPatterns:n}=e(d.store),i=s();return{slug:o("postType",t(),i).slug,patterns:n()}}),[]),o=(0,c.useSelect)((e=>e(d.store).getCurrentTheme().stylesheet));return(0,u.useMemo)((()=>[{name:"fallback",blocks:(0,y.parse)(e),title:(0,fs.__)("Fallback content")},...s.filter((e=>Array.isArray(e.templateTypes)&&e.templateTypes.some((e=>t.startsWith(e))))).map((e=>({...e,blocks:(0,y.parse)(e.content).map((e=>function(e){return e.innerBlocks.find((e=>"core/template-part"===e.name))&&(e.innerBlocks=e.innerBlocks.map((e=>("core/template-part"===e.name&&void 0===e.attributes.theme&&(e.attributes.theme=o),e)))),"core/template-part"===e.name&&void 0===e.attributes.theme&&(e.attributes.theme=o),e}(e)))})))]),[e,t,s])}function Ml({fallbackContent:e,onChoosePattern:t,postType:s}){const[,,o]=(0,d.useEntityBlockEditor)("postType",s),n=Rl(e);return(0,L.jsx)(h.__experimentalBlockPatternsList,{blockPatterns:n,onClickPattern:(e,s)=>{o(s,{selection:void 0}),t()}})}function Ll({slug:e,isCustom:t,onClose:s,postType:o}){const n=function(e,t=!1){return(0,c.useSelect)((s=>{const{getEntityRecord:o,getDefaultTemplateId:n}=s(d.store),i=n({slug:e,is_custom:t,ignore_empty:!0});return i?o("postType",T,i)?.content?.raw:void 0}),[e,t])}(e,t);return n?(0,L.jsxs)(Uo.Modal,{className:"editor-start-template-options__modal",title:(0,fs.__)("Choose a pattern"),closeLabel:(0,fs.__)("Cancel"),focusOnMount:"firstElement",onRequestClose:s,isFullScreen:!0,children:[(0,L.jsx)("div",{className:"editor-start-template-options__modal-content",children:(0,L.jsx)(Ml,{fallbackContent:n,slug:e,isCustom:t,postType:o,onChoosePattern:()=>{s()}})}),(0,L.jsx)(Uo.Flex,{className:"editor-start-template-options__modal__actions",justify:"flex-end",expanded:!1,children:(0,L.jsx)(Uo.FlexItem,{children:(0,L.jsx)(Uo.Button,{__next40pxDefaultSize:!0,variant:"tertiary",onClick:s,children:(0,fs.__)("Skip")})})})]}):null}function Ol(){const[e,t]=(0,u.useState)(!1),{shouldOpenModal:s,slug:o,isCustom:n,postType:i,postId:r}=(0,c.useSelect)((e=>{const{getCurrentPostType:t,getCurrentPostId:s}=e(Ac),o=t(),n=s(),{getEditedEntityRecord:i,hasEditsForEntityRecord:r}=e(d.store),a=i("postType",o,n);return{shouldOpenModal:!r("postType",o,n)&&""===a.content&&T===o,slug:a.slug,isCustom:a.is_custom,postType:o,postId:n}}),[]);return(0,u.useEffect)((()=>{t(!1)}),[i,r]),!s||e?null:(0,L.jsx)(Ll,{slug:o,isCustom:n,postType:i,onClose:()=>t(!0)})}function Fl(){const e=(0,c.useSelect)((e=>{const{richEditingEnabled:t,codeEditingEnabled:s}=e(Ac).getEditorSettings();return!t||!s}),[]),{getBlockSelectionStart:t}=(0,c.useSelect)(h.store),{getActiveComplementaryArea:s}=(0,c.useSelect)(Ua),{enableComplementaryArea:o,disableComplementaryArea:n}=(0,c.useDispatch)(Ua),{redo:i,undo:r,savePost:a,setIsListViewOpened:l,switchEditorMode:d,toggleDistractionFree:u}=(0,c.useDispatch)(Ac),{isEditedPostDirty:p,isPostSavingLocked:m,isListViewOpened:g,getEditorMode:_}=(0,c.useSelect)(Ac);return(0,vl.useShortcut)("core/editor/toggle-mode",(()=>{d("visual"===_()?"text":"visual")}),{isDisabled:e}),(0,vl.useShortcut)("core/editor/toggle-distraction-free",(()=>{u()})),(0,vl.useShortcut)("core/editor/undo",(e=>{r(),e.preventDefault()})),(0,vl.useShortcut)("core/editor/redo",(e=>{i(),e.preventDefault()})),(0,vl.useShortcut)("core/editor/save",(e=>{e.preventDefault(),m()||p()&&a()})),(0,vl.useShortcut)("core/editor/toggle-list-view",(e=>{g()||(e.preventDefault(),l(!0))})),(0,vl.useShortcut)("core/editor/toggle-sidebar",(e=>{e.preventDefault();if(["edit-post/document","edit-post/block"].includes(s("core")))n("core");else{const e=t()?"edit-post/block":"edit-post/document";o("core",e)}})),null}function Vl({clientId:e,onClose:t}){const{getBlocks:s}=(0,c.useSelect)(h.store),{replaceBlocks:o}=(0,c.useDispatch)(h.store);return(0,c.useSelect)((t=>t(h.store).canRemoveBlock(e)),[e])?(0,L.jsx)(Uo.MenuItem,{onClick:()=>{o(e,s(e)),t()},children:(0,fs.__)("Detach")}):null}function zl({clientIds:e,blocks:t}){const[s,o]=(0,u.useState)(!1),{replaceBlocks:n}=(0,c.useDispatch)(h.store),{createSuccessNotice:i}=(0,c.useDispatch)(_s.store),{canCreate:r}=(0,c.useSelect)((e=>({canCreate:e(h.store).canInsertBlockType("core/template-part")})),[]);if(!r)return null;return(0,L.jsxs)(L.Fragment,{children:[(0,L.jsx)(Uo.MenuItem,{icon:z,onClick:()=>{o(!0)},"aria-expanded":s,"aria-haspopup":"dialog",children:(0,fs.__)("Create template part")}),s&&(0,L.jsx)(Jo,{closeModal:()=>{o(!1)},blocks:t,onCreate:async t=>{n(e,(0,y.createBlock)("core/template-part",{slug:t.slug,theme:t.theme})),i((0,fs.__)("Template part created."),{type:"snackbar"})}})]})}function Ul(){return(0,L.jsx)(h.BlockSettingsMenuControls,{children:({selectedClientIds:e,onClose:t})=>(0,L.jsx)(Hl,{clientIds:e,onClose:t})})}function Hl({clientIds:e,onClose:t}){const{blocks:s}=(0,c.useSelect)((t=>{const{getBlocksByClientId:s}=t(h.store);return{blocks:s(e)}}),[e]);return 1===s.length&&"core/template-part"===s[0]?.name?(0,L.jsx)(Vl,{clientId:e[0],onClose:t}):(0,L.jsx)(zl,{clientIds:e,blocks:s})}const{ExperimentalBlockEditorProvider:Gl}=$(h.privateApis),{PatternsMenuItems:$l}=$(ln.privateApis),Wl=()=>{},Zl=["wp_block","wp_navigation","wp_template_part"];const Yl=Sr((({post:e,settings:t,recovery:s,initialEdits:o,children:n,BlockEditorProviderComponent:i=Gl,__unstableTemplate:r})=>{const a=!!r,{editorSettings:l,selection:p,isReady:g,mode:_,defaultMode:f,postTypeEntities:b}=(0,c.useSelect)((t=>{const{getEditorSettings:s,getEditorSelection:o,getRenderingMode:n,__unstableIsEditorReady:i,getDefaultRenderingMode:r}=$(t(Ac)),{getEntitiesConfig:l}=t(d.store),c=n(),u=r(e.type),p="template-locked"===u?a:void 0!==u,m=void 0!==u;return{editorSettings:s(),isReady:i(),mode:m?c:void 0,defaultMode:p?u:void 0,selection:o(),postTypeEntities:"wp_template"===e.type?l("postType"):null}}),[e.type,a]),x=a&&"post-only"!==_,S=x?r:e,w=(0,u.useMemo)((()=>{const t={};if("wp_template"===e.type){if("page"===e.slug)t.postType="page";else if("single"===e.slug)t.postType="post";else if("single"===e.slug.split("-")[0]){const s=b?.map((e=>e.name))||[],o=e.slug.match(`^single-(${s.join("|")})(?:-.+)?$`);o&&(t.postType=o[1])}}else Zl.includes(S.type)&&!x||(t.postId=e.id,t.postType=e.type);return{...t,templateSlug:"wp_template"===S.type?S.slug:void 0}}),[x,e.id,e.type,e.slug,S.type,S.slug,b]),{id:C,type:P}=S,j=ta(l,P,C,_),[E,N,D]=function(e,t,s){const o="template-locked"===s?"template":"post",[n,i,r]=(0,d.useEntityBlockEditor)("postType",e.type,{id:e.id}),[a,l,c]=(0,d.useEntityBlockEditor)("postType",t?.type,{id:t?.id}),p=(0,u.useMemo)((()=>{if("wp_navigation"===e.type)return[(0,y.createBlock)("core/navigation",{ref:e.id,templateLock:!1})]}),[e.type,e.id]),m=(0,u.useMemo)((()=>p||("template"===o?a:n)),[p,o,a,n]);return t&&"template-locked"===s||"wp_navigation"===e.type?[m,Wl,Wl]:[m,"post"===o?i:l,"post"===o?r:c]}(e,r,_),{updatePostLock:A,setupEditor:R,updateEditorSettings:M,setCurrentTemplateId:O,setEditedPost:F,setRenderingMode:V}=$((0,c.useDispatch)(Ac)),{createWarningNotice:z}=(0,c.useDispatch)(_s.store);return(0,u.useLayoutEffect)((()=>{s||(A(t.postLock),R(e,o,t.template),t.autosave&&z((0,fs.__)("There is an autosave of this post that is more recent than the version below."),{id:"autosave-exists",actions:[{label:(0,fs.__)("View the autosave"),url:t.autosave.editLink}]}))}),[]),(0,u.useEffect)((()=>{F(e.type,e.id)}),[e.type,e.id,F]),(0,u.useEffect)((()=>{M(t)}),[t,M]),(0,u.useEffect)((()=>{O(r?.id)}),[r?.id,O]),(0,u.useEffect)((()=>{f&&V(f)}),[f,V]),function(e,t){(0,u.useEffect)((()=>((0,m.addFilter)("blockEditor.__unstableCanInsertBlockType","removeTemplatePartsFromInserter",((s,o)=>!(!ra.includes(e)&&"core/template-part"===o.name&&"post-only"===t)&&s)),(0,m.addFilter)("blockEditor.__unstableCanInsertBlockType","removePostContentFromInserter",((t,s,o,{getBlockParentsByBlockName:n})=>ra.includes(e)||"core/post-content"!==s.name?t:n(o,"core/query").length>0)),()=>{(0,m.removeFilter)("blockEditor.__unstableCanInsertBlockType","removeTemplatePartsFromInserter"),(0,m.removeFilter)("blockEditor.__unstableCanInsertBlockType","removePostContentFromInserter")})),[e,t])}(e.type,_),(0,ya.useCommandLoader)({name:"core/editor/edit-ui",hook:function(){const{editorMode:e,isListViewOpen:t,showBlockBreadcrumbs:s,isDistractionFree:o,isFocusMode:n,isPreviewMode:i,isViewable:r,isCodeEditingEnabled:a,isRichEditingEnabled:l,isPublishSidebarEnabled:u}=(0,c.useSelect)((e=>{var t,s;const{get:o}=e(k.store),{isListViewOpened:n,getCurrentPostType:i,getEditorSettings:r}=e(Ac),{getSettings:a}=e(h.store),{getPostType:l}=e(d.store);return{editorMode:null!==(t=o("core","editorMode"))&&void 0!==t?t:"visual",isListViewOpen:n(),showBlockBreadcrumbs:o("core","showBlockBreadcrumbs"),isDistractionFree:o("core","distractionFree"),isFocusMode:o("core","focusMode"),isPreviewMode:a().isPreviewMode,isViewable:null!==(s=l(i())?.viewable)&&void 0!==s&&s,isCodeEditingEnabled:r().codeEditingEnabled,isRichEditingEnabled:r().richEditingEnabled,isPublishSidebarEnabled:e(Ac).isPublishSidebarEnabled()}}),[]),{getActiveComplementaryArea:p}=(0,c.useSelect)(Ua),{toggle:m}=(0,c.useDispatch)(k.store),{createInfoNotice:g}=(0,c.useDispatch)(_s.store),{__unstableSaveForPreview:_,setIsListViewOpened:f,switchEditorMode:b,toggleDistractionFree:y,toggleSpotlightMode:x,toggleTopToolbar:S}=(0,c.useDispatch)(Ac),{openModal:w,enableComplementaryArea:C,disableComplementaryArea:P}=(0,c.useDispatch)(Ua),{getCurrentPostId:j}=(0,c.useSelect)(Ac),{isBlockBasedTheme:E,canCreateTemplate:T}=(0,c.useSelect)((e=>({isBlockBasedTheme:e(d.store).getCurrentTheme()?.is_block_theme,canCreateTemplate:e(d.store).canUser("create",{kind:"postType",name:"wp_template"})})),[]),B=a&&l;if(i)return{commands:[],isLoading:!1};const I=[];if(I.push({name:"core/open-shortcut-help",label:(0,fs.__)("Keyboard shortcuts"),icon:aa,callback:({close:e})=>{e(),w("editor/keyboard-shortcut-help")}}),I.push({name:"core/toggle-distraction-free",label:o?(0,fs.__)("Exit Distraction free"):(0,fs.__)("Enter Distraction free"),callback:({close:e})=>{y(),e()}}),I.push({name:"core/open-preferences",label:(0,fs.__)("Editor preferences"),callback:({close:e})=>{e(),w("editor/preferences")}}),I.push({name:"core/toggle-spotlight-mode",label:n?(0,fs.__)("Exit Spotlight mode"):(0,fs.__)("Enter Spotlight mode"),callback:({close:e})=>{x(),e()}}),I.push({name:"core/toggle-list-view",label:t?(0,fs.__)("Close List View"):(0,fs.__)("Open List View"),icon:la,callback:({close:e})=>{f(!t),e(),g(t?(0,fs.__)("List View off."):(0,fs.__)("List View on."),{id:"core/editor/toggle-list-view/notice",type:"snackbar"})}}),I.push({name:"core/toggle-top-toolbar",label:(0,fs.__)("Top toolbar"),callback:({close:e})=>{S(),e()}}),B&&I.push({name:"core/toggle-code-editor",label:"visual"===e?(0,fs.__)("Open code editor"):(0,fs.__)("Exit code editor"),icon:ca,callback:({close:t})=>{b("visual"===e?"text":"visual"),t()}}),I.push({name:"core/toggle-breadcrumbs",label:s?(0,fs.__)("Hide block breadcrumbs"):(0,fs.__)("Show block breadcrumbs"),callback:({close:e})=>{m("core","showBlockBreadcrumbs"),e(),g(s?(0,fs.__)("Breadcrumbs hidden."):(0,fs.__)("Breadcrumbs visible."),{id:"core/editor/toggle-breadcrumbs/notice",type:"snackbar"})}}),I.push({name:"core/open-settings-sidebar",label:(0,fs.__)("Show or hide the Settings panel."),icon:(0,fs.isRTL)()?da:ua,callback:({close:e})=>{const t=p("core");e(),"edit-post/document"===t?P("core"):C("core","edit-post/document")}}),I.push({name:"core/open-block-inspector",label:(0,fs.__)("Show or hide the Block settings panel"),icon:pa,callback:({close:e})=>{const t=p("core");e(),"edit-post/block"===t?P("core"):C("core","edit-post/block")}}),I.push({name:"core/toggle-publish-sidebar",label:u?(0,fs.__)("Disable pre-publish checks"):(0,fs.__)("Enable pre-publish checks"),icon:ma,callback:({close:e})=>{e(),m("core","isPublishSidebarEnabled"),g(u?(0,fs.__)("Pre-publish checks disabled."):(0,fs.__)("Pre-publish checks enabled."),{id:"core/editor/publish-sidebar/notice",type:"snackbar"})}}),r&&I.push({name:"core/preview-link",label:(0,fs.__)("Preview in a new tab"),icon:Fo,callback:async({close:e})=>{e();const t=j(),s=await _();window.open(s,`wp-preview-${t}`)}}),T&&E){const e=(0,v.getPath)(window.location.href)?.includes("site-editor.php");e||I.push({name:"core/go-to-site-editor",label:(0,fs.__)("Open Site Editor"),callback:({close:e})=>{e(),document.location="site-editor.php"}})}return{commands:I,isLoading:!1}}}),(0,ya.useCommandLoader)({name:"core/editor/contextual-commands",hook:function(){const{postType:e}=(0,c.useSelect)((e=>{const{getCurrentPostType:t}=e(Ac);return{postType:t()}}),[]),{openModal:t}=(0,c.useDispatch)(Ua),s=[];return e===I&&(s.push({name:"core/rename-pattern",label:(0,fs.__)("Rename pattern"),icon:ha,callback:({close:e})=>{t(cl),e()}}),s.push({name:"core/duplicate-pattern",label:(0,fs.__)("Duplicate pattern"),icon:ga,callback:({close:e})=>{t(pl),e()}})),{isLoading:!1,commands:s}},context:"entity-edit"}),(0,ya.useCommandLoader)({name:"core/editor/page-content-focus",hook:function(){const{onNavigateToEntityRecord:e,goBack:t,templateId:s,isPreviewMode:o}=(0,c.useSelect)((e=>{const{getRenderingMode:t,getEditorSettings:s,getCurrentTemplateId:o}=$(e(Ac)),n=s();return{isTemplateHidden:"post-only"===t(),onNavigateToEntityRecord:n.onNavigateToEntityRecord,getEditorSettings:s,goBack:n.onNavigateToPreviousEntityRecord,templateId:o(),isPreviewMode:n.isPreviewMode}}),[]),{editedRecord:n,hasResolved:i}=(0,d.useEntityRecord)("postType","wp_template",s);if(o)return{isLoading:!1,commands:[]};const r=[];return s&&i&&r.push({name:"core/switch-to-template-focus",label:(0,fs.sprintf)((0,fs.__)("Edit template: %s"),(0,Lo.decodeEntities)(n.title)),icon:W,callback:({close:t})=>{e({postId:s,postType:"wp_template"}),t()}}),t&&r.push({name:"core/switch-to-previous-entity",label:(0,fs.__)("Go back"),icon:_a,callback:({close:e})=>{t(),e()}}),{isLoading:!1,commands:r}},context:"entity-edit"}),(0,ya.useCommandLoader)({name:"core/edit-site/manipulate-document",hook:function(){const{postType:e,postId:t}=(0,c.useSelect)((e=>{const{getCurrentPostId:t,getCurrentPostType:s}=e(Ac);return{postType:s(),postId:t()}}),[]),{editedRecord:s,hasResolved:o}=(0,d.useEntityRecord)("postType",e,t),{revertTemplate:n}=$((0,c.useDispatch)(Ac));if(!o||![B,T].includes(e))return{isLoading:!0,commands:[]};const i=[];if(Oo(s)){const e=s.type===T?(0,fs.sprintf)((0,fs.__)("Reset template: %s"),(0,Lo.decodeEntities)(s.title)):(0,fs.sprintf)((0,fs.__)("Reset template part: %s"),(0,Lo.decodeEntities)(s.title));i.push({name:"core/reset-template",label:e,icon:(0,fs.isRTL)()?fa:ba,callback:({close:e})=>{n(s),e()}})}return{isLoading:!o,commands:i}}}),g&&_?(0,L.jsx)(d.EntityProvider,{kind:"root",type:"site",children:(0,L.jsx)(d.EntityProvider,{kind:"postType",type:e.type,id:e.id,children:(0,L.jsx)(h.BlockContextProvider,{value:w,children:(0,L.jsxs)(i,{value:E,onChange:D,onInput:N,selection:p,settings:j,useSubRegistry:!1,children:[n,!t.isPreviewMode&&(0,L.jsxs)(L.Fragment,{children:[(0,L.jsx)($l,{}),(0,L.jsx)(Ul,{}),(0,L.jsx)(Al,{}),"template-locked"===_&&(0,L.jsx)(na,{}),"wp_navigation"===P&&(0,L.jsx)(ia,{}),(0,L.jsx)(Fl,{}),(0,L.jsx)(Il,{}),(0,L.jsx)(fl,{}),(0,L.jsx)(xl,{}),(0,L.jsx)(Ol,{}),(0,L.jsx)(dl,{}),(0,L.jsx)(ml,{})]})]})})})}):null}));function Kl(e){return(0,L.jsx)(Yl,{...e,BlockEditorProviderComponent:h.BlockEditorProvider,children:e.children})}const ql=Kl,{useGlobalStyle:Ql}=$(h.privateApis);function Xl({template:e,post:t}){const[s="white"]=Ql("color.background"),[o]=(0,d.useEntityBlockEditor)("postType",t.type,{id:t.id}),[n]=(0,d.useEntityBlockEditor)("postType",e?.type,{id:e?.id}),i=e&&n?n:o,r=!i?.length;return(0,L.jsxs)("div",{className:"editor-fields-content-preview",style:{backgroundColor:s},children:[r&&(0,L.jsx)("span",{className:"editor-fields-content-preview__empty",children:(0,fs.__)("Empty content")}),!r&&(0,L.jsx)(h.BlockPreview.Async,{children:(0,L.jsx)(h.BlockPreview,{blocks:i})})]})}const Jl={type:"media",id:"content-preview",label:(0,fs.__)("Content preview"),render:function({item:e}){const{settings:t,template:s}=(0,c.useSelect)((t=>{var s;const{canUser:o,getPostType:n,getTemplateId:i,getEntityRecord:r}=$(t(d.store)),a=o("read",{kind:"postType",name:"wp_template"}),l=t(Ac).getEditorSettings(),c=l.supportsTemplateMode,u=null!==(s=n(e.type)?.viewable)&&void 0!==s&&s,p=c&&u&&a?i(e.type,e.id):null;return{settings:l,template:p?r("postType","wp_template",p):void 0}}),[e.type,e.id]);return(0,L.jsx)(Kl,{post:e,settings:t,__unstableTemplate:s,children:(0,L.jsx)(Xl,{template:s,post:e})})},enableSorting:!1},ec=Jl;function tc(e,t,s){return{type:"REGISTER_ENTITY_ACTION",kind:e,name:t,config:s}}function sc(e,t,s){return{type:"UNREGISTER_ENTITY_ACTION",kind:e,name:t,actionId:s}}function oc(e,t,s){return{type:"REGISTER_ENTITY_FIELD",kind:e,name:t,config:s}}function nc(e,t,s){return{type:"UNREGISTER_ENTITY_FIELD",kind:e,name:t,fieldId:s}}function ic(e,t){return{type:"SET_IS_READY",kind:e,name:t}}const rc=e=>async({registry:t})=>{if($(t.select(Ac)).isEntityReady("postType",e))return;$(t.dispatch(Ac)).setIsReady("postType",e);const s=await t.resolveSelect(d.store).getPostType(e),o=await t.resolveSelect(d.store).canUser("create",{kind:"postType",name:e}),n=await t.resolveSelect(d.store).getCurrentTheme(),i=[s.viewable?Vo:void 0,s.supports?.revisions?zo:void 0,void 0,"wp_template_part"===s.slug&&o&&n?.is_block_theme?an:void 0,o&&"wp_block"===s.slug?hn:void 0,s.supports?.title?fn:void 0,s.supports?.["page-attributes"]?Fn:void 0,"wp_block"===s.slug?mi:void 0,gi,bi,wi,Ci,ji].filter(Boolean),r=[s.supports?.thumbnail&&n?.theme_supports?.["post-thumbnails"]&&Ii,s.supports?.author&&Li,$i,Yi,er,s.supports?.["page-attributes"]&&rr,s.supports?.comments&&ar,dr,ur,s.supports?.editor&&s.viewable&&ec].filter(Boolean);if(s.supports?.title){let t;t="page"===e?gr:"wp_template"===e?_r:"wp_block"===e?xr:vr,r.push(t)}t.batch((()=>{i.forEach((s=>{$(t.dispatch(Ac)).registerEntityAction("postType",e,s)})),r.forEach((s=>{$(t.dispatch(Ac)).registerEntityField("postType",e,s)}))})),(0,m.doAction)("core.registerPostTypeSchema",e)};function ac(e){return{type:"SET_CURRENT_TEMPLATE_ID",id:e}}const lc=e=>async({select:t,dispatch:s,registry:o})=>{const n=await o.dispatch(d.store).saveEntityRecord("postType","wp_template",e);return o.dispatch(d.store).editEntityRecord("postType",t.getCurrentPostType(),t.getCurrentPostId(),{template:n.slug}),o.dispatch(_s.store).createSuccessNotice((0,fs.__)("Custom template created. You're in template mode now."),{type:"snackbar",actions:[{label:(0,fs.__)("Go back"),onClick:()=>s.setRenderingMode(t.getEditorSettings().defaultRenderingMode)}]}),n},cc=e=>({registry:t})=>{var s;const o=(null!==(s=t.select(k.store).get("core","hiddenBlockTypes"))&&void 0!==s?s:[]).filter((t=>!(Array.isArray(e)?e:[e]).includes(t)));t.dispatch(k.store).set("core","hiddenBlockTypes",o)},dc=e=>({registry:t})=>{var s;const o=null!==(s=t.select(k.store).get("core","hiddenBlockTypes"))&&void 0!==s?s:[],n=new Set([...o,...Array.isArray(e)?e:[e]]);t.dispatch(k.store).set("core","hiddenBlockTypes",[...n])},uc=({onSave:e,dirtyEntityRecords:t=[],entitiesToSkip:s=[],close:o}={})=>({registry:n})=>{const i=[{kind:"postType",name:"wp_navigation"}],r="site-editor-save-success",a=n.select(d.store).getEntityRecord("root","__unstableBase")?.home;n.dispatch(_s.store).removeNotice(r);const l=t.filter((({kind:e,name:t,key:o,property:n})=>!s.some((s=>s.kind===e&&s.name===t&&s.key===o&&s.property===n))));o?.(l);const c=[],u=[];l.forEach((({kind:e,name:t,key:s,property:o})=>{"root"===e&&"site"===t?c.push(o):(i.some((s=>s.kind===e&&s.name===t))&&n.dispatch(d.store).editEntityRecord(e,t,s,{status:"publish"}),u.push(n.dispatch(d.store).saveEditedEntityRecord(e,t,s)))})),c.length&&u.push(n.dispatch(d.store).__experimentalSaveSpecifiedEntityEdits("root","site",void 0,c)),n.dispatch(h.store).__unstableMarkLastChangeAsPersistent(),Promise.all(u).then((t=>e?e(t):t)).then((e=>{e.some((e=>void 0===e))?n.dispatch(_s.store).createErrorNotice((0,fs.__)("Saving failed.")):n.dispatch(_s.store).createSuccessNotice((0,fs.__)("Site updated."),{type:"snackbar",id:r,actions:[{label:(0,fs.__)("View site"),url:a}]})})).catch((e=>n.dispatch(_s.store).createErrorNotice(`${(0,fs.__)("Saving failed.")} ${e}`)))},pc=(e,{allowUndo:t=!0}={})=>async({registry:s})=>{const o="edit-site-template-reverted";if(s.dispatch(_s.store).removeNotice(o),Oo(e))try{const n=s.select(d.store).getEntityConfig("postType",e.type);if(!n)return void s.dispatch(_s.store).createErrorNotice((0,fs.__)("The editor has encountered an unexpected error. Please reload."),{type:"snackbar"});const i=(0,v.addQueryArgs)(`${n.baseURL}/${e.id}`,{context:"edit",source:e.origin}),r=await gs()({path:i});if(!r)return void s.dispatch(_s.store).createErrorNotice((0,fs.__)("The editor has encountered an unexpected error. Please reload."),{type:"snackbar"});const a=({blocks:e=[]})=>(0,y.__unstableSerializeAndClean)(e),l=s.select(d.store).getEditedEntityRecord("postType",e.type,e.id);s.dispatch(d.store).editEntityRecord("postType",e.type,e.id,{content:a,blocks:l.blocks,source:"custom"},{undoIgnore:!0});const c=(0,y.parse)(r?.content?.raw);if(s.dispatch(d.store).editEntityRecord("postType",e.type,r.id,{content:a,blocks:c,source:"theme"}),t){const t=()=>{s.dispatch(d.store).editEntityRecord("postType",e.type,l.id,{content:a,blocks:l.blocks,source:"custom"})};s.dispatch(_s.store).createSuccessNotice((0,fs.__)("Template reset."),{type:"snackbar",id:o,actions:[{label:(0,fs.__)("Undo"),onClick:t}]})}}catch(e){const t=e.message&&"unknown_error"!==e.code?e.message:(0,fs.__)("Template revert failed. Please reload.");s.dispatch(_s.store).createErrorNotice(t,{type:"snackbar"})}else s.dispatch(_s.store).createErrorNotice((0,fs.__)("This template is not revertable."),{type:"snackbar"})},mc=e=>async({registry:t})=>{const s=e.every((e=>e?.has_theme_file)),o=await Promise.allSettled(e.map((e=>t.dispatch(d.store).deleteEntityRecord("postType",e.type,e.id,{force:!0},{throwOnError:!0}))));if(o.every((({status:e})=>"fulfilled"===e))){let o;if(1===e.length){let t;"string"==typeof e[0].title?t=e[0].title:"string"==typeof e[0].title?.rendered?t=e[0].title?.rendered:"string"==typeof e[0].title?.raw&&(t=e[0].title?.raw),o=s?(0,fs.sprintf)((0,fs.__)('"%s" reset.'),(0,Lo.decodeEntities)(t)):(0,fs.sprintf)((0,fs._x)('"%s" deleted.',"template part"),(0,Lo.decodeEntities)(t))}else o=s?(0,fs.__)("Items reset."):(0,fs.__)("Items deleted.");t.dispatch(_s.store).createSuccessNotice(o,{type:"snackbar",id:"editor-template-deleted-success"})}else{let e;if(1===o.length)e=o[0].reason?.message?o[0].reason.message:s?(0,fs.__)("An error occurred while reverting the item."):(0,fs.__)("An error occurred while deleting the item.");else{const t=new Set,n=o.filter((({status:e})=>"rejected"===e));for(const e of n)e.reason?.message&&t.add(e.reason.message);e=0===t.size?(0,fs.__)("An error occurred while deleting the items."):1===t.size?s?(0,fs.sprintf)((0,fs.__)("An error occurred while reverting the items: %s"),[...t][0]):(0,fs.sprintf)((0,fs.__)("An error occurred while deleting the items: %s"),[...t][0]):s?(0,fs.sprintf)((0,fs.__)("Some errors occurred while reverting the items: %s"),[...t].join(",")):(0,fs.sprintf)((0,fs.__)("Some errors occurred while deleting the items: %s"),[...t].join(","))}t.dispatch(_s.store).createErrorNotice(e,{type:"snackbar"})}},hc=e=>({select:t,registry:s})=>{var o;const n=t.getCurrentPostType(),i=s.select(d.store).getCurrentTheme()?.stylesheet,r=null!==(o=s.select(k.store).get("core","renderingModes")?.[i])&&void 0!==o?o:{};if(r[n]===e)return;const a={[i]:{...r,[n]:e}};s.dispatch(k.store).set("core","renderingModes",a)};var gc=s(5215),_c=s.n(gc);const fc=(0,L.jsx)(M.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,L.jsx)(M.Path,{d:"M12 4c-4.4 0-8 3.6-8 8s3.6 8 8 8 8-3.6 8-8-3.6-8-8-8zm0 14.5c-3.6 0-6.5-2.9-6.5-6.5S8.4 5.5 12 5.5s6.5 2.9 6.5 6.5-2.9 6.5-6.5 6.5zM9 16l4.5-3L15 8.4l-4.5 3L9 16z"})}),bc=(0,L.jsx)(M.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,L.jsx)(M.Path,{d:"M17.8 2l-.9.3c-.1 0-3.6 1-5.2 2.1C10 5.5 9.3 6.5 8.9 7.1c-.6.9-1.7 4.7-1.7 6.3l-.9 2.3c-.2.4 0 .8.4 1 .1 0 .2.1.3.1.3 0 .6-.2.7-.5l.6-1.5c.3 0 .7-.1 1.2-.2.7-.1 1.4-.3 2.2-.5.8-.2 1.6-.5 2.4-.8.7-.3 1.4-.7 1.9-1.2s.8-1.2 1-1.9c.2-.7.3-1.6.4-2.4.1-.8.1-1.7.2-2.5 0-.8.1-1.5.2-2.1V2zm-1.9 5.6c-.1.8-.2 1.5-.3 2.1-.2.6-.4 1-.6 1.3-.3.3-.8.6-1.4.9-.7.3-1.4.5-2.2.8-.6.2-1.3.3-1.8.4L15 7.5c.3-.3.6-.7 1-1.1 0 .4 0 .8-.1 1.2zM6 20h8v-1.5H6V20z"})}),yc=[];const xc={rootClientId:void 0,insertionIndex:void 0,filterValue:void 0},vc=["post-only","template-locked"],Sc=(0,c.createRegistrySelector)((e=>(0,c.createSelector)((t=>{if("object"==typeof t.blockInserterPanel)return t.blockInserterPanel;if("template-locked"===st(t)){const[t]=e(h.store).getBlocksByName("core/post-content");if(t)return{rootClientId:t,insertionIndex:void 0,filterValue:void 0}}return xc}),(t=>{const[s]=e(h.store).getBlocksByName("core/post-content");return[t.blockInserterPanel,st(t),s]}))));function wc(e){return e.listViewToggleRef}function kc(e){return e.inserterSidebarToggleRef}const Cc={wp_block:ga,wp_navigation:fc,page:_a,post:bc},Pc=(0,c.createRegistrySelector)((e=>(t,s,o)=>{{if("wp_template_part"===s||"wp_template"===s){const t=(e(d.store).getCurrentTheme()?.default_template_part_areas||[]).find((e=>o.area===e.area));return t?.icon?U(t.icon):W}if(Cc[s])return Cc[s];const t=e(d.store).getPostType(s);return"string"==typeof t?.icon&&t.icon.startsWith("dashicons-")?t.icon.slice(10):_a}})),jc=(0,c.createRegistrySelector)((e=>(t,s,o)=>{const{type:n,id:i}=oe(t),r=e(d.store).getEntityRecordNonTransientEdits("postType",s||n,o||i);if(!r?.meta)return!1;const a=e(d.store).getEntityRecord("postType",s||n,o||i)?.meta;return!_c()({...a,footnotes:void 0},{...r.meta,footnotes:void 0})}));function Ec(e,...t){return function(e,t,s){var o;return null!==(o=e.actions[t]?.[s])&&void 0!==o?o:yc}(e.dataviews,...t)}function Tc(e,...t){return function(e,t,s){return e.isReady[t]?.[s]}(e.dataviews,...t)}function Bc(e,...t){return function(e,t,s){var o;return null!==(o=e.fields[t]?.[s])&&void 0!==o?o:yc}(e.dataviews,...t)}const Ic=(0,c.createRegistrySelector)((e=>(0,c.createSelector)(((t,s)=>{s=Array.isArray(s)?s:[s];const{getBlocksByName:o,getBlockParents:n,getBlockName:i}=e(h.store);return o(s).filter((e=>n(e).every((e=>{const t=i(e);return"core/query"!==t&&!s.includes(t)}))))}),(()=>[e(h.store).getBlocks()])))),Nc=(0,c.createRegistrySelector)((e=>(t,s)=>{const{getPostType:o,getCurrentTheme:n,hasFinishedResolution:i}=e(d.store),r=n(),a=o(s);if(!i("getPostType",[s])||!i("getCurrentTheme"))return;const l=r?.stylesheet,c=e(k.store).get("core","renderingModes")?.[l]?.[s],u=Array.isArray(a?.supports?.editor)?a.supports.editor.find((e=>"default-mode"in e))?.["default-mode"]:void 0,p=c||u;return vc.includes(p)?p:"post-only"})),Dc={reducer:b,selectors:e,actions:t},Ac=(0,c.createReduxStore)("core/editor",{...Dc});(0,c.register)(Ac),$(Ac).registerPrivateActions(a),$(Ac).registerPrivateSelectors(l);function Rc(e){const t=e.avatar_urls&&e.avatar_urls[24]?(0,L.jsx)("img",{className:"editor-autocompleters__user-avatar",alt:"",src:e.avatar_urls[24]}):(0,L.jsx)("span",{className:"editor-autocompleters__no-avatar"});return(0,L.jsxs)(L.Fragment,{children:[t,(0,L.jsx)("span",{className:"editor-autocompleters__user-name",children:e.name}),(0,L.jsx)("span",{className:"editor-autocompleters__user-slug",children:e.slug})]})}(0,m.addFilter)("blocks.registerBlockType","core/editor/custom-sources-backwards-compatibility/shim-attribute-source",(function(e){var t;const s=Object.fromEntries(Object.entries(null!==(t=e.attributes)&&void 0!==t?t:{}).filter((([,{source:e}])=>"meta"===e)).map((([e,{meta:t}])=>[e,t])));return Object.entries(s).length&&(e.edit=(e=>(0,p.createHigherOrderComponent)((t=>({attributes:s,setAttributes:o,...n})=>{const i=(0,c.useSelect)((e=>e(Ac).getCurrentPostType()),[]),[r,a]=(0,d.useEntityProp)("postType",i,"meta"),l=(0,u.useMemo)((()=>({...s,...Object.fromEntries(Object.entries(e).map((([e,t])=>[e,r[t]])))})),[s,r]);return(0,L.jsx)(t,{attributes:l,setAttributes:t=>{const s=Object.fromEntries(Object.entries(null!=t?t:{}).filter((([t])=>t in e)).map((([t,s])=>[e[t],s])));Object.entries(s).length&&a(s),o(t)},...n})}),"withMetaAttributeSource"))(s)(e.edit)),e}));const Mc={name:"users",className:"editor-autocompleters__user",triggerPrefix:"@",useItems(e){const t=(0,c.useSelect)((t=>{const{getUsers:s}=t(d.store);return s({context:"view",search:encodeURIComponent(e)})}),[e]),s=(0,u.useMemo)((()=>t?t.map((e=>({key:`user-${e.slug}`,value:e,label:Rc(e)}))):[]),[t]);return[s]},getOptionCompletion:e=>`@${e.slug}`};(0,m.addFilter)("editor.Autocomplete.completers","editor/autocompleters/set-default-completers",(function(e=[]){return e.push({...Mc}),e})),(0,m.addFilter)("editor.MediaUpload","core/editor/components/media-upload",(()=>Ei.MediaUpload));const{PatternOverridesControls:Lc,ResetOverridesControl:Oc,PatternOverridesBlockControls:Fc,PATTERN_TYPES:Vc,PARTIAL_SYNCING_SUPPORTED_BLOCKS:zc,PATTERN_SYNC_TYPES:Uc}=$(ln.privateApis),Hc=(0,p.createHigherOrderComponent)((e=>t=>{const s=!!zc[t.name];return(0,L.jsxs)(L.Fragment,{children:[(0,L.jsx)(e,{...t},"edit"),t.isSelected&&s&&(0,L.jsx)(Gc,{...t}),s&&(0,L.jsx)(Fc,{})]})}),"withPatternOverrideControls");function Gc(e){const t=(0,h.useBlockEditingMode)(),{hasPatternOverridesSource:s,isEditingSyncedPattern:o}=(0,c.useSelect)((e=>{const{getCurrentPostType:t,getEditedPostAttribute:s}=e(Ac);return{hasPatternOverridesSource:!!(0,y.getBlockBindingsSource)("core/pattern-overrides"),isEditingSyncedPattern:t()===Vc.user&&s("meta")?.wp_pattern_sync_status!==Uc.unsynced&&s("wp_pattern_sync_status")!==Uc.unsynced}}),[]),n=e.attributes.metadata?.bindings,i=!!n&&Object.values(n).some((e=>"core/pattern-overrides"===e.source)),r=o&&"default"===t,a=!o&&!!e.attributes.metadata?.name&&"disabled"!==t&&i;return s?(0,L.jsxs)(L.Fragment,{children:[r&&(0,L.jsx)(Lc,{...e}),a&&(0,L.jsx)(Oc,{...e})]}):null}(0,m.addFilter)("editor.BlockEdit","core/editor/with-pattern-override-controls",Hc);class $c extends u.Component{constructor(e){super(e),this.needsAutosave=!(!e.isDirty||!e.isAutosaveable)}componentDidMount(){this.props.disableIntervalChecks||this.setAutosaveTimer()}componentDidUpdate(e){this.props.disableIntervalChecks?this.props.editsReference!==e.editsReference&&this.props.autosave():(this.props.interval!==e.interval&&(clearTimeout(this.timerId),this.setAutosaveTimer()),this.props.isDirty&&(!this.props.isAutosaving||e.isAutosaving)?this.props.editsReference!==e.editsReference&&(this.needsAutosave=!0):this.needsAutosave=!1)}componentWillUnmount(){clearTimeout(this.timerId)}setAutosaveTimer(e=1e3*this.props.interval){this.timerId=setTimeout((()=>{this.autosaveTimerHandler()}),e)}autosaveTimerHandler(){this.props.isAutosaveable?(this.needsAutosave&&(this.needsAutosave=!1,this.props.autosave()),this.setAutosaveTimer()):this.setAutosaveTimer(1e3)}render(){return null}}const Wc=(0,p.compose)([(0,c.withSelect)(((e,t)=>{const{getReferenceByDistinctEdits:s}=e(d.store),{isEditedPostDirty:o,isEditedPostAutosaveable:n,isAutosavingPost:i,getEditorSettings:r}=e(Ac),{interval:a=r().autosaveInterval}=t;return{editsReference:s(),isDirty:o(),isAutosaveable:n(),isAutosaving:i(),interval:a}})),(0,c.withDispatch)(((e,t)=>({autosave(){const{autosave:s=e(Ac).autosave}=t;s()}})))])($c),Zc=(0,L.jsx)(M.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,L.jsx)(M.Path,{d:"M10.8622 8.04053L14.2805 12.0286L10.8622 16.0167L9.72327 15.0405L12.3049 12.0286L9.72327 9.01672L10.8622 8.04053Z"})}),Yc=(0,L.jsx)(M.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,L.jsx)(M.Path,{d:"m13.1 16-3.4-4 3.4-4 1.1 1-2.6 3 2.6 3-1.1 1z"})}),Kc=window.wp.dom;function qc(e){const{isFrontPage:t,isPostsPage:s}=(0,c.useSelect)((t=>{const{canUser:s,getEditedEntityRecord:o}=t(d.store),n=s("read",{kind:"root",name:"site"})?o("root","site"):void 0,i=parseInt(e,10);return{isFrontPage:n?.page_on_front===i,isPostsPage:n?.page_for_posts===i}}));return t?(0,fs.__)("Homepage"):!!s&&(0,fs.__)("Posts Page")}const Qc=(0,Uo.__unstableMotion)(Uo.Button);function Xc(e){const{postId:t,postType:s,postTypeLabel:o,documentTitle:n,isNotFound:i,templateTitle:r,onNavigateToPreviousEntityRecord:a,isTemplatePreview:l}=(0,c.useSelect)((e=>{var t;const{getCurrentPostType:s,getCurrentPostId:o,getEditorSettings:n,getRenderingMode:i}=e(Ac),{getEditedEntityRecord:r,getPostType:a,getCurrentTheme:l,isResolving:c}=e(d.store),u=s(),p=o(),m=r("postType",u,p),{default_template_types:h=[]}=null!==(t=l())&&void 0!==t?t:{},g=Y({templateTypes:h,template:m}),_=a(u)?.labels?.singular_name;return{postId:p,postType:u,postTypeLabel:_,documentTitle:m.title,isNotFound:!m&&!c("getEditedEntityRecord","postType",u,p),templateTitle:g.title,onNavigateToPreviousEntityRecord:n().onNavigateToPreviousEntityRecord,isTemplatePreview:"template-locked"===i()}}),[]),{open:m}=(0,c.useDispatch)(ya.store),g=(0,p.useReducedMotion)(),_=A.includes(s),f=!!a,b=_?r:n,y=e.title||b,x=e.icon,v=qc(t),S=(0,u.useRef)(!1);return(0,u.useEffect)((()=>{S.current=!0}),[]),(0,L.jsxs)("div",{className:Di("editor-document-bar",{"has-back-button":f}),children:[(0,L.jsx)(Uo.__unstableAnimatePresence,{children:f&&(0,L.jsx)(Qc,{className:"editor-document-bar__back",icon:(0,fs.isRTL)()?Zc:Yc,onClick:e=>{e.stopPropagation(),a()},size:"compact",initial:!!S.current&&{opacity:0,transform:"translateX(15%)"},animate:{opacity:1,transform:"translateX(0%)"},exit:{opacity:0,transform:"translateX(15%)"},transition:g?{duration:0}:void 0,children:(0,fs.__)("Back")})}),!_&&l&&(0,L.jsx)(h.BlockIcon,{icon:W,className:"editor-document-bar__icon-layout"}),i?(0,L.jsx)(Uo.__experimentalText,{children:(0,fs.__)("Document not found")}):(0,L.jsxs)(Uo.Button,{className:"editor-document-bar__command",onClick:()=>m(),size:"compact",children:[(0,L.jsxs)(Uo.__unstableMotion.div,{className:"editor-document-bar__title",initial:!!S.current&&{opacity:0,transform:f?"translateX(15%)":"translateX(-15%)"},animate:{opacity:1,transform:"translateX(0%)"},transition:g?{duration:0}:void 0,children:[x&&(0,L.jsx)(h.BlockIcon,{icon:x}),(0,L.jsxs)(Uo.__experimentalText,{size:"body",as:"h1",children:[(0,L.jsx)("span",{className:"editor-document-bar__post-title",children:y?(0,Kc.__unstableStripHTML)(y):(0,fs.__)("No title")}),v&&(0,L.jsx)("span",{className:"editor-document-bar__post-type-label",children:`· ${v}`}),o&&!e.title&&!v&&(0,L.jsx)("span",{className:"editor-document-bar__post-type-label",children:`· ${(0,Lo.decodeEntities)(o)}`})]})]},f),(0,L.jsx)("span",{className:"editor-document-bar__shortcut",children:wl.displayShortcut.primary("k")})]})]})}const Jc=window.wp.richText,ed=({children:e,isValid:t,isDisabled:s,level:o,href:n,onSelect:i})=>(0,L.jsx)("li",{className:Di("document-outline__item",`is-${o.toLowerCase()}`,{"is-invalid":!t,"is-disabled":s}),children:(0,L.jsxs)("a",{href:n,className:"document-outline__button","aria-disabled":s,onClick:function(e){s?e.preventDefault():i()},children:[(0,L.jsx)("span",{className:"document-outline__emdash","aria-hidden":"true"}),(0,L.jsx)("strong",{className:"document-outline__level",children:o}),(0,L.jsx)("span",{className:"document-outline__item-content",children:e})]})}),td=(0,L.jsx)("em",{children:(0,fs.__)("(Empty heading)")}),sd=[(0,L.jsx)("br",{},"incorrect-break"),(0,L.jsx)("em",{children:(0,fs.__)("(Incorrect heading level)")},"incorrect-message")],od=[(0,L.jsx)("br",{},"incorrect-break-h1"),(0,L.jsx)("em",{children:(0,fs.__)("(Your theme may already use a H1 for the post title)")},"incorrect-message-h1")],nd=[(0,L.jsx)("br",{},"incorrect-break-multiple-h1"),(0,L.jsx)("em",{children:(0,fs.__)("(Multiple H1 headings are not recommended)")},"incorrect-message-multiple-h1")];function id(){return(0,L.jsxs)(Uo.SVG,{width:"138",height:"148",viewBox:"0 0 138 148",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,L.jsx)(Uo.Rect,{width:"138",height:"148",rx:"4",fill:"#F0F6FC"}),(0,L.jsx)(Uo.Line,{x1:"44",y1:"28",x2:"24",y2:"28",stroke:"#DDDDDD"}),(0,L.jsx)(Uo.Rect,{x:"48",y:"16",width:"27",height:"23",rx:"4",fill:"#DDDDDD"}),(0,L.jsx)(Uo.Path,{d:"M54.7585 32V23.2727H56.6037V26.8736H60.3494V23.2727H62.1903V32H60.3494V28.3949H56.6037V32H54.7585ZM67.4574 23.2727V32H65.6122V25.0241H65.5611L63.5625 26.277V24.6406L65.723 23.2727H67.4574Z",fill:"black"}),(0,L.jsx)(Uo.Line,{x1:"55",y1:"59",x2:"24",y2:"59",stroke:"#DDDDDD"}),(0,L.jsx)(Uo.Rect,{x:"59",y:"47",width:"29",height:"23",rx:"4",fill:"#DDDDDD"}),(0,L.jsx)(Uo.Path,{d:"M65.7585 63V54.2727H67.6037V57.8736H71.3494V54.2727H73.1903V63H71.3494V59.3949H67.6037V63H65.7585ZM74.6605 63V61.6705L77.767 58.794C78.0313 58.5384 78.2528 58.3082 78.4318 58.1037C78.6136 57.8991 78.7514 57.6989 78.8452 57.5028C78.9389 57.304 78.9858 57.0895 78.9858 56.8594C78.9858 56.6037 78.9276 56.3835 78.8111 56.1989C78.6946 56.0114 78.5355 55.8679 78.3338 55.7685C78.1321 55.6662 77.9034 55.6151 77.6477 55.6151C77.3807 55.6151 77.1477 55.669 76.9489 55.777C76.75 55.8849 76.5966 56.0398 76.4886 56.2415C76.3807 56.4432 76.3267 56.6832 76.3267 56.9616H74.5753C74.5753 56.3906 74.7045 55.8949 74.9631 55.4744C75.2216 55.054 75.5838 54.7287 76.0497 54.4986C76.5156 54.2685 77.0526 54.1534 77.6605 54.1534C78.2855 54.1534 78.8295 54.2642 79.2926 54.4858C79.7585 54.7045 80.1207 55.0085 80.3793 55.3977C80.6378 55.7869 80.767 56.233 80.767 56.7358C80.767 57.0653 80.7017 57.3906 80.571 57.7116C80.4432 58.0327 80.2145 58.3892 79.8849 58.7812C79.5554 59.1705 79.0909 59.6378 78.4915 60.1832L77.2173 61.4318V61.4915H80.8821V63H74.6605Z",fill:"black"}),(0,L.jsx)(Uo.Line,{x1:"80",y1:"90",x2:"24",y2:"90",stroke:"#DDDDDD"}),(0,L.jsx)(Uo.Rect,{x:"84",y:"78",width:"30",height:"23",rx:"4",fill:"#F0B849"}),(0,L.jsx)(Uo.Path,{d:"M90.7585 94V85.2727H92.6037V88.8736H96.3494V85.2727H98.1903V94H96.3494V90.3949H92.6037V94H90.7585ZM99.5284 92.4659V91.0128L103.172 85.2727H104.425V87.2841H103.683L101.386 90.919V90.9872H106.564V92.4659H99.5284ZM103.717 94V92.0227L103.751 91.3793V85.2727H105.482V94H103.717Z",fill:"black"}),(0,L.jsx)(Uo.Line,{x1:"66",y1:"121",x2:"24",y2:"121",stroke:"#DDDDDD"}),(0,L.jsx)(Uo.Rect,{x:"70",y:"109",width:"29",height:"23",rx:"4",fill:"#DDDDDD"}),(0,L.jsx)(Uo.Path,{d:"M76.7585 125V116.273H78.6037V119.874H82.3494V116.273H84.1903V125H82.3494V121.395H78.6037V125H76.7585ZM88.8864 125.119C88.25 125.119 87.6832 125.01 87.1861 124.791C86.6918 124.57 86.3011 124.266 86.0142 123.879C85.7301 123.49 85.5838 123.041 85.5753 122.533H87.4332C87.4446 122.746 87.5142 122.933 87.642 123.095C87.7727 123.254 87.946 123.378 88.1619 123.466C88.3778 123.554 88.6207 123.598 88.8906 123.598C89.1719 123.598 89.4205 123.548 89.6364 123.449C89.8523 123.349 90.0213 123.212 90.1435 123.036C90.2656 122.859 90.3267 122.656 90.3267 122.426C90.3267 122.193 90.2614 121.987 90.1307 121.808C90.0028 121.626 89.8182 121.484 89.5767 121.382C89.3381 121.28 89.054 121.229 88.7244 121.229H87.9105V119.874H88.7244C89.0028 119.874 89.2486 119.825 89.4616 119.729C89.6776 119.632 89.8452 119.499 89.9645 119.328C90.0838 119.155 90.1435 118.953 90.1435 118.723C90.1435 118.504 90.0909 118.312 89.9858 118.148C89.8835 117.98 89.7386 117.849 89.5511 117.756C89.3665 117.662 89.1506 117.615 88.9034 117.615C88.6534 117.615 88.4247 117.661 88.2173 117.751C88.0099 117.839 87.8438 117.966 87.7188 118.131C87.5938 118.295 87.527 118.489 87.5185 118.71H85.75C85.7585 118.207 85.902 117.764 86.1804 117.381C86.4588 116.997 86.8338 116.697 87.3054 116.482C87.7798 116.263 88.3153 116.153 88.9119 116.153C89.5142 116.153 90.0412 116.263 90.4929 116.482C90.9446 116.7 91.2955 116.996 91.5455 117.368C91.7983 117.737 91.9233 118.152 91.9205 118.612C91.9233 119.101 91.7713 119.509 91.4645 119.835C91.1605 120.162 90.7642 120.369 90.2756 120.457V120.526C90.9176 120.608 91.4063 120.831 91.7415 121.195C92.0795 121.555 92.2472 122.007 92.2443 122.55C92.2472 123.047 92.1037 123.489 91.8139 123.875C91.527 124.261 91.1307 124.565 90.625 124.787C90.1193 125.009 89.5398 125.119 88.8864 125.119Z",fill:"black"})]})}const rd=(e=[])=>e.filter((e=>"core/heading"===e.name)).map((e=>({...e,level:e.attributes.level,isEmpty:ad(e)}))),ad=e=>!e.attributes.content||0===e.attributes.content.trim().length;function ld({onSelect:e,hasOutlineItemsDisabled:t}){const{selectBlock:s}=(0,c.useDispatch)(h.store),{title:o,isTitleSupported:n}=(0,c.useSelect)((e=>{var t;const{getEditedPostAttribute:s}=e(Ac),{getPostType:o}=e(d.store),n=o(s("type"));return{title:s("title"),isTitleSupported:null!==(t=n?.supports?.title)&&void 0!==t&&t}})),i=(0,c.useSelect)((e=>{const{getClientIdsWithDescendants:t,getBlock:s}=e(h.store);return t().map((e=>s(e)))})),r=(0,c.useSelect)((e=>{if("post-only"===e(Ac).getRenderingMode())return;const{getBlocksByName:t,getClientIdsOfDescendants:s}=e(h.store),[o]=t("core/post-content");return o?s(o):void 0}),[]),a=(0,u.useRef)(1),l=(0,u.useMemo)((()=>rd(i)),[i]);if(l.length<1)return(0,L.jsxs)("div",{className:"editor-document-outline has-no-headings",children:[(0,L.jsx)(id,{}),(0,L.jsx)("p",{children:(0,fs.__)("Navigate the structure of your document and address issues like empty or incorrect heading levels.")})]});const p=document.querySelector(".editor-post-title__input"),m=n&&o&&p,g=l.reduce(((e,t)=>({...e,[t.level]:(e[t.level]||0)+1})),{})[1]>1;return(0,L.jsx)("div",{className:"document-outline",children:(0,L.jsxs)("ul",{children:[m&&(0,L.jsx)(ed,{level:(0,fs.__)("Title"),isValid:!0,onSelect:e,href:`#${p.id}`,isDisabled:t,children:o}),l.map((o=>{const n=o.level>a.current+1,i=!(o.isEmpty||n||!o.level||1===o.level&&(g||m));return a.current=o.level,(0,L.jsxs)(ed,{level:`H${o.level}`,isValid:i,isDisabled:t||(l=o.clientId,!(!Array.isArray(r)||r.includes(l))),href:`#block-${o.clientId}`,onSelect:()=>{s(o.clientId),e?.()},children:[o.isEmpty?td:(0,Jc.getTextContent)((0,Jc.create)({html:o.attributes.content})),n&&sd,1===o.level&&g&&nd,m&&1===o.level&&!g&&od]},o.clientId);var l}))]})})}function cd({children:e}){const t=(0,c.useSelect)((e=>{const{getGlobalBlockCount:t}=e(h.store);return t("core/heading")>0}));return t?e:null}const dd=function(){const{registerShortcut:e}=(0,c.useDispatch)(vl.store);return(0,u.useEffect)((()=>{e({name:"core/editor/toggle-mode",category:"global",description:(0,fs.__)("Switch between visual editor and code editor."),keyCombination:{modifier:"secondary",character:"m"}}),e({name:"core/editor/save",category:"global",description:(0,fs.__)("Save your changes."),keyCombination:{modifier:"primary",character:"s"}}),e({name:"core/editor/undo",category:"global",description:(0,fs.__)("Undo your last changes."),keyCombination:{modifier:"primary",character:"z"}}),e({name:"core/editor/redo",category:"global",description:(0,fs.__)("Redo your last undo."),keyCombination:{modifier:"primaryShift",character:"z"},aliases:(0,wl.isAppleOS)()?[]:[{modifier:"primary",character:"y"}]}),e({name:"core/editor/toggle-list-view",category:"global",description:(0,fs.__)("Show or hide the List View."),keyCombination:{modifier:"access",character:"o"}}),e({name:"core/editor/toggle-distraction-free",category:"global",description:(0,fs.__)("Enter or exit distraction free mode."),keyCombination:{modifier:"primaryShift",character:"\\"}}),e({name:"core/editor/toggle-sidebar",category:"global",description:(0,fs.__)("Show or hide the Settings panel."),keyCombination:{modifier:"primaryShift",character:","}}),e({name:"core/editor/keyboard-shortcuts",category:"main",description:(0,fs.__)("Display these keyboard shortcuts."),keyCombination:{modifier:"access",character:"h"}}),e({name:"core/editor/next-region",category:"global",description:(0,fs.__)("Navigate to the next part of the editor."),keyCombination:{modifier:"ctrl",character:"`"},aliases:[{modifier:"access",character:"n"}]}),e({name:"core/editor/previous-region",category:"global",description:(0,fs.__)("Navigate to the previous part of the editor."),keyCombination:{modifier:"ctrlShift",character:"`"},aliases:[{modifier:"access",character:"p"},{modifier:"ctrlShift",character:"~"}]})}),[e]),(0,L.jsx)(h.BlockEditorKeyboardShortcuts.Register,{})},ud=(0,L.jsx)(M.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,L.jsx)(M.Path,{d:"M15.6 6.5l-1.1 1 2.9 3.3H8c-.9 0-1.7.3-2.3.9-1.4 1.5-1.4 4.2-1.4 5.6v.2h1.5v-.3c0-1.1 0-3.5 1-4.5.3-.3.7-.5 1.3-.5h9.2L14.5 15l1.1 1.1 4.6-4.6-4.6-5z"})}),pd=(0,L.jsx)(M.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,L.jsx)(M.Path,{d:"M18.3 11.7c-.6-.6-1.4-.9-2.3-.9H6.7l2.9-3.3-1.1-1-4.5 5L8.5 16l1-1-2.7-2.7H16c.5 0 .9.2 1.3.5 1 1 1 3.4 1 4.5v.3h1.5v-.2c0-1.5 0-4.3-1.5-5.7z"})});const md=(0,u.forwardRef)((function(e,t){const s=(0,wl.isAppleOS)()?wl.displayShortcut.primaryShift("z"):wl.displayShortcut.primary("y"),o=(0,c.useSelect)((e=>e(Ac).hasEditorRedo()),[]),{redo:n}=(0,c.useDispatch)(Ac);return(0,L.jsx)(Uo.Button,{__next40pxDefaultSize:!0,...e,ref:t,icon:(0,fs.isRTL)()?pd:ud,label:(0,fs.__)("Redo"),shortcut:s,"aria-disabled":!o,onClick:o?n:void 0,className:"editor-history__redo"})}));const hd=(0,u.forwardRef)((function(e,t){const s=(0,c.useSelect)((e=>e(Ac).hasEditorUndo()),[]),{undo:o}=(0,c.useDispatch)(Ac);return(0,L.jsx)(Uo.Button,{__next40pxDefaultSize:!0,...e,ref:t,icon:(0,fs.isRTL)()?ud:pd,label:(0,fs.__)("Undo"),shortcut:wl.displayShortcut.primary("z"),"aria-disabled":!s,onClick:s?o:void 0,className:"editor-history__undo"})}));function gd(){const[e,t]=(0,u.useState)(!1),s=(0,c.useSelect)((e=>e(h.store).isValidTemplate()),[]),{setTemplateValidity:o,synchronizeTemplate:n}=(0,c.useDispatch)(h.store);return s?null:(0,L.jsxs)(L.Fragment,{children:[(0,L.jsx)(Uo.Notice,{className:"editor-template-validation-notice",isDismissible:!1,status:"warning",actions:[{label:(0,fs.__)("Keep it as is"),onClick:()=>o(!0)},{label:(0,fs.__)("Reset the template"),onClick:()=>t(!0)}],children:(0,fs.__)("The content of your post doesn’t match the template assigned to your post type.")}),(0,L.jsx)(Uo.__experimentalConfirmDialog,{isOpen:e,confirmButtonText:(0,fs.__)("Reset"),onConfirm:()=>{t(!1),n()},onCancel:()=>t(!1),size:"medium",children:(0,fs.__)("Resetting the template may result in loss of content, do you want to continue?")})]})}const _d=function(){const{notices:e}=(0,c.useSelect)((e=>({notices:e(_s.store).getNotices()})),[]),{removeNotice:t}=(0,c.useDispatch)(_s.store),s=e.filter((({isDismissible:e,type:t})=>e&&"default"===t)),o=e.filter((({isDismissible:e,type:t})=>!e&&"default"===t));return(0,L.jsxs)(L.Fragment,{children:[(0,L.jsx)(Uo.NoticeList,{notices:o,className:"components-editor-notices__pinned"}),(0,L.jsx)(Uo.NoticeList,{notices:s,className:"components-editor-notices__dismissible",onRemove:t,children:(0,L.jsx)(gd,{})})]})},fd=-3;function bd(){const e=(0,c.useSelect)((e=>e(_s.store).getNotices()),[]),{removeNotice:t}=(0,c.useDispatch)(_s.store),s=e.filter((({type:e})=>"snackbar"===e)).slice(fd);return(0,L.jsx)(Uo.SnackbarList,{notices:s,className:"components-editor-notices__snackbar",onRemove:t})}function yd({record:e,checked:t,onChange:s}){const{name:o,kind:n,title:i,key:r}=e,{entityRecordTitle:a,hasPostMetaChanges:l}=(0,c.useSelect)((e=>{var t;if("postType"!==n||"wp_template"!==o)return{entityRecordTitle:i,hasPostMetaChanges:$(e(Ac)).hasPostMetaChanges(o,r)};const s=e(d.store).getEditedEntityRecord(n,o,r),{default_template_types:a=[]}=null!==(t=e(d.store).getCurrentTheme())&&void 0!==t?t:{};return{entityRecordTitle:Y({template:s,templateTypes:a}).title,hasPostMetaChanges:$(e(Ac)).hasPostMetaChanges(o,r)}}),[o,n,i,r]);return(0,L.jsxs)(L.Fragment,{children:[(0,L.jsx)(Uo.PanelRow,{children:(0,L.jsx)(Uo.CheckboxControl,{__nextHasNoMarginBottom:!0,label:(0,Lo.decodeEntities)(a)||(0,fs.__)("Untitled"),checked:t,onChange:s,className:"entities-saved-states__change-control"})}),l&&(0,L.jsx)("ul",{className:"entities-saved-states__changes",children:(0,L.jsx)("li",{children:(0,fs.__)("Post Meta.")})})]})}const{getGlobalStylesChanges:xd,GlobalStylesContext:vd}=$(h.privateApis);function Sd({record:e}){const{user:t}=(0,u.useContext)(vd),s=(0,c.useSelect)((t=>t(d.store).getEntityRecord(e.kind,e.name,e.key)),[e.kind,e.name,e.key]),o=xd(t,s,{maxResults:10});return o.length?(0,L.jsx)("ul",{className:"entities-saved-states__changes",children:o.map((e=>(0,L.jsx)("li",{children:e},e)))}):null}function wd({record:e,count:t}){if("globalStyles"===e?.name)return null;const s=function(e,t){switch(e){case"site":return 1===t?(0,fs.__)("This change will affect your whole site."):(0,fs.__)("These changes will affect your whole site.");case"wp_template":return(0,fs.__)("This change will affect other parts of your site that use this template.");case"page":case"post":return(0,fs.__)("The following has been modified.")}}(e?.name,t);return s?(0,L.jsx)(Uo.PanelRow,{children:s}):null}function kd({list:e,unselectedEntities:t,setUnselectedEntities:s}){const o=e.length,n=e[0];let i=(0,c.useSelect)((e=>e(d.store).getEntityConfig(n.kind,n.name)),[n.kind,n.name]).label;return"wp_template_part"===n?.name&&(i=1===o?(0,fs.__)("Template Part"):(0,fs.__)("Template Parts")),(0,L.jsxs)(Uo.PanelBody,{title:i,initialOpen:!0,className:"entities-saved-states__panel-body",children:[(0,L.jsx)(wd,{record:n,count:o}),e.map((e=>(0,L.jsx)(yd,{record:e,checked:!t.some((t=>t.kind===e.kind&&t.name===e.name&&t.key===e.key&&t.property===e.property)),onChange:t=>s(e,t)},e.key||e.property))),"globalStyles"===n?.name&&(0,L.jsx)(Sd,{record:n})]})}const Cd=()=>{const{editedEntities:e,siteEdits:t,siteEntityConfig:s}=(0,c.useSelect)((e=>{const{__experimentalGetDirtyEntityRecords:t,getEntityRecordEdits:s,getEntityConfig:o}=e(d.store);return{editedEntities:t(),siteEdits:s("root","site"),siteEntityConfig:o("root","site")}}),[]),o=(0,u.useMemo)((()=>{var o;const n=e.filter((e=>!("root"===e.kind&&"site"===e.name))),i=null!==(o=s?.meta?.labels)&&void 0!==o?o:{},r=[];for(const e in t)r.push({kind:"root",name:"site",title:i[e]||e,property:e});return[...n,...r]}),[e,t,s]),[n,i]=(0,u.useState)([]);return{dirtyEntityRecords:o,isDirty:o.length-n.length>0,setUnselectedEntities:({kind:e,name:t,key:s,property:o},r)=>{i(r?n.filter((n=>n.kind!==e||n.name!==t||n.key!==s||n.property!==o)):[...n,{kind:e,name:t,key:s,property:o}])},unselectedEntities:n}};function Pd(e){return e}function jd({close:e,renderDialog:t,variant:s}){const o=Cd();return(0,L.jsx)(Ed,{close:e,renderDialog:t,variant:s,...o})}function Ed({additionalPrompt:e,close:t,onSave:s=Pd,saveEnabled:o,saveLabel:n=(0,fs.__)("Save"),renderDialog:i,dirtyEntityRecords:r,isDirty:a,setUnselectedEntities:l,unselectedEntities:d,variant:m="default"}){const h=(0,u.useRef)(),{saveDirtyEntities:g}=$((0,c.useDispatch)(Ac)),_=r.reduce(((e,t)=>{const{name:s}=t;return e[s]||(e[s]=[]),e[s].push(t),e}),{}),{site:f,wp_template:b,wp_template_part:y,...x}=_,v=[f,b,y,...Object.values(x)].filter(Array.isArray),S=null!=o?o:a,w=(0,u.useCallback)((()=>t()),[t]),[k,C]=(0,p.__experimentalUseDialog)({onClose:()=>w()}),P=(0,p.useInstanceId)(Ed,"entities-saved-states__panel-label"),j=(0,p.useInstanceId)(Ed,"entities-saved-states__panel-description"),E=r.length?(0,fs.__)("Select the items you want to save."):void 0,T="inline"===m,B=(0,L.jsxs)(L.Fragment,{children:[(0,L.jsx)(Uo.FlexItem,{isBlock:!T,as:Uo.Button,variant:T?"tertiary":"secondary",size:T?void 0:"compact",onClick:w,children:(0,fs.__)("Cancel")}),(0,L.jsx)(Uo.FlexItem,{isBlock:!T,as:Uo.Button,ref:h,variant:"primary",size:T?void 0:"compact",disabled:!S,accessibleWhenDisabled:!0,onClick:()=>g({onSave:s,dirtyEntityRecords:r,entitiesToSkip:d,close:t}),className:"editor-entities-saved-states__save-button",children:n})]});return(0,L.jsxs)("div",{ref:i?k:void 0,...i&&C,className:Di("entities-saved-states__panel",{"is-inline":T}),role:i?"dialog":void 0,"aria-labelledby":i?P:void 0,"aria-describedby":i?j:void 0,children:[!T&&(0,L.jsx)(Uo.Flex,{className:"entities-saved-states__panel-header",gap:2,children:B}),(0,L.jsxs)("div",{className:"entities-saved-states__text-prompt",children:[(0,L.jsx)("div",{className:"entities-saved-states__text-prompt--header-wrapper",children:(0,L.jsx)("strong",{id:i?P:void 0,className:"entities-saved-states__text-prompt--header",children:(0,fs.__)("Are you ready to save?")})}),(0,L.jsxs)("div",{id:i?j:void 0,children:[e,(0,L.jsx)("p",{className:"entities-saved-states__text-prompt--changes-count",children:a?(0,u.createInterpolateElement)((0,fs.sprintf)((0,fs._n)("There is <strong>%d site change</strong> waiting to be saved.","There are <strong>%d site changes</strong> waiting to be saved.",r.length),r.length),{strong:(0,L.jsx)("strong",{})}):E})]})]}),v.map((e=>(0,L.jsx)(kd,{list:e,unselectedEntities:d,setUnselectedEntities:l},e[0].name))),T&&(0,L.jsx)(Uo.Flex,{direction:"row",justify:"flex-end",className:"entities-saved-states__panel-footer",children:B})]})}function Td(){try{return(0,c.select)(Ac).getEditedPostContent()}catch(e){}}function Bd({text:e,children:t,variant:s="secondary"}){const o=(0,p.useCopyToClipboard)(e);return(0,L.jsx)(Uo.Button,{__next40pxDefaultSize:!0,variant:s,ref:o,children:t})}class Id extends u.Component{constructor(){super(...arguments),this.state={error:null}}componentDidCatch(e){(0,m.doAction)("editor.ErrorBoundary.errorLogged",e)}static getDerivedStateFromError(e){return{error:e}}render(){const{error:e}=this.state,{canCopyContent:t=!1}=this.props;return e?(0,L.jsxs)(Uo.__experimentalHStack,{className:"editor-error-boundary",alignment:"baseline",spacing:4,justify:"space-between",expanded:!1,wrap:!0,children:[(0,L.jsx)(Uo.__experimentalText,{as:"p",children:(0,fs.__)("The editor has encountered an unexpected error.")}),(0,L.jsxs)(Uo.__experimentalHStack,{expanded:!1,children:[t&&(0,L.jsx)(Bd,{text:Td,children:(0,fs.__)("Copy contents")}),(0,L.jsx)(Bd,{variant:"primary",text:e?.stack,children:(0,fs.__)("Copy error")})]})]}):this.props.children}}const Nd=Id,Dd=window.requestIdleCallback?window.requestIdleCallback:window.requestAnimationFrame;let Ad;function Rd(){const{postId:e,isEditedPostNew:t,hasRemoteAutosave:s}=(0,c.useSelect)((e=>({postId:e(Ac).getCurrentPostId(),isEditedPostNew:e(Ac).isEditedPostNew(),hasRemoteAutosave:!!e(Ac).getEditorSettings().autosave})),[]),{getEditedPostAttribute:o}=(0,c.useSelect)(Ac),{createWarningNotice:n,removeNotice:i}=(0,c.useDispatch)(_s.store),{editPost:r,resetEditorBlocks:a}=(0,c.useDispatch)(Ac);(0,u.useEffect)((()=>{let l=function(e,t){return window.sessionStorage.getItem(bs(e,t))}(e,t);if(!l)return;try{l=JSON.parse(l)}catch{return}const{post_title:c,content:d,excerpt:u}=l,p={title:c,content:d,excerpt:u};if(!Object.keys(p).some((e=>p[e]!==o(e))))return void ys(e,t);if(s)return;const m="wpEditorAutosaveRestore";n((0,fs.__)("The backup of this post in your browser is different from the version below."),{id:m,actions:[{label:(0,fs.__)("Restore the backup"),onClick(){const{content:e,...t}=p;r(t),a((0,y.parse)(p.content)),i(m)}}]})}),[t,e])}const Md=(0,p.ifCondition)((()=>{if(void 0!==Ad)return Ad;try{window.sessionStorage.setItem("__wpEditorTestSessionStorage",""),window.sessionStorage.removeItem("__wpEditorTestSessionStorage"),Ad=!0}catch{Ad=!1}return Ad}))((function(){const{autosave:e}=(0,c.useDispatch)(Ac),t=(0,u.useCallback)((()=>{Dd((()=>e({local:!0})))}),[]);Rd(),function(){const{postId:e,isEditedPostNew:t,isDirty:s,isAutosaving:o,didError:n}=(0,c.useSelect)((e=>({postId:e(Ac).getCurrentPostId(),isEditedPostNew:e(Ac).isEditedPostNew(),isDirty:e(Ac).isEditedPostDirty(),isAutosaving:e(Ac).isAutosavingPost(),didError:e(Ac).didPostSaveRequestFail()})),[]),i=(0,u.useRef)(s),r=(0,u.useRef)(o);(0,u.useEffect)((()=>{!n&&(r.current&&!o||i.current&&!s)&&ys(e,t),i.current=s,r.current=o}),[s,o,n]);const a=(0,p.usePrevious)(t),l=(0,p.usePrevious)(e);(0,u.useEffect)((()=>{l===e&&a&&!t&&ys(e,!0)}),[t,e])}();const s=(0,c.useSelect)((e=>e(Ac).getEditorSettings().localAutosaveInterval),[]);return(0,L.jsx)(Wc,{interval:s,autosave:t})}));const Ld=function({children:e}){const t=(0,c.useSelect)((e=>{const{getEditedPostAttribute:t}=e(Ac),{getPostType:s}=e(d.store),o=s(t("type"));return!!o?.supports?.["page-attributes"]}),[]);return t?e:null};const Od=function({children:e,supportKeys:t}){const s=(0,c.useSelect)((e=>{const{getEditedPostAttribute:t}=e(Ac),{getPostType:s}=e(d.store);return s(t("type"))}),[]);let o=!!s;return s&&(o=(Array.isArray(t)?t:[t]).some((e=>!!s.supports[e]))),o?e:null};function Fd(){const e=(0,c.useSelect)((e=>{var t;return null!==(t=e(Ac).getEditedPostAttribute("menu_order"))&&void 0!==t?t:0}),[]),{editPost:t}=(0,c.useDispatch)(Ac),[s,o]=(0,u.useState)(null),n=null!=s?s:e;return(0,L.jsx)(Uo.Flex,{children:(0,L.jsx)(Uo.FlexBlock,{children:(0,L.jsx)(Uo.__experimentalNumberControl,{__next40pxDefaultSize:!0,label:(0,fs.__)("Order"),help:(0,fs.__)("Set the page order."),value:n,onChange:e=>{o(e);const s=Number(e);Number.isInteger(s)&&""!==e.trim?.()&&t({menu_order:s})},hideLabelFromVision:!0,onBlur:()=>{o(null)}})})})}function Vd(){return(0,L.jsx)(Od,{supportKeys:"page-attributes",children:(0,L.jsx)(Fd,{})})}const zd=(0,u.forwardRef)((({className:e,label:t,children:s},o)=>(0,L.jsxs)(Uo.__experimentalHStack,{className:Di("editor-post-panel__row",e),ref:o,children:[t&&(0,L.jsx)("div",{className:"editor-post-panel__row-label",children:t}),(0,L.jsx)("div",{className:"editor-post-panel__row-control",children:s})]})));function Ud(e){const t=e.map((e=>({children:[],parent:void 0,...e})));if(t.some((({parent:e})=>void 0===e)))return t;const s=t.reduce(((e,t)=>{const{parent:s}=t;return e[s]||(e[s]=[]),e[s].push(t),e}),{}),o=e=>e.map((e=>{const t=s[e.id];return{...e,children:t&&t.length?o(t):[]}}));return o(s[0]||[])}const Hd=e=>(0,Lo.decodeEntities)(e),Gd=e=>({...e,name:Hd(e.name)}),$d=e=>(null!=e?e:[]).map(Gd);function Wd(e){return e?.title?.rendered?(0,Lo.decodeEntities)(e.title.rendered):`#${e.id} (${(0,fs.__)("no title")})`}const Zd=(e,t)=>{const s=sr()(e||"").toLowerCase(),o=sr()(t||"").toLowerCase();return s===o?0:s.startsWith(o)?s.length:1/0};function Yd(){const{editPost:e}=(0,c.useDispatch)(Ac),[t,s]=(0,u.useState)(!1),{isHierarchical:o,parentPostId:n,parentPostTitle:i,pageItems:r,isLoading:a}=(0,c.useSelect)((e=>{var s;const{getPostType:o,getEntityRecords:n,getEntityRecord:i,isResolving:r}=e(d.store),{getCurrentPostId:a,getEditedPostAttribute:l}=e(Ac),c=l("type"),u=l("parent"),p=o(c),m=a(),h=null!==(s=p?.hierarchical)&&void 0!==s&&s,g={per_page:100,exclude:m,parent_exclude:m,orderby:"menu_order",order:"asc",_fields:"id,title,parent"};t&&(g.search=t);const _=u?i("postType",c,u):null;return{isHierarchical:h,parentPostId:u,parentPostTitle:_?Wd(_):"",pageItems:h?n("postType",c,g):null,isLoading:!!h&&r("getEntityRecords",["postType",c,g])}}),[t]),l=(0,u.useMemo)((()=>{const e=(s,o=0)=>{const n=s.map((t=>[{value:t.id,label:"— ".repeat(o)+(0,Lo.decodeEntities)(t.name),rawName:t.name},...e(t.children||[],o+1)])).sort((([e],[s])=>Zd(e.rawName,t)>=Zd(s.rawName,t)?1:-1));return n.flat()};if(!r)return[];let s=r.map((e=>({id:e.id,parent:e.parent,name:Wd(e)})));t||(s=Ud(s));const o=e(s),a=o.find((e=>e.value===n));return i&&!a&&o.unshift({value:n,label:i}),o}),[r,t,i,n]);if(!o)return null;return(0,L.jsx)(Uo.ComboboxControl,{__nextHasNoMarginBottom:!0,__next40pxDefaultSize:!0,className:"editor-page-attributes__parent",label:(0,fs.__)("Parent"),help:(0,fs.__)("Choose a parent page."),value:n,options:l,onFilterValueChange:(0,p.debounce)((e=>{s(e)}),300),onChange:t=>{e({parent:t})},hideLabelFromVision:!0,isLoading:a})}function Kd({isOpen:e,onClick:t}){const s=(0,c.useSelect)((e=>{const{getEditedPostAttribute:t}=e(Ac),s=t("parent");if(!s)return null;const{getEntityRecord:o}=e(d.store);return o("postType",t("type"),s)}),[]),o=(0,u.useMemo)((()=>s?Wd(s):(0,fs.__)("None")),[s]);return(0,L.jsx)(Uo.Button,{size:"compact",className:"editor-post-parent__panel-toggle",variant:"tertiary","aria-expanded":e,"aria-label":(0,fs.sprintf)((0,fs.__)("Change parent: %s"),o),onClick:t,children:o})}function qd(){const e=(0,c.useSelect)((e=>e(d.store).getEntityRecord("root","__unstableBase")?.home),[]),[t,s]=(0,u.useState)(null),o=(0,u.useMemo)((()=>({anchor:t,placement:"left-start",offset:36,shift:!0})),[t]);return(0,L.jsx)(zd,{label:(0,fs.__)("Parent"),ref:s,children:(0,L.jsx)(Uo.Dropdown,{popoverProps:o,className:"editor-post-parent__panel-dropdown",contentClassName:"editor-post-parent__panel-dialog",focusOnMount:!0,renderToggle:({isOpen:e,onToggle:t})=>(0,L.jsx)(Kd,{isOpen:e,onClick:t}),renderContent:({onClose:t})=>(0,L.jsxs)("div",{className:"editor-post-parent",children:[(0,L.jsx)(h.__experimentalInspectorPopoverHeader,{title:(0,fs.__)("Parent"),onClose:t}),(0,L.jsxs)("div",{children:[(0,u.createInterpolateElement)((0,fs.sprintf)((0,fs.__)('Child pages inherit characteristics from their parent, such as URL structure. For instance, if "Pricing" is a child of "Services", its URL would be %s<wbr />/services<wbr />/pricing.'),(0,v.filterURLForDisplay)(e).replace(/([/.])/g,"<wbr />$1")),{wbr:(0,L.jsx)("wbr",{})}),(0,L.jsx)("p",{children:(0,u.createInterpolateElement)((0,fs.__)("They also show up as sub-items in the default navigation menu. <a>Learn more.</a>"),{a:(0,L.jsx)(Uo.ExternalLink,{href:(0,fs.__)("https://wordpress.org/documentation/article/page-post-settings-sidebar/#page-attributes")})})})]}),(0,L.jsx)(Yd,{})]})})})}const Qd=Yd,Xd="page-attributes";function Jd(){const{isEnabled:e,postType:t}=(0,c.useSelect)((e=>{const{getEditedPostAttribute:t,isEditorPanelEnabled:s}=e(Ac),{getPostType:o}=e(d.store);return{isEnabled:s(Xd),postType:o(t("type"))}}),[]);return e&&t?(0,L.jsx)(qd,{}):null}function eu(){return(0,L.jsx)(Ld,{children:(0,L.jsx)(Jd,{})})}const tu=(0,L.jsx)(M.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,L.jsx)(M.Path,{fillRule:"evenodd",clipRule:"evenodd",d:"M18.5 5.5V8H20V5.5H22.5V4H20V1.5H18.5V4H16V5.5H18.5ZM13.9624 4H6C4.89543 4 4 4.89543 4 6V18C4 19.1046 4.89543 20 6 20H18C19.1046 20 20 19.1046 20 18V10.0391H18.5V18C18.5 18.2761 18.2761 18.5 18 18.5H10L10 10.4917L16.4589 10.5139L16.4641 9.01389L5.5 8.97618V6C5.5 5.72386 5.72386 5.5 6 5.5H13.9624V4ZM5.5 10.4762V18C5.5 18.2761 5.72386 18.5 6 18.5H8.5L8.5 10.4865L5.5 10.4762Z"})}),su=(0,fs.__)("Custom Template");function ou({onClose:e}){const{defaultBlockTemplate:t,onNavigateToEntityRecord:s}=(0,c.useSelect)((e=>{const{getEditorSettings:t,getCurrentTemplateId:s}=e(Ac);return{defaultBlockTemplate:t().defaultBlockTemplate,onNavigateToEntityRecord:t().onNavigateToEntityRecord,getTemplateId:s}})),{createTemplate:o}=$((0,c.useDispatch)(Ac)),[n,i]=(0,u.useState)(""),[r,a]=(0,u.useState)(!1),l=()=>{i(""),e()};return(0,L.jsx)(Uo.Modal,{title:(0,fs.__)("Create custom template"),onRequestClose:l,focusOnMount:"firstContentElement",size:"small",children:(0,L.jsx)("form",{className:"editor-post-template__create-form",onSubmit:async e=>{if(e.preventDefault(),r)return;a(!0);const i=null!=t?t:(0,y.serialize)([(0,y.createBlock)("core/group",{tagName:"header",layout:{inherit:!0}},[(0,y.createBlock)("core/site-title"),(0,y.createBlock)("core/site-tagline")]),(0,y.createBlock)("core/separator"),(0,y.createBlock)("core/group",{tagName:"main"},[(0,y.createBlock)("core/group",{layout:{inherit:!0}},[(0,y.createBlock)("core/post-title")]),(0,y.createBlock)("core/post-content",{layout:{inherit:!0}})])]),c=await o({slug:(0,v.cleanForSlug)(n||su),content:i,title:n||su});a(!1),s({postId:c.id,postType:"wp_template"}),l()},children:(0,L.jsxs)(Uo.__experimentalVStack,{spacing:"3",children:[(0,L.jsx)(Uo.TextControl,{__next40pxDefaultSize:!0,__nextHasNoMarginBottom:!0,label:(0,fs.__)("Name"),value:n,onChange:i,placeholder:su,disabled:r,help:(0,fs.__)('Describe the template, e.g. "Post with sidebar". A custom template can be manually applied to any post or page.')}),(0,L.jsxs)(Uo.__experimentalHStack,{justify:"right",children:[(0,L.jsx)(Uo.Button,{__next40pxDefaultSize:!0,variant:"tertiary",onClick:l,children:(0,fs.__)("Cancel")}),(0,L.jsx)(Uo.Button,{__next40pxDefaultSize:!0,variant:"primary",type:"submit",isBusy:r,"aria-disabled":r,children:(0,fs.__)("Create")})]})]})})})}function nu(){return(0,c.useSelect)((e=>{const{getCurrentPostId:t,getCurrentPostType:s}=e(Ac);return{postId:t(),postType:s()}}),[])}function iu(){const{postType:e,postId:t}=nu();return(0,c.useSelect)((s=>{const{canUser:o,getEntityRecord:n,getEntityRecords:i}=s(d.store),r=o("read",{kind:"root",name:"site"})?n("root","site"):void 0,a=i("postType","wp_template",{per_page:-1}),l=+t===r?.page_for_posts,c="page"===e&&+t===r?.page_on_front&&a?.some((({slug:e})=>"front-page"===e));return!l&&!c}),[t,e])}function ru(e){return(0,c.useSelect)((t=>t(d.store).getEntityRecords("postType","wp_template",{per_page:-1,post_type:e})),[e])}function au(e){const t=lu(),s=iu(),o=ru(e);return(0,u.useMemo)((()=>s&&o?.filter((e=>e.is_custom&&e.slug!==t&&!!e.content.raw))),[o,t,s])}function lu(){const{postType:e,postId:t}=nu(),s=ru(e),o=(0,c.useSelect)((s=>{const o=s(d.store).getEditedEntityRecord("postType",e,t);return o?.template}),[e,t]);if(o)return s?.find((e=>e.slug===o))?.slug}function cu({isOpen:e,onClick:t}){const s=(0,c.useSelect)((e=>{const t=e(Ac).getEditedPostAttribute("template"),{supportsTemplateMode:s,availableTemplates:o}=e(Ac).getEditorSettings();if(!s&&o[t])return o[t];const n=e(d.store).canUser("create",{kind:"postType",name:"wp_template"})&&e(Ac).getCurrentTemplateId();return n?.title||n?.slug||o?.[t]}),[]);return(0,L.jsx)(Uo.Button,{__next40pxDefaultSize:!0,variant:"tertiary","aria-expanded":e,"aria-label":(0,fs.__)("Template options"),onClick:t,children:null!=s?s:(0,fs.__)("Default template")})}function du({onClose:e}){var t,s;const o=iu(),{availableTemplates:n,fetchedTemplates:i,selectedTemplateSlug:r,canCreate:a,canEdit:l,currentTemplateId:p,onNavigateToEntityRecord:m,getEditorSettings:g}=(0,c.useSelect)((e=>{const{canUser:t,getEntityRecords:s}=e(d.store),n=e(Ac).getEditorSettings(),i=t("create",{kind:"postType",name:"wp_template"}),r=e(Ac).getCurrentTemplateId();return{availableTemplates:n.availableTemplates,fetchedTemplates:i?s("postType","wp_template",{post_type:e(Ac).getCurrentPostType(),per_page:-1}):void 0,selectedTemplateSlug:e(Ac).getEditedPostAttribute("template"),canCreate:o&&i&&n.supportsTemplateMode,canEdit:o&&i&&n.supportsTemplateMode&&!!r,currentTemplateId:r,onNavigateToEntityRecord:n.onNavigateToEntityRecord,getEditorSettings:e(Ac).getEditorSettings}}),[o]),_=(0,u.useMemo)((()=>Object.entries({...n,...Object.fromEntries((null!=i?i:[]).map((({slug:e,title:t})=>[e,t.rendered])))}).map((([e,t])=>({value:e,label:t})))),[n,i]),f=null!==(t=_.find((e=>e.value===r)))&&void 0!==t?t:_.find((e=>!e.value)),{editPost:b}=(0,c.useDispatch)(Ac),{createSuccessNotice:y}=(0,c.useDispatch)(_s.store),[x,v]=(0,u.useState)(!1);return(0,L.jsxs)("div",{className:"editor-post-template__classic-theme-dropdown",children:[(0,L.jsx)(h.__experimentalInspectorPopoverHeader,{title:(0,fs.__)("Template"),help:(0,fs.__)("Templates define the way content is displayed when viewing your site."),actions:a?[{icon:tu,label:(0,fs.__)("Add template"),onClick:()=>v(!0)}]:[],onClose:e}),o?(0,L.jsx)(Uo.SelectControl,{__next40pxDefaultSize:!0,__nextHasNoMarginBottom:!0,hideLabelFromVision:!0,label:(0,fs.__)("Template"),value:null!==(s=f?.value)&&void 0!==s?s:"",options:_,onChange:e=>b({template:e||""})}):(0,L.jsx)(Uo.Notice,{status:"warning",isDismissible:!1,children:(0,fs.__)("The posts page template cannot be changed.")}),l&&m&&(0,L.jsx)("p",{children:(0,L.jsx)(Uo.Button,{__next40pxDefaultSize:!0,variant:"link",onClick:()=>{m({postId:p,postType:"wp_template"}),e(),y((0,fs.__)("Editing template. Changes made here affect all posts and pages that use the template."),{type:"snackbar",actions:[{label:(0,fs.__)("Go back"),onClick:()=>g().onNavigateToPreviousEntityRecord()}]})},children:(0,fs.__)("Edit template")})}),x&&(0,L.jsx)(ou,{onClose:()=>v(!1)})]})}const uu=function(){const[e,t]=(0,u.useState)(null),s=(0,u.useMemo)((()=>({anchor:e,className:"editor-post-template__dropdown",placement:"left-start",offset:36,shift:!0})),[e]);return(0,L.jsx)(zd,{label:(0,fs.__)("Template"),ref:t,children:(0,L.jsx)(Uo.Dropdown,{popoverProps:s,focusOnMount:!0,renderToggle:({isOpen:e,onToggle:t})=>(0,L.jsx)(cu,{isOpen:e,onClick:t}),renderContent:({onClose:e})=>(0,L.jsx)(du,{onClose:e})})})},{PreferenceBaseOption:pu}=(window.wp.warning,$(k.privateApis));function mu(e){const{toggleEditorPanelEnabled:t}=(0,c.useDispatch)(Ac),{isChecked:s,isRemoved:o}=(0,c.useSelect)((t=>{const{isEditorPanelEnabled:s,isEditorPanelRemoved:o}=t(Ac);return{isChecked:s(e.panelName),isRemoved:o(e.panelName)}}),[e.panelName]);return o?null:(0,L.jsx)(pu,{isChecked:s,onChange:()=>t(e.panelName),...e})}const{Fill:hu,Slot:gu}=(0,Uo.createSlotFill)("EnablePluginDocumentSettingPanelOption"),_u=({label:e,panelName:t})=>(0,L.jsx)(hu,{children:(0,L.jsx)(mu,{label:e,panelName:t})});_u.Slot=gu;const fu=_u,{Fill:bu,Slot:yu}=(0,Uo.createSlotFill)("PluginDocumentSettingPanel"),xu=({name:e,className:t,title:s,icon:o,children:n})=>{const{name:i}=(0,wa.usePluginContext)(),r=`${i}/${e}`,{opened:a,isEnabled:l}=(0,c.useSelect)((e=>{const{isEditorPanelOpened:t,isEditorPanelEnabled:s}=e(Ac);return{opened:t(r),isEnabled:s(r)}}),[r]),{toggleEditorPanelOpened:d}=(0,c.useDispatch)(Ac);return(0,L.jsxs)(L.Fragment,{children:[(0,L.jsx)(fu,{label:s,panelName:r}),(0,L.jsx)(bu,{children:l&&(0,L.jsx)(Uo.PanelBody,{className:t,title:s,icon:o,opened:a,onToggle:()=>d(r),children:n})})]})};xu.Slot=yu;const vu=xu,Su=({allowedBlocks:e,icon:t,label:s,onClick:o,small:n,role:i})=>(0,L.jsx)(h.BlockSettingsMenuControls,{children:({selectedBlocks:r,onClose:a})=>((e,t)=>{return!Array.isArray(t)||(s=t,0===e.filter((e=>!s.includes(e))).length);var s})(r,e)?(0,L.jsx)(Uo.MenuItem,{onClick:(0,p.compose)(o,a),icon:t,label:n?s:void 0,role:i,children:!n&&s}):null});function wu(e){var t;const s=(0,wa.usePluginContext)();return(0,L.jsx)(Za,{name:"core/plugin-more-menu",as:null!==(t=e.as)&&void 0!==t?t:Uo.MenuItem,icon:e.icon||s.icon,...e})}const{Fill:ku,Slot:Cu}=(0,Uo.createSlotFill)("PluginPostPublishPanel"),Pu=({children:e,className:t,title:s,initialOpen:o=!1,icon:n})=>{const{icon:i}=(0,wa.usePluginContext)();return(0,L.jsx)(ku,{children:(0,L.jsx)(Uo.PanelBody,{className:t,initialOpen:o||!s,title:s,icon:null!=n?n:i,children:e})})};Pu.Slot=Cu;const ju=Pu,{Fill:Eu,Slot:Tu}=(0,Uo.createSlotFill)("PluginPostStatusInfo"),Bu=({children:e,className:t})=>(0,L.jsx)(Eu,{children:(0,L.jsx)(Uo.PanelRow,{className:t,children:e})});Bu.Slot=Tu;const Iu=Bu,{Fill:Nu,Slot:Du}=(0,Uo.createSlotFill)("PluginPrePublishPanel"),Au=({children:e,className:t,title:s,initialOpen:o=!1,icon:n})=>{const{icon:i}=(0,wa.usePluginContext)();return(0,L.jsx)(Nu,{children:(0,L.jsx)(Uo.PanelBody,{className:t,initialOpen:o||!s,title:s,icon:null!=n?n:i,children:e})})};Au.Slot=Du;const Ru=Au;function Mu(e){var t;const s=(0,wa.usePluginContext)();return(0,L.jsx)(Za,{name:"core/plugin-preview-menu",as:null!==(t=e.as)&&void 0!==t?t:Uo.MenuItem,icon:e.icon||s.icon,...e})}function Lu({className:e,...t}){return(0,L.jsx)(tl,{panelClassName:e,className:"editor-sidebar",scope:"core",...t})}function Ou(e){return(0,L.jsx)(Ka,{__unstableExplicitMenuItem:!0,scope:"core",...e})}function Fu({onClick:e}){const[t,s]=(0,u.useState)(!1),{postType:o,postId:n}=nu(),i=au(o),{editEntityRecord:r}=(0,c.useDispatch)(d.store);if(!i?.length)return null;return(0,L.jsxs)(L.Fragment,{children:[(0,L.jsx)(Uo.MenuItem,{onClick:()=>s(!0),children:(0,fs.__)("Change template")}),t&&(0,L.jsx)(Uo.Modal,{title:(0,fs.__)("Choose a template"),onRequestClose:()=>s(!1),overlayClassName:"editor-post-template__swap-template-modal",isFullScreen:!0,children:(0,L.jsx)("div",{className:"editor-post-template__swap-template-modal-content",children:(0,L.jsx)(Vu,{postType:o,onSelect:async t=>{r("postType",o,n,{template:t.name},{undoIgnore:!0}),s(!1),e()}})})})]})}function Vu({postType:e,onSelect:t}){const s=au(e),o=(0,u.useMemo)((()=>s.map((e=>({name:e.slug,blocks:(0,y.parse)(e.content.raw),title:(0,Lo.decodeEntities)(e.title.rendered),id:e.id})))),[s]);return(0,L.jsx)(h.__experimentalBlockPatternsList,{label:(0,fs.__)("Templates"),blockPatterns:o,onClickPattern:t})}function zu({onClick:e}){const t=lu(),s=iu(),{postType:o,postId:n}=nu(),{editEntityRecord:i}=(0,c.useDispatch)(d.store);return t&&s?(0,L.jsx)(Uo.MenuItem,{onClick:()=>{i("postType",o,n,{template:""},{undoIgnore:!0}),e()},children:(0,fs.__)("Use default template")}):null}function Uu({onClick:e}){const{canCreateTemplates:t}=(0,c.useSelect)((e=>{const{canUser:t}=e(d.store);return{canCreateTemplates:t("create",{kind:"postType",name:"wp_template"})}}),[]),[s,o]=(0,u.useState)(!1),n=iu();return t&&n?(0,L.jsxs)(L.Fragment,{children:[(0,L.jsx)(Uo.MenuItem,{onClick:()=>{o(!0)},children:(0,fs.__)("Create new template")}),s&&(0,L.jsx)(ou,{onClose:()=>{o(!1),e()}})]}):null}function Hu({id:e}){const{isTemplateHidden:t,onNavigateToEntityRecord:s,getEditorSettings:o,hasGoBack:n}=(0,c.useSelect)((e=>{const{getRenderingMode:t,getEditorSettings:s}=$(e(Ac)),o=s();return{isTemplateHidden:"post-only"===t(),onNavigateToEntityRecord:o.onNavigateToEntityRecord,getEditorSettings:s,hasGoBack:o.hasOwnProperty("onNavigateToPreviousEntityRecord")}}),[]),{get:i}=(0,c.useSelect)(k.store),{editedRecord:r,hasResolved:a}=(0,d.useEntityRecord)("postType","wp_template",e),{createSuccessNotice:l}=(0,c.useDispatch)(_s.store),{setRenderingMode:p,setDefaultRenderingMode:m}=$((0,c.useDispatch)(Ac)),h=(0,c.useSelect)((e=>!!e(d.store).canUser("create",{kind:"postType",name:"wp_template"})),[]),[g,_]=(0,u.useState)(null),f=(0,u.useMemo)((()=>({anchor:g,className:"editor-post-template__dropdown",placement:"left-start",offset:36,shift:!0})),[g]);if(!a)return null;const b=n?[{label:(0,fs.__)("Go back"),onClick:()=>o().onNavigateToPreviousEntityRecord()}]:void 0;return(0,L.jsx)(zd,{label:(0,fs.__)("Template"),ref:_,children:(0,L.jsx)(Uo.DropdownMenu,{popoverProps:f,focusOnMount:!0,toggleProps:{size:"compact",variant:"tertiary",tooltipPosition:"middle left"},label:(0,fs.__)("Template options"),text:(0,Lo.decodeEntities)(r.title),icon:null,children:({onClose:e})=>(0,L.jsxs)(L.Fragment,{children:[(0,L.jsxs)(Uo.MenuGroup,{children:[h&&(0,L.jsx)(Uo.MenuItem,{onClick:()=>{s({postId:r.id,postType:"wp_template"}),e(),i("core/edit-site","welcomeGuideTemplate")||l((0,fs.__)("Editing template. Changes made here affect all posts and pages that use the template."),{type:"snackbar",actions:b})},children:(0,fs.__)("Edit template")}),(0,L.jsx)(Fu,{onClick:e}),(0,L.jsx)(zu,{onClick:e}),h&&(0,L.jsx)(Uu,{onClick:e})]}),(0,L.jsx)(Uo.MenuGroup,{children:(0,L.jsx)(Uo.MenuItem,{icon:t?void 0:Ho,isSelected:!t,role:"menuitemcheckbox",onClick:()=>{const e=t?"template-locked":"post-only";p(e),m(e)},children:(0,fs.__)("Show template")})})]})})})}function Gu(){const{templateId:e,isBlockTheme:t}=(0,c.useSelect)((e=>{const{getCurrentTemplateId:t,getEditorSettings:s}=e(Ac);return{templateId:t(),isBlockTheme:s().__unstableIsBlockBasedTheme}}),[]),s=(0,c.useSelect)((e=>{var t;const s=e(Ac).getCurrentPostType(),o=e(d.store).getPostType(s);if(!o?.viewable)return!1;const n=e(Ac).getEditorSettings();if(!!n.availableTemplates&&Object.keys(n.availableTemplates).length>0)return!0;if(!n.supportsTemplateMode)return!1;return null!==(t=e(d.store).canUser("create",{kind:"postType",name:"wp_template"}))&&void 0!==t&&t}),[]),o=(0,c.useSelect)((e=>{var t;return null!==(t=e(d.store).canUser("read",{kind:"postType",name:"wp_template"}))&&void 0!==t&&t}),[]);return t&&o||!s?t&&e?(0,L.jsx)(Hu,{id:e}):null:(0,L.jsx)(uu,{})}const $u={_fields:"id,name",context:"view"},Wu={who:"authors",per_page:100,...$u};function Zu(e){const{authorId:t,authors:s,postAuthor:o,isLoading:n}=(0,c.useSelect)((t=>{const{getUser:s,getUsers:o,isResolving:n}=t(d.store),{getEditedPostAttribute:i}=t(Ac),r=i("author"),a={...Wu};return e&&(a.search=e,a.search_columns=["name"]),{authorId:r,authors:o(a),postAuthor:s(r,$u),isLoading:n("getUsers",[a])}}),[e]),i=(0,u.useMemo)((()=>{const e=(null!=s?s:[]).map((e=>({value:e.id,label:(0,Lo.decodeEntities)(e.name)}))),t=e.findIndex((({value:e})=>o?.id===e));let n=[];return t<0&&o?n=[{value:o.id,label:(0,Lo.decodeEntities)(o.name)}]:t<0&&!o&&(n=[{value:0,label:(0,fs.__)("(No author)")}]),[...n,...e]}),[s,o]);return{authorId:t,authorOptions:i,postAuthor:o,isLoading:n}}function Yu(){const[e,t]=(0,u.useState)(),{editPost:s}=(0,c.useDispatch)(Ac),{authorId:o,authorOptions:n,isLoading:i}=Zu(e);return(0,L.jsx)(Uo.ComboboxControl,{__nextHasNoMarginBottom:!0,__next40pxDefaultSize:!0,label:(0,fs.__)("Author"),options:n,value:o,onFilterValueChange:(0,p.debounce)((e=>{t(e)}),300),onChange:e=>{e&&s({author:e})},allowReset:!1,hideLabelFromVision:!0,isLoading:i})}function Ku(){const{editPost:e}=(0,c.useDispatch)(Ac),{authorId:t,authorOptions:s}=Zu();return(0,L.jsx)(Uo.SelectControl,{__next40pxDefaultSize:!0,__nextHasNoMarginBottom:!0,className:"post-author-selector",label:(0,fs.__)("Author"),options:s,onChange:t=>{const s=Number(t);e({author:s})},value:t,hideLabelFromVision:!0})}const qu=function(){return(0,c.useSelect)((e=>{const t=e(d.store).getUsers(Wu);return t?.length>=25}),[])?(0,L.jsx)(Yu,{}):(0,L.jsx)(Ku,{})};function Qu({children:e}){const{hasAssignAuthorAction:t,hasAuthors: <AUTHORS>