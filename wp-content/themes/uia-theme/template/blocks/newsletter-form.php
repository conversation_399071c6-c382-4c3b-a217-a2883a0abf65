<section data-input-element id="<?= $options['id']; ?>" class="block-newsletter-form <?= render_options($options); ?>">
  <div class="container">
    <div class="row">

      <div class="col-lg-3 offset-lg-3">
        <div class="title">
          <?= $fields['title']; ?>
        </div>
      </div>

      <div class="col-lg-6">
        <div class="form-container">
          <?php
          // Verifica se Contact Form 7 è attivo e il form esiste
          if (function_exists('wpcf7_contact_form') && !empty($fields['form'])) {
              // Estrai l'ID del form dal shortcode
              preg_match('/id="([^"]+)"/', $fields['form'], $matches);
              $form_id = isset($matches[1]) ? $matches[1] : '';

              // Verifica se il form esiste
              if ($form_id) {
                  $contact_form = wpcf7_contact_form($form_id);
                  if ($contact_form && !$contact_form->initial()) {
                      // Form esiste, renderizzalo
                      echo do_shortcode($fields['form']);
                  } else {
                      // Form non esiste
                      echo '<div class="alert alert-warning">';
                      echo '<p><strong>Attenzione:</strong> Il form della newsletter non è configurato correttamente.</p>';
                      echo '<p>Form ID: <code>' . esc_html($form_id) . '</code> non trovato.</p>';
                      echo '</div>';
                  }
              } else {
                  echo '<div class="alert alert-warning">';
                  echo '<p><strong>Attenzione:</strong> Shortcode del form non valido.</p>';
                  echo '</div>';
              }
          } else {
              echo '<div class="alert alert-warning">';
              echo '<p><strong>Attenzione:</strong> Contact Form 7 non è attivo o il form non è configurato.</p>';
              echo '</div>';
          }
          ?>
        </div>
      </div>

    </div>
  </div>

  <div id="rectangle-element-newsletter-form" class="square-element-newsletter-form">
    <div data-depth='0.5' class="image">
      <?= get_svg('rectangle'); ?>
    </div>
  </div>

</section>