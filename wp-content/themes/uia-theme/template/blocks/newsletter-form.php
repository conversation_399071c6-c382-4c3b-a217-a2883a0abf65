<section data-input-element id="<?= $options['id']; ?>" class="block-newsletter-form <?= render_options($options); ?>">
  <div class="container">
    <div class="row">

      <div class="col-lg-3 offset-lg-3">
        <div class="title">
          <?= $fields['title']; ?>
        </div>
      </div>

      <div class="col-lg-6">
        <div class="form-container">
          <?php
          // Debug: mostra tutti i form CF7 disponibili
          if (function_exists('wpcf7_contact_form')) {
              echo '<div style="background: #f0f0f0; padding: 10px; margin: 10px 0; font-size: 12px;">';
              echo '<strong>DEBUG - Form CF7 disponibili:</strong><br>';

              $forms = get_posts(array(
                  'post_type' => 'wpcf7_contact_form',
                  'numberposts' => -1
              ));

              if ($forms) {
                  foreach ($forms as $form) {
                      echo 'ID: <code>' . $form->ID . '</code> - Titolo: ' . $form->post_title . '<br>';
                  }
              } else {
                  echo 'Nessun form trovato!<br>';
              }

              echo 'Form richiesto: <code>a326ce9</code><br>';
              echo '</div>';
          }

          // Debug: mostra il contenuto del form ID 171
          if (function_exists('wpcf7_contact_form')) {
              $test_form = wpcf7_contact_form(171);
              if ($test_form && !$test_form->initial()) {
                  echo '<div style="background: #fff3cd; border: 1px solid #ffeaa7; padding: 10px; margin: 10px 0; font-size: 12px;">';
                  echo '<strong>DEBUG - Contenuto form ID 171:</strong><br>';
                  echo '<pre style="font-size: 10px; max-height: 200px; overflow: auto;">';
                  echo htmlspecialchars($test_form->prop('form'));
                  echo '</pre>';
                  echo '</div>';
              }
          }

          // Per ora NON renderizziamo il form per evitare errori JS
          echo '<div class="alert alert-info" style="background: #d1ecf1; border: 1px solid #bee5eb; padding: 10px; margin: 10px 0;">';
          echo '<p><strong>Info:</strong> Form temporaneamente disabilitato per debug JavaScript.</p>';
          echo '<p>Aggiorna il campo ACF con: <code>[contact-form-7 id="171" title="Newsletter Form"]</code></p>';
          echo '</div>';
          ?>
        </div>
      </div>

    </div>
  </div>

  <div id="rectangle-element-newsletter-form" class="square-element-newsletter-form">
    <div data-depth='0.5' class="image">
      <?= get_svg('rectangle'); ?>
    </div>
  </div>

</section>