<section data-input-element id="<?= $options['id']; ?>" class="block-newsletter-form <?= render_options($options); ?>">
  <div class="container">
    <div class="row">

      <div class="col-lg-3 offset-lg-3">
        <div class="title">
          <?= $fields['title']; ?>
        </div>
      </div>

      <div class="col-lg-6">
        <div class="form-container">
          <?php
          // Debug: mostra tutti i form CF7 disponibili
          if (function_exists('wpcf7_contact_form')) {
              echo '<div style="background: #f0f0f0; padding: 10px; margin: 10px 0; font-size: 12px;">';
              echo '<strong>DEBUG - Form CF7 disponibili:</strong><br>';

              $forms = get_posts(array(
                  'post_type' => 'wpcf7_contact_form',
                  'numberposts' => -1
              ));

              if ($forms) {
                  foreach ($forms as $form) {
                      echo 'ID: <code>' . $form->ID . '</code> - <PERSON>lo: ' . $form->post_title . '<br>';
                  }
              } else {
                  echo 'Nessun form trovato!<br>';
              }

              echo 'Form richiesto: <code>a326ce9</code><br>';
              echo '</div>';
          }

          // TEMPORANEO: usa il form Newsletter Form (ID: 171) se quello configurato non esiste
          $fallback_shortcode = '[contact-form-7 id="171" title="Newsletter Form"]';

          // Verifica se Contact Form 7 è attivo e il form esiste
          if (function_exists('wpcf7_contact_form') && !empty($fields['form'])) {
              // Estrai l'ID del form dal shortcode
              preg_match('/id="([^"]+)"/', $fields['form'], $matches);
              $form_id = isset($matches[1]) ? $matches[1] : '';

              // Verifica se il form esiste
              if ($form_id) {
                  $contact_form = wpcf7_contact_form($form_id);
                  if ($contact_form && !$contact_form->initial()) {
                      // Form esiste, renderizzalo
                      echo do_shortcode($fields['form']);
                  } else {
                      // Form non esiste, usa il fallback
                      echo '<div class="alert alert-info" style="background: #d1ecf1; border: 1px solid #bee5eb; padding: 10px; margin: 10px 0;">';
                      echo '<p><strong>Info:</strong> Usando form di fallback (ID: 171) perché il form configurato non esiste.</p>';
                      echo '</div>';
                      echo do_shortcode($fallback_shortcode);
                  }
              } else {
                  echo '<div class="alert alert-warning">';
                  echo '<p><strong>Attenzione:</strong> Shortcode del form non valido. Usando fallback.</p>';
                  echo '</div>';
                  echo do_shortcode($fallback_shortcode);
              }
          } else {
              echo '<div class="alert alert-warning">';
              echo '<p><strong>Attenzione:</strong> Contact Form 7 non è attivo o il form non è configurato.</p>';
              echo '</div>';
          }
          ?>
        </div>
      </div>

    </div>
  </div>

  <div id="rectangle-element-newsletter-form" class="square-element-newsletter-form">
    <div data-depth='0.5' class="image">
      <?= get_svg('rectangle'); ?>
    </div>
  </div>

</section>