<section data-input-element id="<?= $options['id']; ?>" class="block-newsletter-form <?= render_options($options); ?>">
  <div class="container">
    <div class="row">

      <div class="col-lg-3 offset-lg-3">
        <div class="title">
          <?= $fields['title']; ?>
        </div>
      </div>

      <div class="col-lg-6">
        <div class="form-container">
          <?php
          echo "<!-- DEBUG: form field content: " . htmlspecialchars($fields['form']) . " -->";
          echo "<!-- DEBUG: Before do_shortcode -->";

          // Verifica se Contact Form 7 è attivo
          if (function_exists('wpcf7_contact_form')) {
              echo "<!-- DEBUG: CF7 function exists -->";

              // Estrai l'ID del form dal shortcode
              preg_match('/id="([^"]+)"/', $fields['form'], $matches);
              $form_id = isset($matches[1]) ? $matches[1] : '';
              echo "<!-- DEBUG: Form ID extracted: " . $form_id . " -->";

              // Verifica se il form esiste
              if ($form_id) {
                  $contact_form = wpcf7_contact_form($form_id);
                  if ($contact_form && !$contact_form->initial()) {
                      echo "<!-- DEBUG: Form exists -->";
                      $form_output = do_shortcode($fields['form']);
                      echo "<!-- DEBUG: After do_shortcode, output length: " . strlen($form_output) . " -->";
                      echo $form_output;
                  } else {
                      echo "<!-- DEBUG: Form does NOT exist -->";
                      echo "<p>Form Contact Form 7 con ID '{$form_id}' non trovato</p>";
                  }
              } else {
                  echo "<!-- DEBUG: Could not extract form ID -->";
                  echo "<p>Impossibile estrarre l'ID del form</p>";
              }
          } else {
              echo "<!-- DEBUG: CF7 function NOT exists -->";
              echo "<p>Contact Form 7 plugin non è attivo</p>";
          }

          echo "<!-- DEBUG: End of form container -->";
          ?>
        </div>
      </div>

    </div>
  </div>

  <div id="rectangle-element-newsletter-form" class="square-element-newsletter-form">
    <div data-depth='0.5' class="image">
      <?= get_svg('rectangle'); ?>
    </div>
  </div>

</section>