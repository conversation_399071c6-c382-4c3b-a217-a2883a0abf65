<?php 
// Block Title + Link
// ACF Fields
$link = $fields['link'];
$file = $fields['link_brochure'];
?>



<section id="<?= $options['id']; ?>" class="block-title-link <?= render_options($options); ?>">
  <div class="container">
    <div class="row">
      <div class="col-md-8">
        <div class="title <?= $fields['title_color']; ?>">
          <?= $fields['title']; ?>
        </div>
      </div>
        <div class="col-md-4">
          <?php if ($link) : ?>
            <a href="<?= $link['url'] ?>" target="<?= $link['target'] ?>" title="<?= $link['title'] ?>">
              <div class="btn btn-black"><?= $link['title'] ?></div>
            </a>
          <?php endif; ?>

          <?php if ($file) : ?>
            <button type="button" class="btn btn-black brochure-download-btn"
                    data-toggle="modal"
                    data-target="#brochureModal"
                    data-file-url="<?= esc_attr($file['url']) ?>"
                    data-file-name="<?= esc_attr($file['filename']) ?>"
                    data-file-title="<?= esc_attr($file['title']) ?>">
              Scarica Brochure
            </button>
          <?php endif; ?>
        </div>
    </div>
  </div>

  <?php
  $floatingElement = $fields['floating_element'];
  if ($floatingElement) : ?>

    <div id="square-element-title-link" class="square-element-title-link">
      <div data-depth='0.7' class="image">
        <?= get_svg('square-magenta'); ?>
      </div>
    </div>

  <?php endif; ?>
</section>

<!-- Modale Bootstrap per richiesta brochure -->
<div class="modal fade" id="brochureModal" tabindex="-1" role="dialog" aria-labelledby="brochureModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="brochureModalLabel">Richiedi Brochure</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <div class="brochure-info mb-3">
          <p>Compila il form per ricevere la brochure via email.</p>
          <div class="file-info">
            <strong>File richiesto:</strong> <span id="modal-file-name"></span>
          </div>
        </div>

        <!-- Form Contact Form 7 per richiesta brochure -->
        <div id="brochure-form-container">
          <?php
          // Shortcode CF7 per richiesta brochure
          // NOTA: Sostituire con l'ID del form CF7 creato per le brochure
          echo do_shortcode('[contact-form-7 id="9a6af79" title="Scarica Brochure Corso triennale"]');
          ?>

          <!-- Form di esempio se non hai ancora creato il CF7 -->
          <div class="example-form" style="display: none;">
            <form class="wpcf7-form">
              <p>
                <label>Nome *<br>
                <input type="text" name="your-name" required>
                </label>
              </p>
              <p>
                <label>Email *<br>
                <input type="email" name="your-email" required>
                </label>
              </p>
              <p>
                <label>Messaggio<br>
                <textarea name="your-message" rows="4"></textarea>
                </label>
              </p>
              <p>
                <input type="submit" value="Invia richiesta" class="wpcf7-submit">
              </p>
            </form>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>