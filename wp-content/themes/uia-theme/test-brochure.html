<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Funzionalità Brochure</title>
    <link href="https://stackpath.bootstrapcdn.com/bootstrap/4.3.1/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .btn-black {
            background-color: #333;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 25px;
            text-transform: uppercase;
        }
        .btn-black:hover {
            background-color: #555;
            color: white;
        }
        .brochure-info {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #e91e63;
            margin-bottom: 20px;
        }
        .file-info strong {
            color: #e91e63;
        }
    </style>
</head>
<body>
    <div class="container mt-5">
        <h1>Test Funzionalità Brochure</h1>
        
        <!-- Simulazione del blocco title-link -->
        <section class="block-title-link">
            <div class="container">
                <div class="row">
                    <div class="col-md-8">
                        <div class="title">
                            <h2>Scarica la nostra Brochure</h2>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <button type="button" class="btn btn-black brochure-download-btn" 
                                data-toggle="modal" 
                                data-target="#brochureModal" 
                                data-file-url="https://example.com/brochure.pdf" 
                                data-file-name="brochure-esempio.pdf"
                                data-file-title="Brochure Esempio">
                            Scarica Brochure
                        </button>
                    </div>
                </div>
            </div>
        </section>
    </div>

    <!-- Modale Bootstrap per richiesta brochure -->
    <div class="modal fade" id="brochureModal" tabindex="-1" role="dialog" aria-labelledby="brochureModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header" style="background-color: #333; color: white; border-bottom: 3px solid #e91e63;">
                    <h5 class="modal-title" id="brochureModalLabel">Richiedi Brochure</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close" style="color: white;">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="brochure-info">
                        <p>Compila il form per ricevere la brochure via email.</p>
                        <div class="file-info">
                            <strong>File richiesto:</strong> <span id="modal-file-name">Nome file apparirà qui</span>
                        </div>
                    </div>
                    
                    <!-- Form di test -->
                    <form id="test-form">
                        <div class="form-group">
                            <label for="your-name">Nome *</label>
                            <input type="text" class="form-control" id="your-name" name="your-name" required>
                        </div>
                        <div class="form-group">
                            <label for="your-email">Email *</label>
                            <input type="email" class="form-control" id="your-email" name="your-email" required>
                        </div>
                        <div class="form-group">
                            <label for="your-message">Messaggio</label>
                            <textarea class="form-control" id="your-message" name="your-message" rows="4"></textarea>
                        </div>
                        <input type="hidden" id="brochure_file_url" name="brochure_file_url" value="">
                        <button type="submit" class="btn" style="background-color: #e91e63; color: white; border-radius: 25px; padding: 12px 30px;">
                            Invia richiesta
                        </button>
                    </form>
                    
                    <div id="test-response" class="mt-3" style="display: none;">
                        <div class="alert alert-success">
                            <strong>Test completato!</strong> La funzionalità sta funzionando correttamente.
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://code.jquery.com/jquery-3.4.1.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.3.1/js/bootstrap.min.js"></script>
    
    <script>
        $(document).ready(function() {
            // Gestione click sui bottoni brochure
            $('.brochure-download-btn').on('click', function(e) {
                e.preventDefault();
                
                // Recupera i dati del file dal bottone
                const fileUrl = $(this).data('file-url');
                const fileName = $(this).data('file-name');
                const fileTitle = $(this).data('file-title');
                
                console.log('File data:', { fileUrl, fileName, fileTitle });
                
                // Aggiorna il contenuto della modale
                $('#modal-file-name').text(fileName || fileTitle || 'Brochure');
                
                // Popola il campo nascosto nel form con l'URL del file
                $('#brochure_file_url').val(fileUrl);
                
                console.log('Campo nascosto popolato con:', fileUrl);
                
                // Apri la modale
                $('#brochureModal').modal('show');
            });
            
            // Gestione invio form di test
            $('#test-form').on('submit', function(e) {
                e.preventDefault();
                
                const formData = {
                    name: $('#your-name').val(),
                    email: $('#your-email').val(),
                    message: $('#your-message').val(),
                    fileUrl: $('#brochure_file_url').val()
                };
                
                console.log('Dati form:', formData);
                
                // Simula invio
                setTimeout(function() {
                    $('#test-response').show();
                    setTimeout(function() {
                        $('#brochureModal').modal('hide');
                    }, 2000);
                }, 1000);
            });
        });
    </script>
</body>
</html>
