<?php
/**
 * Registrazione stringhe WPML per il sistema brochure
 * 
 * Questo file registra tutte le stringhe utilizzate nel sistema brochure
 * per renderle traducibili tramite WPML String Translation
 */

// Previeni accesso diretto
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Registra le stringhe per WPML String Translation
 */
function register_brochure_wpml_strings() {
    if (function_exists('icl_register_string')) {
        
        // Stringhe della modale
        icl_register_string('uia-theme', 'Richiedi Brochure', 'Richiedi Brochure');
        icl_register_string('uia-theme', 'Chiudi', 'Chiudi');
        icl_register_string('uia-theme', 'Compila il form per ricevere la brochure via email.', 'Compila il form per ricevere la brochure via email.');
        icl_register_string('uia-theme', 'File richiesto:', 'File richiesto:');
        
        // Stringhe del pulsante
        icl_register_string('uia-theme', 'Scarica Brochure', 'Scarica Brochure');
        
        // Stringhe dell'email
        icl_register_string('uia-theme', 'File Brochure', 'File Brochure');
        icl_register_string('uia-theme', 'Nome file:', 'Nome file:');
        icl_register_string('uia-theme', 'Scarica il file:', 'Scarica il file:');
        icl_register_string('uia-theme', 'Link diretto:', 'Link diretto:');
        icl_register_string('uia-theme', 'Nota: Il file è anche allegato a questa email per comodità.', 'Nota: Il file è anche allegato a questa email per comodità.');
    }
}

// Hook per registrare le stringhe all'inizializzazione
add_action('init', 'register_brochure_wpml_strings');

/**
 * Funzione helper per ottenere stringhe tradotte
 * Utilizza WPML se disponibile, altrimenti fallback su __()
 */
function get_brochure_translated_string($string, $name = null) {
    if (function_exists('icl_t')) {
        return icl_t('uia-theme', $name ?: $string, $string);
    }
    return __($string, 'uia-theme');
}
