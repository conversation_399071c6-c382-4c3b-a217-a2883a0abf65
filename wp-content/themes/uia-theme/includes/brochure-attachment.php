<?php
/**
 * Gestione allegati brochure per Contact Form 7
 * 
 * Questo file gestisce l'allegato automatico dei file brochure
 * alle email inviate tramite Contact Form 7 dalla modale
 */

// Previeni accesso diretto
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Aggiunge l'allegato brochure alle email di Contact Form 7
 * 
 * @param WPCF7_Mail $mail L'oggetto mail di CF7
 * @param WPCF7_ContactForm $contact_form Il form di contatto
 */
function add_brochure_attachment_to_cf7_mail($mail, $contact_form) {
    // Verifica se è il form della modale brochure
    $form_id = $contact_form->id();
    
    // Lista degli ID dei form che dovrebbero avere l'allegato brochure
    // Aggiorna questo array con gli ID dei form che useranno questa funzionalità
    $brochure_form_ids = array(
        // Aggiungi qui gli ID dei form CF7 per le brochure
        // Esempio: 123, 456, 789
    );
    
    if (!in_array($form_id, $brochure_form_ids)) {
        return $mail;
    }
    
    // Recupera l'URL del file dalla sessione o dai dati POST
    $file_url = '';
    
    // Prova a recuperare l'URL del file dai dati della sessione
    if (session_status() == PHP_SESSION_NONE) {
        session_start();
    }
    
    if (isset($_SESSION['brochure_file_url'])) {
        $file_url = $_SESSION['brochure_file_url'];
        // Pulisci la sessione dopo l'uso
        unset($_SESSION['brochure_file_url']);
    }
    
    // Se non trovato nella sessione, prova dai dati POST
    if (empty($file_url) && isset($_POST['brochure_file_url'])) {
        $file_url = sanitize_url($_POST['brochure_file_url']);
    }
    
    // Se abbiamo un URL del file, allegalo alla mail
    if (!empty($file_url)) {
        // Verifica che l'URL sia valido e appartenga al sito
        if (filter_var($file_url, FILTER_VALIDATE_URL) && 
            strpos($file_url, home_url()) === 0) {
            
            // Converti l'URL in percorso del file system
            $file_path = str_replace(home_url(), ABSPATH, $file_url);
            $file_path = str_replace('/', DIRECTORY_SEPARATOR, $file_path);
            
            // Verifica che il file esista
            if (file_exists($file_path)) {
                // Ottieni gli allegati attuali
                $attachments = $mail->get('attachments');
                if (!is_array($attachments)) {
                    $attachments = array();
                }
                
                // Aggiungi il file agli allegati
                $attachments[] = $file_path;
                
                // Imposta gli allegati aggiornati
                $mail->set('attachments', $attachments);
                
                // Log per debug (opzionale)
                error_log('Brochure allegata: ' . $file_path);
            } else {
                error_log('File brochure non trovato: ' . $file_path);
            }
        } else {
            error_log('URL brochure non valido: ' . $file_url);
        }
    }
    
    return $mail;
}

// Hook per modificare la mail prima dell'invio
add_filter('wpcf7_mail_components', 'add_brochure_attachment_to_cf7_mail', 10, 2);

/**
 * Gestisce l'invio dei dati del file tramite AJAX
 */
function handle_brochure_file_data() {
    // Verifica nonce per sicurezza
    if (!wp_verify_nonce($_POST['nonce'], 'brochure_nonce')) {
        wp_die('Accesso negato');
    }
    
    $file_url = sanitize_url($_POST['file_url']);
    
    // Salva l'URL del file nella sessione
    if (session_status() == PHP_SESSION_NONE) {
        session_start();
    }
    
    $_SESSION['brochure_file_url'] = $file_url;
    
    wp_send_json_success(array('message' => 'File URL salvato'));
}

// Hook AJAX per utenti loggati e non loggati
add_action('wp_ajax_save_brochure_file', 'handle_brochure_file_data');
add_action('wp_ajax_nopriv_save_brochure_file', 'handle_brochure_file_data');

/**
 * Aggiunge un campo nascosto al form CF7 con l'URL del file
 */
function add_brochure_hidden_field_to_cf7($form, $contact_form) {
    // Verifica se è un form per brochure
    $form_id = $contact_form->id();
    
    $brochure_form_ids = array(
        // Stessi ID del filtro precedente
    );
    
    if (in_array($form_id, $brochure_form_ids)) {
        // Aggiungi un campo nascosto per l'URL del file
        $hidden_field = '<input type="hidden" name="brochure_file_url" id="brochure_file_url" value="" />';
        $form = $form . $hidden_field;
    }
    
    return $form;
}

// Hook per modificare il form HTML
add_filter('wpcf7_form_elements', 'add_brochure_hidden_field_to_cf7', 10, 2);

/**
 * Aggiunge le variabili AJAX necessarie per il frontend
 */
function enqueue_brochure_ajax_script() {
    // Localizza lo script con le variabili AJAX
    wp_localize_script('jquery', 'ajax_object', array(
        'ajax_url' => admin_url('admin-ajax.php'),
        'nonce' => wp_create_nonce('brochure_nonce')
    ));
}

// Hook per aggiungere le variabili AJAX
add_action('wp_enqueue_scripts', 'enqueue_brochure_ajax_script');
