<?php
/**
 * Shortcode per allegare automaticamente file brochure ai form Contact Form 7
 *
 * Uso: [brochure_attachment]
 *
 * Recupera automaticamente il file dal campo ACF 'link_brochure' della pagina corrente
 */

// Previeni accesso diretto
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Shortcode per gestire l'allegato brochure
 *
 * @param array $atts Attributi dello shortcode
 * @return string HTML con campo nascosto contenente l'ID del file
 */
function brochure_attachment_shortcode($atts) {
    // Debug: forza output visibile per test
    $debug_output = '<div style="background: yellow; padding: 10px; margin: 10px 0;">';
    $debug_output .= '<strong>DEBUG SHORTCODE BROCHURE:</strong><br>';

    // Ottieni l'ID della pagina corrente
    $page_id = get_the_ID();
    $debug_output .= 'Page ID: ' . ($page_id ? $page_id : 'NULL') . '<br>';

    if (!$page_id) {
        $debug_output .= 'ERRORE: Impossibile ottenere ID pagina<br>';
        $debug_output .= '</div>';
        return $debug_output;
    }

    // Recupera il campo ACF file dalla pagina corrente
    $file = get_field('link_brochure', $page_id);
    $debug_output .= 'Campo ACF link_brochure: ' . (is_array($file) ? 'ARRAY TROVATO' : 'NULL/VUOTO') . '<br>';

    if (is_array($file)) {
        $debug_output .= 'File ID: ' . (isset($file['ID']) ? $file['ID'] : 'MANCANTE') . '<br>';
        $debug_output .= 'File URL: ' . (isset($file['url']) ? $file['url'] : 'MANCANTE') . '<br>';
        $debug_output .= 'File Name: ' . (isset($file['filename']) ? $file['filename'] : 'MANCANTE') . '<br>';
    }

    if (!$file || !is_array($file) || empty($file['ID'])) {
        $debug_output .= 'ERRORE: Campo link_brochure non trovato o vuoto<br>';
        $debug_output .= '</div>';
        return $debug_output;
    }

    $file_id = intval($file['ID']);

    // Verifica che il file esista
    $file_path = get_attached_file($file_id);
    $debug_output .= 'File path: ' . ($file_path ? $file_path : 'NULL') . '<br>';
    $debug_output .= 'File exists: ' . (file_exists($file_path) ? 'SÌ' : 'NO') . '<br>';

    if (!$file_path || !file_exists($file_path)) {
        $debug_output .= 'ERRORE: File non trovato nel filesystem<br>';
        $debug_output .= '</div>';
        return $debug_output;
    }

    // Aggiungi l'hook per allegare il file (solo per questa richiesta)
    add_filter('wpcf7_mail_components', 'attach_brochure_to_cf7_mail', 10, 3);

    $debug_output .= 'SUCCESS: Tutto OK, campi nascosti aggiunti<br>';
    $debug_output .= '</div>';

    // Restituisci debug + campi nascosti
    return $debug_output . '<input type="hidden" name="brochure_file_id" value="' . esc_attr($file_id) . '" style="display:none;" />
            <input type="hidden" name="brochure_file_url" value="' . esc_attr($file['url']) . '" style="display:none;" />
            <input type="hidden" name="brochure_file_name" value="' . esc_attr($file['filename']) . '" style="display:none;" />
            <!-- Brochure attachment: file_id ' . $file_id . ' (' . esc_html($file['filename']) . ') registrato -->';
}

/**
 * Funzione che allega il file brochure alla mail CF7
 */
function attach_brochure_to_cf7_mail($components, $contact_form, $mail) {
    // Debug: log tutti i dati POST
    error_log('DEBUG CF7 POST data: ' . print_r($_POST, true));

    // Recupera l'ID del file dal campo nascosto inviato dal form
    if (!isset($_POST['brochure_file_id']) || empty($_POST['brochure_file_id'])) {
        error_log('DEBUG: brochure_file_id non trovato nei dati POST');
        return $components;
    }

    $file_id_raw = $_POST['brochure_file_id'];
    error_log('DEBUG: File ID raw ricevuto: ' . $file_id_raw);

    // Se l'ID è "auto", recupera il file dalla pagina corrente
    if ($file_id_raw === 'auto') {
        // Prova a recuperare l'ID della pagina dai referrer o dai dati POST
        $page_id = null;

        // Metodo 1: da HTTP_REFERER
        if (isset($_SERVER['HTTP_REFERER'])) {
            $referer_url = $_SERVER['HTTP_REFERER'];
            $page_id = url_to_postid($referer_url);
            error_log('DEBUG: Page ID da referer: ' . $page_id);
        }

        // Metodo 2: se non funziona, prova con l'ultima pagina visitata
        if (!$page_id && isset($_POST['_wpcf7_container_post'])) {
            $page_id = intval($_POST['_wpcf7_container_post']);
            error_log('DEBUG: Page ID da CF7 container: ' . $page_id);
        }

        if ($page_id) {
            $file = get_field('link_brochure', $page_id);
            if ($file && is_array($file) && !empty($file['ID'])) {
                $file_id = intval($file['ID']);
                error_log('DEBUG: File ID recuperato da ACF: ' . $file_id);
            } else {
                error_log('DEBUG: Campo ACF link_brochure non trovato per page ID: ' . $page_id);
                return $components;
            }
        } else {
            error_log('DEBUG: Impossibile determinare page ID');
            return $components;
        }
    } else {
        $file_id = intval($file_id_raw);
    }

    error_log('DEBUG: File ID finale: ' . $file_id);

    // Ottieni il percorso del file
    $file_path = get_attached_file($file_id);
    error_log('DEBUG: File path: ' . ($file_path ? $file_path : 'NULL'));

    if ($file_path && file_exists($file_path)) {
        // Debug info file
        $file_size = filesize($file_path);
        $file_info = pathinfo($file_path);
        error_log('DEBUG: File trovato - Size: ' . $file_size . ' bytes, Extension: ' . $file_info['extension']);

        // Aggiungi il file agli allegati
        if (!isset($components['attachments'])) {
            $components['attachments'] = array();
        }

        $components['attachments'][] = $file_path;

        // Debug: mostra tutti gli allegati
        error_log('DEBUG: Allegati totali: ' . print_r($components['attachments'], true));

        // Log per debug
        error_log('SUCCESS: Brochure allegata: ' . $file_path . ' (ID: ' . $file_id . ')');
    } else {
        error_log('ERROR: File brochure non trovato: ID ' . $file_id . ', Path: ' . ($file_path ? $file_path : 'NULL'));
    }

    // Aggiungi l'URL del file al corpo del messaggio se disponibile
    if (isset($_POST['brochure_file_url']) && isset($_POST['brochure_file_name'])) {
        $file_url = sanitize_url($_POST['brochure_file_url']);
        $file_name = sanitize_text_field($_POST['brochure_file_name']);

        // Modifica il corpo del messaggio per includere l'URL del file
        if (isset($components['body'])) {
            $components['body'] .= "\n\n--- File Brochure ---\n";
            $components['body'] .= "Nome file: " . $file_name . "\n";
            $components['body'] .= "URL file: " . $file_url . "\n";
            $components['body'] .= "Nota: Il file è anche allegato a questa email.\n";
        }

        error_log('DEBUG: URL file aggiunto al messaggio: ' . $file_url);
    }

    return $components;
}

// Registra lo shortcode
add_shortcode('brochure_attachment', 'brochure_attachment_shortcode');
