<?php
/**
 * Shortcode per allegare automaticamente file brochure ai form Contact Form 7
 *
 * Uso: [brochure_attachment]
 *
 * Recupera automaticamente il file dal campo ACF 'link_brochure' della pagina corrente
 */

// Previeni accesso diretto
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Shortcode per gestire l'allegato brochure
 *
 * @param array $atts Attributi dello shortcode
 * @return string HTML con campo nascosto contenente l'ID del file
 */
function brochure_attachment_shortcode($atts) {
    // Ottieni l'ID della pagina corrente
    $page_id = get_the_ID();

    if (!$page_id) {
        return '<!-- Brochure attachment: impossibile ottenere ID pagina -->';
    }

    // Recupera il campo ACF file dalla pagina corrente
    $file = get_field('link_brochure', $page_id);

    if (!$file || !is_array($file) || empty($file['ID'])) {
        return '<!-- Brochure attachment: campo link_brochure non trovato o vuoto -->';
    }

    $file_id = intval($file['ID']);

    // Verifica che il file esista
    $file_path = get_attached_file($file_id);
    if (!$file_path || !file_exists($file_path)) {
        return '<!-- Brochure attachment: file ID ' . $file_id . ' non trovato nel filesystem -->';
    }

    // Aggiungi l'hook per allegare il file (solo per questa richiesta)
    add_filter('wpcf7_mail_components', 'attach_brochure_to_cf7_mail', 10, 3);

    // Restituisci campi nascosti
    return '<input type="hidden" name="brochure_file_id" value="' . esc_attr($file_id) . '" style="display:none;" />
            <input type="hidden" name="brochure_file_url" value="' . esc_attr($file['url']) . '" style="display:none;" />
            <input type="hidden" name="brochure_file_name" value="' . esc_attr($file['filename']) . '" style="display:none;" />
            <!-- Brochure attachment: file_id ' . $file_id . ' (' . esc_html($file['filename']) . ') registrato -->';
}

/**
 * Funzione che allega il file brochure alla mail CF7
 */
function attach_brochure_to_cf7_mail($components, $contact_form, $mail) {
    // Recupera l'ID del file dal campo nascosto inviato dal form
    if (!isset($_POST['brochure_file_id']) || empty($_POST['brochure_file_id'])) {
        return $components;
    }

    $file_id_raw = $_POST['brochure_file_id'];

    // Se l'ID è "auto", recupera il file dalla pagina corrente
    if ($file_id_raw === 'auto') {
        $page_id = null;

        // Metodo 1: da HTTP_REFERER
        if (isset($_SERVER['HTTP_REFERER'])) {
            $referer_url = $_SERVER['HTTP_REFERER'];
            $page_id = url_to_postid($referer_url);
        }

        // Metodo 2: da CF7 container
        if (!$page_id && isset($_POST['_wpcf7_container_post'])) {
            $page_id = intval($_POST['_wpcf7_container_post']);
        }

        if ($page_id) {
            $file = get_field('link_brochure', $page_id);
            if ($file && is_array($file) && !empty($file['ID'])) {
                $file_id = intval($file['ID']);
            } else {
                return $components;
            }
        } else {
            return $components;
        }
    } else {
        $file_id = intval($file_id_raw);
    }

    // Ottieni il percorso del file
    $file_path = get_attached_file($file_id);

    if ($file_path && file_exists($file_path)) {
        // Aggiungi il file agli allegati
        if (!isset($components['attachments'])) {
            $components['attachments'] = array();
        }

        $components['attachments'][] = $file_path;
    }

    // Aggiungi l'URL del file al corpo del messaggio se disponibile
    if (isset($_POST['brochure_file_url']) && isset($_POST['brochure_file_name'])) {
        $file_url = sanitize_url($_POST['brochure_file_url']);
        $file_name = sanitize_text_field($_POST['brochure_file_name']);

        // Modifica il corpo del messaggio per includere l'URL del file
        if (isset($components['body'])) {
            $components['body'] .= "\n\n--- 📎 File Brochure ---\n";
            $components['body'] .= "Nome file: " . $file_name . "\n";
            $components['body'] .= "Scarica il file: " . $file_url . "\n";
            $components['body'] .= "\n🔗 Link diretto: <a href=\"" . $file_url . "\" target=\"_blank\">" . $file_name . "</a>\n";
            $components['body'] .= "\nNota: Il file è anche allegato a questa email per comodità.\n";
        }
    }

    return $components;
}

// Registra lo shortcode
add_shortcode('brochure_attachment', 'brochure_attachment_shortcode');

/**
 * Aggiunge automaticamente l'allegato al campo "File attachments" di CF7
 */
function add_brochure_to_cf7_attachments($attachments, $name, $contact_form) {
    // Solo se il campo si chiama "brochure_file_url"
    if ($name === 'brochure_file_url' && isset($_POST['brochure_file_id'])) {
        $file_id_raw = $_POST['brochure_file_id'];

        // Recupera il file ID (stesso logic della funzione precedente)
        if ($file_id_raw === 'auto') {
            $page_id = null;
            if (isset($_SERVER['HTTP_REFERER'])) {
                $page_id = url_to_postid($_SERVER['HTTP_REFERER']);
            }
            if (!$page_id && isset($_POST['_wpcf7_container_post'])) {
                $page_id = intval($_POST['_wpcf7_container_post']);
            }
            if ($page_id) {
                $file = get_field('link_brochure', $page_id);
                if ($file && is_array($file) && !empty($file['ID'])) {
                    $file_id = intval($file['ID']);
                } else {
                    return $attachments;
                }
            } else {
                return $attachments;
            }
        } else {
            $file_id = intval($file_id_raw);
        }

        // Ottieni il percorso del file
        $file_path = get_attached_file($file_id);
        if ($file_path && file_exists($file_path)) {
            $attachments[] = $file_path;
        }
    }

    return $attachments;
}

// Hook per aggiungere allegati automaticamente
add_filter('wpcf7_mail_tag_replaced_file', 'add_brochure_to_cf7_attachments', 10, 3);
