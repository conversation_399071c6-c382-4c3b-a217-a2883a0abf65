<?php
/**
 * Shortcode per allegare automaticamente file brochure ai form Contact Form 7
 *
 * Uso: [brochure_attachment]
 *
 * Recupera automaticamente il file dal campo ACF 'link_brochure' della pagina corrente
 */

// Previeni accesso diretto
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Shortcode per gestire l'allegato brochure
 *
 * @param array $atts Attributi dello shortcode
 * @return string HTML con campo nascosto contenente l'ID del file
 */
function brochure_attachment_shortcode($atts) {
    // Ottieni l'ID della pagina corrente
    $page_id = get_the_ID();

    if (!$page_id) {
        return '<!-- Brochure attachment: impossibile ottenere ID pagina -->';
    }

    // Recupera il campo ACF file dalla pagina corrente
    $file = get_field('link_brochure', $page_id);

    if (!$file || !is_array($file) || empty($file['ID'])) {
        return '<!-- Brochure attachment: campo link_brochure non trovato o vuoto -->';
    }

    $file_id = intval($file['ID']);

    // Verifica che il file esista
    $file_path = get_attached_file($file_id);
    if (!$file_path || !file_exists($file_path)) {
        return '<!-- Brochure attachment: file ID ' . $file_id . ' non trovato nel filesystem -->';
    }

    // Aggiungi l'hook per allegare il file (solo per questa richiesta)
    add_filter('wpcf7_mail_components', 'attach_brochure_to_cf7_mail', 10, 3);

    // Restituisci un campo nascosto con l'ID del file per il processing
    return '<input type="hidden" name="brochure_file_id" value="' . esc_attr($file_id) . '" />
            <!-- Brochure attachment: file_id ' . $file_id . ' (' . esc_html($file['filename']) . ') registrato -->';
}

/**
 * Funzione che allega il file brochure alla mail CF7
 */
function attach_brochure_to_cf7_mail($components, $contact_form, $mail) {
    // Recupera l'ID del file dal campo nascosto inviato dal form
    if (!isset($_POST['brochure_file_id']) || empty($_POST['brochure_file_id'])) {
        return $components;
    }

    $file_id = intval($_POST['brochure_file_id']);

    // Ottieni il percorso del file
    $file_path = get_attached_file($file_id);

    if ($file_path && file_exists($file_path)) {
        // Aggiungi il file agli allegati
        if (!isset($components['attachments'])) {
            $components['attachments'] = array();
        }

        $components['attachments'][] = $file_path;

        // Log per debug
        error_log('Brochure allegata: ' . $file_path . ' (ID: ' . $file_id . ')');
    } else {
        error_log('File brochure non trovato: ID ' . $file_id);
    }

    return $components;
}

// Registra lo shortcode
add_shortcode('brochure_attachment', 'brochure_attachment_shortcode');
