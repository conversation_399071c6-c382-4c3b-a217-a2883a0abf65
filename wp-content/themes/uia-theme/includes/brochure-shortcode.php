<?php
/**
 * Shortcode per allegare automaticamente file brochure ai form Contact Form 7
 * 
 * Uso: [brochure_attachment file_id="123"]
 * 
 * Dove file_id è l'ID del file nella Media Library di WordPress
 */

// Previeni accesso diretto
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Shortcode per gestire l'allegato brochure
 * 
 * @param array $atts Attributi dello shortcode
 * @return string HTML output (vuoto, ma registra il file per l'allegato)
 */
function brochure_attachment_shortcode($atts) {
    // Parametri di default
    $atts = shortcode_atts(array(
        'file_id' => '',
    ), $atts, 'brochure_attachment');
    
    $file_id = intval($atts['file_id']);
    
    if (empty($file_id)) {
        return '<!-- Brochure attachment: file_id mancante -->';
    }
    
    // Verifica che il file esista
    $file_url = wp_get_attachment_url($file_id);
    if (!$file_url) {
        return '<!-- Brochure attachment: file_id ' . $file_id . ' non trovato -->';
    }
    
    // Salva l'ID del file in una variabile globale per usarlo nell'hook CF7
    global $brochure_attachment_file_id;
    $brochure_attachment_file_id = $file_id;
    
    // Aggiungi l'hook per allegare il file (solo per questa richiesta)
    add_filter('wpcf7_mail_components', 'attach_brochure_to_cf7_mail', 10, 3);
    
    return '<!-- Brochure attachment: file_id ' . $file_id . ' registrato -->';
}

/**
 * Funzione che allega il file brochure alla mail CF7
 */
function attach_brochure_to_cf7_mail($components, $contact_form, $mail) {
    global $brochure_attachment_file_id;
    
    if (empty($brochure_attachment_file_id)) {
        return $components;
    }
    
    // Ottieni il percorso del file
    $file_path = get_attached_file($brochure_attachment_file_id);
    
    if ($file_path && file_exists($file_path)) {
        // Aggiungi il file agli allegati
        if (!isset($components['attachments'])) {
            $components['attachments'] = array();
        }
        
        $components['attachments'][] = $file_path;
        
        // Log per debug
        error_log('Brochure allegata: ' . $file_path);
    }
    
    return $components;
}

// Registra lo shortcode
add_shortcode('brochure_attachment', 'brochure_attachment_shortcode');
