<?php

/**
 * The template for displaying all single posts.
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/#single-post
 *
 * @package
 */

get_header(); ?>

<?php

while (have_posts()) : the_post();

    if (get_post_type() == "product") :
        get_template_part('woocommerce/single', 'product');
    else :
        get_template_part('post-type/single', get_post_format());
    endif;

endwhile; // End of the loop.

?>

<?php get_footer();
