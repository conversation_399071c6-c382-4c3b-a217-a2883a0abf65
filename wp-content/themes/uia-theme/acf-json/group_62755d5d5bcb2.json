{"key": "group_62755d5d5bcb2", "title": "Block - Columns Images", "fields": [{"key": "field_62755d5d5f4e2", "label": "Blocco", "name": "block-columns-images", "type": "group", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "layout": "block", "sub_fields": [{"key": "field_62755d5d60c7d", "label": "<PERSON><PERSON><PERSON><PERSON>", "name": "images", "type": "repeater", "instructions": "Inserisci almeno 2 immagini o massimo 3 per riga.", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "collapsed": "", "min": 2, "max": 3, "layout": "row", "button_label": "Aggiungi immagine", "sub_fields": [{"key": "field_62755d96df26e", "label": "<PERSON><PERSON><PERSON><PERSON>", "name": "image", "type": "image", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "return_format": "array", "preview_size": "medium", "library": "all", "min_width": "", "min_height": "", "min_size": "", "max_width": "", "max_height": "", "max_size": "", "mime_types": ""}, {"key": "field_62755dc2df26f", "label": "Didascalia", "name": "alt_text", "type": "text", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "placeholder": "", "prepend": "", "append": "", "maxlength": ""}]}]}, {"key": "field_62755d5d5f4ec", "label": "Opzioni", "name": "opzioni", "type": "clone", "instructions": "", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "clone": ["group_6256c5487525a"], "display": "seamless", "layout": "block", "prefix_label": 0, "prefix_name": 0}], "location": [[{"param": "post_status", "operator": "==", "value": "private"}]], "menu_order": 0, "position": "normal", "style": "default", "label_placement": "top", "instruction_placement": "label", "hide_on_screen": "", "active": true, "description": "", "show_in_rest": 0, "modified": 1651858906}