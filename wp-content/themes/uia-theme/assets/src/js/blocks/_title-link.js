import 'arrive';
import Parallax from 'parallax-js';

document.arrive('#square-element-title-link', {existing: true}, function(){

    var scene = document.getElementById('square-element-title-link');
    var parallaxInstance = new Parallax(scene);

});

// Gestione modale brochure - versione semplificata
console.log('Title-link script caricato');

// Aspetta che il DOM sia pronto
setTimeout(function() {
    console.log('Inizializzazione gestione brochure...');

    // Gestione click sui bottoni brochure
    document.addEventListener('click', function(e) {
        if (e.target && e.target.classList.contains('brochure-download-btn')) {
            console.log('Click su bottone brochure rilevato');
            e.preventDefault();

            const button = e.target;
            const fileUrl = button.getAttribute('data-file-url');
            const fileName = button.getAttribute('data-file-name');
            const fileTitle = button.getAttribute('data-file-title');

            console.log('Dati file:', { fileUrl, fileName, fileTitle });

            // Aggiorna il contenuto della modale
            const modalFileName = document.getElementById('modal-file-name');
            if (modalFileName) {
                modalFileName.textContent = fileName || fileTitle || 'Brochure';
                console.log('Nome file aggiornato nella modale');
            }

            // Memorizza l'URL del file
            window.currentBrochureFile = {
                url: fileUrl,
                name: fileName,
                title: fileTitle
            };

            // Popola il campo nascosto
            const hiddenField = document.getElementById('brochure_file_url');
            if (hiddenField) {
                hiddenField.value = fileUrl;
                console.log('Campo nascosto popolato');
            }

            // Prova ad aprire la modale
            const modal = document.getElementById('brochureModal');
            if (modal) {
                console.log('Modale trovata, tentativo di apertura...');

                // Prova jQuery prima
                if (typeof $ !== 'undefined' && $.fn.modal) {
                    console.log('Usando jQuery per aprire modale');
                    $(modal).modal('show');
                } else if (typeof bootstrap !== 'undefined') {
                    console.log('Usando Bootstrap 5 per aprire modale');
                    const bsModal = new bootstrap.Modal(modal);
                    bsModal.show();
                } else {
                    console.log('Fallback manuale per aprire modale');
                    modal.style.display = 'block';
                    modal.classList.add('show');
                    document.body.classList.add('modal-open');

                    // Aggiungi backdrop
                    const backdrop = document.createElement('div');
                    backdrop.className = 'modal-backdrop fade show';
                    backdrop.id = 'brochure-backdrop';
                    document.body.appendChild(backdrop);
                }
            } else {
                console.error('Modale non trovata!');
            }
        }
    });

    // Gestione chiusura modale
    document.addEventListener('click', function(e) {
        if (e.target && (e.target.classList.contains('close') || e.target.getAttribute('data-dismiss') === 'modal')) {
            const modal = document.getElementById('brochureModal');
            if (modal) {
                if (typeof $ !== 'undefined' && $.fn.modal) {
                    $(modal).modal('hide');
                } else {
                    modal.style.display = 'none';
                    modal.classList.remove('show');
                    document.body.classList.remove('modal-open');

                    const backdrop = document.getElementById('brochure-backdrop');
                    if (backdrop) {
                        backdrop.remove();
                    }
                }
            }
        }
    });

}, 1000);