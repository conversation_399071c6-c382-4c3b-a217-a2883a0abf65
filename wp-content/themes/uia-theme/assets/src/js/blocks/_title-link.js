import 'arrive';
import Parallax from 'parallax-js';

document.arrive('#square-element-title-link', {existing: true}, function(){

    var scene = document.getElementById('square-element-title-link');
    var parallaxInstance = new Parallax(scene);

});

// Gestione modale brochure
document.addEventListener('DOMContentLoaded', function() {

    // Gestisce il click sul pulsante "Scarica Brochure"
    const brochureBtn = document.querySelector('.brochure-download-btn');

    if (brochureBtn) {
        brochureBtn.addEventListener('click', function() {
            // Recupera i dati dal pulsante
            const fileUrl = this.getAttribute('data-file-url');
            const fileName = this.getAttribute('data-file-name');
            const fileTitle = this.getAttribute('data-file-title');



            // Aggiorna il nome del file nella modale
            const modalFileName = document.getElementById('modal-file-name');
            if (modalFileName) {
                modalFileName.textContent = fileName || fileTitle || 'File brochure';
            }

            // Popola i campi nascosti nel form CF7
            setTimeout(function() {
                const fileIdField = document.querySelector('input[name="brochure_file_id"]');
                const fileUrlField = document.querySelector('input[name="brochure_file_url"]');
                const fileNameField = document.querySelector('input[name="brochure_file_name"]');

                if (fileUrlField) {
                    fileUrlField.value = fileUrl || '';
                }

                if (fileNameField) {
                    fileNameField.value = fileName || '';
                }

                // Per il file ID, lo estraiamo dall'URL o lo impostiamo manualmente
                if (fileIdField) {
                    // Prova a estrarre l'ID dall'URL (se possibile)
                    // Altrimenti imposta un valore di default
                    fileIdField.value = 'auto'; // Verrà gestito lato server
                }

            }, 100); // Piccolo delay per assicurarsi che la modale sia completamente caricata
        });
    }

});