import 'arrive';
import Parallax from 'parallax-js';

document.arrive('#square-element-title-link', {existing: true}, function(){

    var scene = document.getElementById('square-element-title-link');
    var parallaxInstance = new Parallax(scene);

});

// Gestione modale brochure - JavaScript vanilla
document.addEventListener('DOMContentLoaded', function() {

    // Gestione click sui bottoni brochure
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('brochure-download-btn')) {
            e.preventDefault();

            // Recupera i dati del file dal bottone
            const button = e.target;
            const fileUrl = button.getAttribute('data-file-url');
            const fileName = button.getAttribute('data-file-name');
            const fileTitle = button.getAttribute('data-file-title');

            // Aggiorna il contenuto della modale
            const modalFileName = document.getElementById('modal-file-name');
            if (modalFileName) {
                modalFileName.textContent = fileName || fileTitle || 'Brochure';
            }

            // Memorizza l'URL del file per l'invio email
            window.currentBrochureFile = {
                url: fileUrl,
                name: fileName,
                title: fileTitle
            };

            // Popola il campo nascosto nel form con l'URL del file
            const hiddenField = document.getElementById('brochure_file_url');
            if (hiddenField) {
                hiddenField.value = fileUrl;
            }

            // Invia i dati del file al server tramite AJAX (solo se ajax_object è disponibile)
            if (typeof ajax_object !== 'undefined') {
                const formData = new FormData();
                formData.append('action', 'save_brochure_file');
                formData.append('file_url', fileUrl);
                formData.append('nonce', ajax_object.nonce);

                fetch(ajax_object.ajax_url, {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    console.log('File URL salvato:', data);
                })
                .catch(error => {
                    console.error('Errore nel salvare file URL:', error);
                });
            }

            // Apri la modale usando Bootstrap
            const modal = document.getElementById('brochureModal');
            if (modal && typeof window.bootstrap !== 'undefined') {
                const bsModal = new bootstrap.Modal(modal);
                bsModal.show();
            } else if (modal && typeof $ !== 'undefined') {
                // Fallback per jQuery/Bootstrap 4
                $(modal).modal('show');
            }
        }
    });

    // Gestione invio form Contact Form 7 nella modale
    document.addEventListener('wpcf7mailsent', function(event) {
        // Verifica se l'evento proviene dal form nella modale
        const modal = document.getElementById('brochureModal');
        if (modal && modal.contains(event.target)) {
            // Chiudi la modale dopo l'invio
            setTimeout(function() {
                if (typeof window.bootstrap !== 'undefined') {
                    const bsModal = bootstrap.Modal.getInstance(modal);
                    if (bsModal) bsModal.hide();
                } else if (typeof $ !== 'undefined') {
                    $(modal).modal('hide');
                }
            }, 2000);
        }
    });

});