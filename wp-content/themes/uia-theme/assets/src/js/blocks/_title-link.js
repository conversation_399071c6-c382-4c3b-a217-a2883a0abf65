import 'arrive';
import Parallax from 'parallax-js';

document.arrive('#square-element-title-link', {existing: true}, function(){

    var scene = document.getElementById('square-element-title-link');
    var parallaxInstance = new Parallax(scene);

});

// Gestione modale brochure
document.arrive('.brochure-download-btn', {existing: true}, function(){

    // Gestione click sui bottoni brochure
    $('.brochure-download-btn').on('click', function(e) {
        e.preventDefault();

        // Recupera i dati del file dal bottone
        const fileUrl = $(this).data('file-url');
        const fileName = $(this).data('file-name');
        const fileTitle = $(this).data('file-title');

        // Aggiorna il contenuto della modale
        $('#modal-file-name').text(fileName || fileTitle || 'Brochure');

        // Memorizza l'URL del file per l'invio email
        window.currentBrochureFile = {
            url: fileUrl,
            name: fileName,
            title: fileTitle
        };

        // Popola il campo nascosto nel form con l'URL del file
        $('#brochure_file_url').val(fileUrl);

        // Invia i dati del file al server tramite AJAX
        $.ajax({
            url: ajax_object.ajax_url,
            type: 'POST',
            data: {
                action: 'save_brochure_file',
                file_url: fileUrl,
                nonce: ajax_object.nonce
            },
            success: function(response) {
                console.log('File URL salvato:', response);
            },
            error: function(xhr, status, error) {
                console.error('Errore nel salvare file URL:', error);
            }
        });

        // Apri la modale
        $('#brochureModal').modal('show');
    });

});

// Gestione invio form Contact Form 7 nella modale
document.addEventListener('wpcf7mailsent', function(event) {
    // Verifica se l'evento proviene dal form nella modale
    if ($(event.target).closest('#brochureModal').length > 0) {
        // Chiudi la modale dopo l'invio
        setTimeout(function() {
            $('#brochureModal').modal('hide');
        }, 2000);
    }
}, false);