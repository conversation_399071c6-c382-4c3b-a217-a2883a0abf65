.block-page-links {
  position: relative;

  .page {
    color: $dark;
    height: 100%;

    .thumbnail-container {
      position: relative;
      height: 100%;
      width: 100%;
      aspect-ratio: 1 / 1.1;
    
      img {
        position: absolute;
        height: 100%;
        width: 100%;
        object-fit: cover;
        object-position: center;
      }

      @media all and (max-width: 820px) {
        min-width: rem(250px);
        min-height: rem(250px);
      }
    }

    .title {
      color: $magenta;
      text-transform: uppercase;
      font-size: rem(40px);
      line-height: em(40px, 50px);
      margin: rem(20px 0 10px);
    }

    .links-list {
      padding: 0;

      li {
        margin-bottom: rem(10px);

        .permalink {
          text-decoration: none;
          display: block;
        
          .btn-magenta {
            padding: rem(5px 10px);
            color: $light;
            font-size: rem(18px);
            font-weight: bold;
            text-transform: uppercase;
            text-align: left;
            background-color: $magenta;
          }
        }
      }
    }

  }

  @include media-breakpoint-down(sm) {
    padding-bottom: rem(70px);
  }
}