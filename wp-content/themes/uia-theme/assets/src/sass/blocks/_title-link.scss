.block-title-link {
  position: relative;

  .title {
    font-size: rem(58px);
    line-height: em(58px, 50px);

    &.magenta {
      color: $magenta;
    }

    &.ciano {
      color: $ciano;
    }

    &.nero {
      color: $dark;
    }

    &.giallo {
      color: $accent;
    }

    &.marea {
      color: $marea;
    }
  }

  .btn-black {
    width: 100%;
    text-align: left;
    color: $light;
    padding: rem(10px 20px);
    background-color: $dark;
    border: none;
    border-radius: 0;
    text-transform: uppercase;
    position: relative;
    z-index: 9;
    border-radius: 50px;

    &:hover {
      color: $light;
    }
  }

  .btn-yellow {
    width: 100%;
    text-align: left;
    color: $dark;
    padding: rem(10px 20px);
    background-color: $accent;
    border: none;
    border-radius: 0;
    text-transform: uppercase;
    position: relative;
    z-index: 9;
    border-radius: 50px;
    margin-top: rem(20px);

    &:hover {
      color: $light;
    }
  }

  .square-element-title-link {
    width: 60vw;
    position: absolute;
    top: -23vw;
    right: -15vw;
    z-index: 2;
    mix-blend-mode: multiply;
    pointer-events: none;
  }
}

// Stili per la modale brochure
#brochureModal {
  .modal-header {
    background-color: $accent;
    border-bottom: 3px solid $magenta;

    .modal-title {
      font-size: rem(24px);
      font-weight: bold;
      color: $dark;
    }

    .close {
      color: $dark;
      opacity: 1;

      &:hover {
        color: $magenta;
      }
    }
  }

  .modal-body {
    padding: rem(30px);

    .brochure-info {
      background-color: lighten($magenta, 45%);
      padding: rem(15px);
      border-radius: rem(5px);
      border-left: 4px solid $magenta;

      p {
        margin: 0;
      }
    }

    // Stili per il form CF7 nella modale
    .wpcf7-form {

      label, p {
        margin: 0;
        width: 100%;
      }

      input[type="text"],
      input[type="email"],
      input[type="tel"],
      select,
      textarea {
        width: 100%;
        padding: rem(10px);
        border: 2px solid lighten($dark, 60%);
        border-radius: rem(5px);
        font-size: rem(16px);
        margin-bottom: rem(20px);

        &:focus {
          outline: none;
          border-color: $magenta;
          box-shadow: 0 0 0 2px rgba($magenta, 0.2);
        }
      }

      textarea {
        max-height: rem(100px);
      }

      .wpcf7-submit {
        background-color: $magenta;
        color: $light;
        border: none;
        padding: rem(12px 30px);
        border-radius: rem(25px);
        font-weight: bold;
        text-transform: uppercase;
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover {
          background-color: darken($magenta, 10%);
          transform: translateY(-2px);
        }
      }

      .wpcf7-acceptance {
        .wpcf7-list-item {
          margin-left: 0;
          margin-bottom: rem(20px);
        }
      }

      // Stili per i messaggi di risposta
      .wpcf7-response-output {
        border: none;
        padding: rem(15px);
        border-radius: rem(5px);
        margin-top: rem(15px);

        &.wpcf7-mail-sent-ok {
          background-color: lighten($accent, 40%);
          color: darken($accent, 20%);
          border-left: 4px solid $accent;
        }

        &.wpcf7-validation-errors {
          background-color: lighten(red, 40%);
          color: darken(red, 20%);
          border-left: 4px solid red;
        }
      }
    }
  }
}