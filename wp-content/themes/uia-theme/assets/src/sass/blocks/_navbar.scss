.block-navbar {
  background-color: $light;
  position: relative;

  .navbar {
    padding: rem(15px 50px);
    transition: all ease-in-out 0.3s;
    align-items: flex-start;
    position: relative;

    @include media-breakpoint-down(lg) {
      padding: rem(15px 0);
    }

    .collapse {
      justify-content: center;
    }
 
    .navbar-brand {
      position: relative;
      z-index: 11;
      .navbar-logo {
        width: rem(300px);
        transition: width ease-in-out 0.3s;

        @media all and (max-width: 1440px) {
          width: rem(250px);
        }
      }
    }

    #hamburger {
      display: none!important;
      background-color: transparent;
      border: none;
      cursor: pointer;
      display: flex;
      padding: 0;
      margin-top: rem(10px);

      &:focus {
        outline: none;
        box-shadow: none;
      }
    
      .line {
        fill: none;
        stroke: $stroke-color;
        stroke-width: 3;
        transition: stroke-dasharray 600ms cubic-bezier(0.4, 0, 0.2, 1),
          stroke-dashoffset 600ms cubic-bezier(0.4, 0, 0.2, 1);
    
        &1 {
          stroke-dasharray: 60 207;
          stroke-width: 3;
        }
    
        &2 {
          stroke-dasharray: 60 60;
          stroke-width: 3;
        }
    
        &3 {
          stroke-dasharray: 60 207;
          stroke-width: 3;
        }
      }
    
      &.open .line {
        &1 {
          stroke-dasharray: 90 207;
          stroke-dashoffset: -134;
          stroke-width: 3;
        }
    
        &2 {
          stroke-dasharray: 1 60;
          stroke-dashoffset: -30;
          stroke-width: 3;
        }
    
        &3 {
          stroke-dasharray: 90 207;
          stroke-dashoffset: -134;
          stroke-width: 3;
        }
      }

      @media all and (max-width: 820px) {
        display: block!important;

      }

      @include media-breakpoint-down(sm) {
        margin-top: 0;
      }
    }

    .menu-main-menu-container {
      .nav {
        .menu-item {

          .nav-link {
            font-size: rem(15px);
            line-height: em(15px, 18px);
            font-weight: 400;
            color: $dark;
            text-transform: uppercase;

            @media all and (max-width: 1440px) {
              padding-left: rem(10px);
              padding-right: rem(10px);
            }

            @media all and (max-width: 450px) {
              font-size: rem(20px);
              line-height: em(20px, 30px);
            }
          }

          &.current-menu-item {

            &.magenta {
              .nav-link {
                color: $magenta;
              }
            }
            &.ciano {
              .nav-link {
                color: $ciano;
              }
            }

            &.marea {
              .nav-link {
                color: $marea;
              }
            }
          }

          &.current-menu-parent {
            .nav-link {
              color: $ciano;
            }
          }

          .dropdown-menu {
            border: none;
            border-top: rem(4px) solid $accent;
            border-radius: 0;
            background-color: #f5e617B3;

            .dropdown-item {
              font-size: rem(15px);
              line-height: em(15px, 18px);
              font-weight: 400;
              color: $dark;
              text-transform: uppercase;

              &:hover {
                background: transparent;
              }
            }
          }
        }
      }
    }

    .social-list {
      padding: 0;
      margin: 0;
    
      .social {
        display: inline-block;
        margin-right: rem(5px);
    
        a {
          display: inline-block;
        }

        &.cart-icon {
          position: relative;
          top: rem(5px);
          
          .wpmenucart-contents {
            color: $dark;
            text-decoration: none;
            line-height: 100%;
          }
        }
      }

      .uia-search-icon {
        margin-top: rem(10px);
        .is-search-form {
          label {
            float: right;
          }
        }
        .is-search-submit {
          background-color: transparent!important;

          .is-search-icon {
            background-color: transparent!important;
            border: none;
            font-size: rem(20px)!important;

            svg {
              width: 25px;
              path {

                fill: $dark;
              }
            }
          }
        }
        .is-search-input {
          border: none!important;
          font-size: rem(16px)!important;
          text-transform: uppercase!important;
        }
      }
    }
  }

  .square-element {
    position: absolute;
    top: -31vw;
    left: -18vw;
    z-index: 1;
    mix-blend-mode: multiply;
    width: 50vw;

    @media all and (max-width: 820px) {
      width: 60vw;
    }
    @include media-breakpoint-down(sm) {
      width: 100vw;
      top: -60vw;
      left: -20vw;
    }
  }

  @media all and (max-width: 820px) {
    .navbar-social-container {
      display: block;
      position: fixed;
      left: 50%;
      transform: translateX(-25%);
    }

    .wrapper-nav {
      padding-top: rem(20px);
      margin-top: rem(20px);
      border-top: rem(5px) solid $magenta;
    }
  }

  @include media-breakpoint-down(sm) {
    .navbar-social-container {
      display: none;
    }
  }
}
