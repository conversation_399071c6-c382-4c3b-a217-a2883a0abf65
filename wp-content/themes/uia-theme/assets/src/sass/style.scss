//
//  Style - Include
//

// Variables and Fonts
@import "variables";
@import "fonts";

// Lib
@import "reset";
@import "~bootstrap";
@import "~slick-carousel/slick/slick";
@import "~slick-carousel/slick/slick-theme";

// Function, mixin

@import "functions";
@import "mixin";
@import "media-query";

//  Common style

@import "DOM";

//  Products
@import "product/single-product";

//  Components
@import "components/lazyload";
@import "components/spacing";
@import "components/form-landing";

//  Blocks
@import "blocks/navbar";
@import "blocks/header-slider";
@import "blocks/text";
@import "blocks/latest-news";
@import "blocks/call-news";
@import "blocks/products-online";
@import "blocks/products-presence";
@import "blocks/products-marea";
@import "blocks/banner-image";
@import "blocks/banner-image-container";
@import "blocks/columns-images";
@import "blocks/newsletter-form";
@import "blocks/newsletter-form-courses";
@import "blocks/subscription-form";
@import "blocks/courses-form";
@import "blocks/title-link";
@import "blocks/page-links";
@import "blocks/title-text-info";
@import "blocks/activities-teachers";
@import "blocks/teacher-info";
@import "blocks/accordion";
@import "blocks/faq";
@import "blocks/contacts";
@import "blocks/map";
@import "blocks/after-shop";
@import "blocks/footer";

// Post Types
@import "post-type/single";

//  Pages
@import "pages/landing";
@import "pages/cart";
@import "pages/checkout";
@import "pages/news";

