//
//  Functional
//

@mixin desktop {
  @media (min-width: #{$width-md + 1 + px}) {
    @content;
  }
}

@mixin tablet($orientation: l){

  @if($orientation == l){

    @media (min-width: #{$width-md + px}) and (max-width: #{$width-xl + px}) and (orientation: landscape) {
      @content;
    }

  }@else if($orientation == p){

    @media (min-width: #{$width-md + px}) and (max-width: #{$width-xl + px}) and (orientation: portrait) {
      @content;
    }

  }@else {

    @media (min-width: #{$width-md + px}) and (max-width: #{$width-xl + px}) {
      @content;
    }

  }
}

@mixin phone {
  @media (max-width: #{$width-md - 1 + px}) {
    @content;
  }
  @media (min-width: #{$width-md + px}) and (max-width: #{$width-md + px}) and (orientation: portrait) {
    @content;
  }
}

@mixin custom($min-width,$max-width){

  @if($min-width == "phone"){
    $min-width: $width-md + px;
  } @else if($min-width == "tablet"){
    $min-width: $width-md + px;
  } @else {
    $min-width: $min-width+px;
  }

  @if($max-width == "phone"){
    $max-width: $width-md + px;
  } @else if($max-width == "tablet"){
    $max-width: $width-md + px;
  } @else {
    $max-width: $max-width+px;
  }

  @media (min-width: #{$min-width + px}) and (max-width: #{$max-width + px}) {
    @content;
  }

}

//
//  Mixin Associativo
//

@mixin media($list...) {

  @each $resolution in $list {

    @if($resolution == phone){
      @include phone {
        @content;
      }
    }

    @if($resolution == phone-landscape){
      @media (max-width: $width-md) and (orientation: landscape) {
        @content;
      }
    }

    @if($resolution == tablet-portrait){
      @include tablet(p){
        @content
      }
    }

    @if($resolution == tablet-landscape){
      @include tablet(l){
        @content
      }
    }

    @if($resolution == tablet){
      @include tablet(p){
        @content
      }
      @include tablet(l){
        @content
      }
    }

    @if($resolution == desktop){
      @include desktop{
        @content
      }
    }

  }

}

//
//  Browser support
//
@mixin edge{
  @supports (-ms-ime-align:auto) {
    @content;
  }
}

@mixin firefox{
  @-moz-document url-prefix() {
    @content;
  }
}

@mixin ie{
  @media \0screen\,screen\9 {
    @content;
  }
  @media screen and (min-width:0\0) and (min-resolution: .001dpcm){
    @content;
  }
  @media screen and (min-width:0\0) {
    @content;
  }
  @media all and (-ms-high-contrast: none), (-ms-high-contrast: active) {
    @content;
  }
}

@mixin safari {
  @media not all and (min-resolution: .001dpcm) {
    @media {
      @content
    }
  }
  @media screen and (min-color-index: 0) and(-webkit-min-device-pixel-ratio: 0) {
    @media{
      @content;
    }
  }
}

@mixin chrome{
  @media screen and (-webkit-min-device-pixel-ratio:0) and (min-resolution:.001dpcm) {
    @content;
  }
}