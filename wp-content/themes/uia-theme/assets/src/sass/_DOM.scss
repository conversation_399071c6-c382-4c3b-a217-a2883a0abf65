//
//  DOM
//

html,body{
  font-family: $font-family-base, sans-serif;
  font-size: $size-font-base;
  overflow-x: hidden;

  @include media-breakpoint-down(sm) {
    font-size: $size-font-base-mobile;
  }
}

body {
  font-size: $size-font-base;
}

body.page-template-page-landing,
body.page-template-page-thankyou {
  header {
    display: none;
  }
  footer {
    display: none;
  }
}

.woocommerce-notices-wrapper {
  margin-bottom: 0;
  .woocommerce-message {
    margin-bottom: 0;
    border-top-color: $accent;

    &:before {
      color: $ciano;
    }

    .button {
      min-height: rem(40px);
      color: $light;
      background-color: $ciano;
      border-radius: 0 !important;
      padding: rem(5px 30px);
      font-size: rem(25px);
      line-height: rem(45px);
      text-transform: uppercase;
    }
  }
}

a,
a:active,
a:hover,
a:focus,
button,
button:active,
button:hover,
button:focus {
  outline: 0 !important;
}

// Text Styles
.text {
  color: $dark;
  font-weight: 300;

  strong,
  b {
    font-weight: 700;
  }

  i,
  em {
    font-style: italic;
  }

  a {
    color: $dark;
    text-decoration: none;

    &:hover {
      color: $magenta;
    }
  }
}

@media all and (max-width: 820px) {

  body.page-marea .block-newsletter-form-courses{
    background-color: $marea;
  }
  
}