# Funzionalità Brochure con Modale Bootstrap e Contact Form 7

## Descrizione
Questa funzionalità trasforma il blocco "Title + Link" in un sistema per richiedere brochure tramite modale Bootstrap 4 con form Contact Form 7. Il file brochure viene automaticamente allegato alla email di risposta.

## Componenti Implementati

### 1. Template Modificato
- **File**: `wp-content/themes/uia-theme/template/blocks/title-link.php`
- **Modifiche**: 
  - Sostituito link con button che apre modale
  - Aggiunta struttura modale Bootstrap 4
  - Integrazione con campo ACF File (`link_brochure`)

### 2. JavaScript
- **File**: `wp-content/themes/uia-theme/assets/src/js/blocks/_title-link.js`
- **Funzionalità**:
  - Gestione apertura modale
  - Passaggio dati file alla modale
  - Invio dati file tramite AJAX
  - Gestione eventi Contact Form 7

### 3. Backend PHP
- **File**: `wp-content/themes/uia-theme/includes/brochure-attachment.php`
- **Funzionalità**:
  - Gestione allegati email CF7
  - Hook AJAX per salvare dati file
  - Sicurezza con nonce
  - Validazione URL file

### 4. Stili CSS
- **File**: `wp-content/themes/uia-theme/assets/src/sass/blocks/_title-link.scss`
- **Stili per**:
  - Modale Bootstrap personalizzata
  - Form Contact Form 7 integrato
  - Messaggi di risposta

## Configurazione Necessaria

### 1. Creare Form Contact Form 7
1. Vai in **Contatti > Contact Forms** nel admin WordPress
2. Crea un nuovo form con questi campi:
```
<label> Nome *
    [text* your-name] </label>

<label> Email *
    [email* your-email] </label>

<label> Messaggio
    [textarea your-message] </label>

[submit "Invia richiesta"]
```

3. Configura l'email con questo template:
```
Oggetto: Richiesta Brochure - [your-name]

Messaggio:
Nome: [your-name]
Email: [your-email]
Messaggio: [your-message]

File richiesto: [brochure_file_url]
```

4. Annota l'ID del form creato

### 2. Aggiornare ID Form
- **File**: `wp-content/themes/uia-theme/template/blocks/title-link.php`
- **Linea 75**: Sostituire `999` con l'ID del form CF7 creato
- **File**: `wp-content/themes/uia-theme/includes/brochure-attachment.php`
- **Linee 25 e 120**: Aggiungere l'ID del form nell'array `$brochure_form_ids`

### 3. Configurare Campo ACF
Il campo `link_brochure` è già configurato nel blocco title-link. Assicurati che:
- Tipo: File
- Return Format: Array
- Sia presente nel gruppo "Block - Title + Link"

## Come Utilizzare

### 1. Nel Backend WordPress
1. Modifica una pagina che usa il template "Page - Block Template"
2. Aggiungi un blocco "Titolo grande + Link"
3. Compila il titolo
4. **Importante**: Invece di usare il campo "Link", usa il campo "File Brochure"
5. Carica il file PDF/documento che vuoi allegare
6. Salva la pagina

### 2. Nel Frontend
1. L'utente vede il bottone "Scarica Brochure"
2. Cliccando si apre la modale con il form
3. L'utente compila i dati e invia
4. Riceve email con il file allegato

## File Coinvolti

```
wp-content/themes/uia-theme/
├── template/blocks/title-link.php          # Template principale
├── assets/src/js/blocks/_title-link.js     # JavaScript
├── assets/src/sass/blocks/_title-link.scss # Stili CSS
├── includes/brochure-attachment.php        # Backend PHP
├── functions.php                           # Include brochure-attachment.php
└── acf-json/group_62627547620d2.json      # Configurazione ACF
```

## Sicurezza
- Validazione URL file
- Verifica che i file siano nel dominio del sito
- Nonce per richieste AJAX
- Sanitizzazione input utente

## Debug
- Log errori in `wp-content/debug.log`
- Console browser per errori JavaScript
- Verifica che Bootstrap 4 sia caricato
- Controlla che jQuery sia disponibile

## Estensioni Future
- Supporto per più tipi di file
- Personalizzazione email template
- Statistiche download
- Integrazione con CRM

## Note Tecniche
- Compatibile con Bootstrap 4.3.1
- Richiede ACF Pro
- Richiede Contact Form 7
- Usa sessioni PHP per passare dati
