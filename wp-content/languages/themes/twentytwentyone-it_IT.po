# Translation of Themes - Twenty Twenty-One in Italian
# This file is distributed under the same license as the Themes - Twenty Twenty-One package.
msgid ""
msgstr ""
"PO-Revision-Date: 2022-01-28 18:57:36+0000\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: GlotPress/3.0.0-rc.2\n"
"Language: it\n"
"Project-Id-Version: Themes - Twenty Twenty-One\n"

#. Description of the theme
#, gp-priority: high
msgid "Twenty Twenty-One is a blank canvas for your ideas and it makes the block editor your best brush. With new block patterns, which allow you to create a beautiful layout in a matter of seconds, this theme’s soft colors and eye-catching — yet timeless — design will let your work shine. Take it for a spin! See how Twenty Twenty-One elevates your portfolio, business website, or personal blog."
msgstr "Twenty Twenty-One è una tela bianca per le tue idee, pronta a rendere l'editor a blocchi il tuo miglior pennello. Con i nuovi pattern di blocchi, che permettono di creare un bellissimo layout in pochi secondi, i colori tenui di questo tema e il design accattivante e senza tempo daranno risalto al tuo lavoro. Facci un giro! Noterai come Twenty Twenty-One fa splendere il tuo portfolio, il tuo sito web aziendale o il tuo blog personale."

#. Theme Name of the theme
#: inc/block-patterns.php:20
#, gp-priority: high
msgid "Twenty Twenty-One"
msgstr "Twenty Twenty-One"

#: inc/template-tags.php:245
#, gp-priority: normal
msgid "Older <span class=\"nav-short\">posts</span>"
msgstr "<span class=\"nav-short\">Articoli</span> meno recenti"

#: inc/template-tags.php:234
#, gp-priority: normal
msgid "Newer <span class=\"nav-short\">posts</span>"
msgstr "<span class=\"nav-short\">Articoli</span> più recenti"

#: classes/class-twenty-twenty-one-customize-notice-control.php:40
#: classes/class-twenty-twenty-one-dark-mode.php:178
#, gp-priority: normal
msgid "https://wordpress.org/support/article/twenty-twenty-one/#dark-mode-support"
msgstr "https://wordpress.org/support/article/twenty-twenty-one/#dark-mode-support"

#. translators: %s: Twenty Twenty-One support article URL.
#: classes/class-twenty-twenty-one-dark-mode.php:177
#, gp-priority: normal
msgid "Dark Mode is a device setting. If a visitor to your site requests it, your site will be shown with a dark background and light text. <a href=\"%s\">Learn more about Dark Mode.</a>"
msgstr "La modalità scura è un'impostazione del dispositivo. Se un visitatore del tuo sito lo richiede, il tuo sito verrà mostrato con uno sfondo scuro e il testo chiaro. <a href=\"%s\">Scopri di più sulla modalità scura.</a>"

#: classes/class-twenty-twenty-one-dark-mode.php:181
#, gp-priority: normal
msgid "Dark Mode can also be turned on and off with a button that you can find in the bottom corner of the page."
msgstr "La modalità scura può essere attivata o disattivata con un pulsante che puoi trovare nell'angolo in basso nella pagina."

#: classes/class-twenty-twenty-one-dark-mode.php:380
#, gp-priority: normal
msgid "This website uses LocalStorage to save the setting when Dark Mode support is turned on or off.<br> LocalStorage is necessary for the setting to work and is only used when a user clicks on the Dark Mode button.<br> No data is saved in the database or transferred."
msgstr "Questo sito usa LocalStorage per salvare l'impostazione di attivazione o disattivazione della modalità scura.<br> LocalStorage è necessario per il funzionamento dell'impostazione ed è usato solo quando un utente fa clic sul pulsante Modalità scura.<br> Nessun dato viene salvato nel database o trasferito."

#: classes/class-twenty-twenty-one-dark-mode.php:379
#, gp-priority: normal
msgid "Suggested text:"
msgstr "Testo suggerito:"

#: classes/class-twenty-twenty-one-dark-mode.php:378
#, gp-priority: normal
msgid "Twenty Twenty-One uses LocalStorage when Dark Mode support is enabled."
msgstr "Twenty Twenty-One utilizza LocalStorage quando il supporto alla modalità scura è abilitato."

#: classes/class-twenty-twenty-one-dark-mode.php:188
#, gp-priority: normal
msgid "Dark Mode support"
msgstr "Supporto alla modalità scura"

#: classes/class-twenty-twenty-one-customize-notice-control.php:41
#, gp-priority: normal
msgid "Learn more about Dark Mode."
msgstr "Approfondimenti sulla modalità scura."

#: classes/class-twenty-twenty-one-customize-notice-control.php:39
#, gp-priority: normal
msgid "To access the Dark Mode settings, select a light background color."
msgstr "Per accedere alle impostazioni della modalità scura, seleziona un colore di sfondo chiaro."

#: classes/class-twenty-twenty-one-dark-mode.php:134
#, gp-priority: normal
msgid "Colors & Dark Mode"
msgstr "Colori e modalità scura"

#: inc/template-tags.php:77
#, gp-priority: normal
msgctxt "Label for sticky posts"
msgid "Featured post"
msgstr "Articolo in evidenza"

#: inc/template-functions.php:424
#, gp-priority: normal
msgctxt "Post password form"
msgid "Submit"
msgstr "Invia"

#: inc/template-functions.php:424
#, gp-priority: normal
msgctxt "Post password form"
msgid "Password"
msgstr "Password"

#: inc/template-functions.php:183
#, gp-priority: normal
msgctxt "Added to posts and pages that are missing titles"
msgid "Untitled"
msgstr "Senza titolo"

#: inc/block-patterns.php:117
#, gp-priority: normal
msgctxt "Block pattern sample content"
msgid "Cambridge, MA, 02139"
msgstr "Cambridge, MA, 02139"

#: inc/block-patterns.php:117
#, gp-priority: normal
msgctxt "Block pattern sample content"
msgid "123 Main Street"
msgstr "123 Main Street"

#: inc/block-patterns.php:117
#, gp-priority: normal
msgctxt "Block pattern sample content"
msgid "123-456-7890"
msgstr "123-456-7890"

#: inc/block-patterns.php:117
#, gp-priority: normal
msgctxt "Block pattern sample content"
msgid "<EMAIL>"
msgstr "<EMAIL>"

#: classes/class-twenty-twenty-one-customize.php:138
#, gp-priority: normal
msgctxt "Customizer control"
msgid "Background color"
msgstr "Colore di sfondo"

#. translators: %s: WordPress Version.
#: inc/back-compat.php:42 inc/back-compat.php:61 inc/back-compat.php:86
#, gp-priority: normal
msgid "This theme requires WordPress 5.3 or newer. You are running version %s. Please upgrade."
msgstr "Questo tema richiede WordPress 5.3 o versione successiva. Stai utilizzando la versione %s. Aggiorna."

#. translators: %: Page number.
#: image.php:48 template-parts/content/content-page.php:36
#: template-parts/content/content-single.php:30
#: template-parts/content/content.php:36
#, gp-priority: normal
msgid "Page %"
msgstr "Pagina %"

#: functions.php:187
#, gp-priority: normal
msgctxt "Font size"
msgid "XXXL"
msgstr "XXXL"

#: functions.php:181
#, gp-priority: normal
msgctxt "Font size"
msgid "XXL"
msgstr "XXL"

#: functions.php:175
#, gp-priority: normal
msgctxt "Font size"
msgid "XL"
msgstr "XL"

#: functions.php:169
#, gp-priority: normal
msgctxt "Font size"
msgid "L"
msgstr "L"

#: functions.php:163
#, gp-priority: normal
msgctxt "Font size"
msgid "M"
msgstr "M"

#: functions.php:157
#, gp-priority: normal
msgctxt "Font size"
msgid "S"
msgstr "S"

#: functions.php:151
#, gp-priority: normal
msgctxt "Font size"
msgid "XS"
msgstr "XS"

#: comments.php:85
#, gp-priority: normal
msgid "Leave a comment"
msgstr "Lascia un commento"

#. translators: %s: Comment count number.
#: comments.php:39
#, gp-priority: normal
msgctxt "Comments title"
msgid "%s comment"
msgid_plural "%s comments"
msgstr[0] "%s commento"
msgstr[1] "%s commenti"

#: comments.php:34
#, gp-priority: normal
msgid "1 comment"
msgstr "1 commento"

#: classes/class-twenty-twenty-one-dark-mode.php:334
#, gp-priority: normal
msgid "On"
msgstr "On"

#: classes/class-twenty-twenty-one-dark-mode.php:331
#, gp-priority: normal
msgid "Off"
msgstr "Off"

#. translators: %s: On/Off
#: classes/class-twenty-twenty-one-dark-mode.php:321
#, gp-priority: normal
msgid "Dark Mode: %s"
msgstr "Modalità scura: %s"

#: inc/template-functions.php:422
#, gp-priority: normal
msgid "This content is password protected. Please enter a password to view."
msgstr "Questo contenuto è protetto da password. Inserisci una password per visualizzarlo."

#: inc/menu-functions.php:34
#, gp-priority: normal
msgid "Open menu"
msgstr "Apri menu"

#: inc/block-patterns.php:107
#, gp-priority: normal
msgid "&#8220;Reading&#8221; by Berthe Morisot"
msgstr "&#8220;La lettura&#8221; di Berthe Morisot"

#: inc/block-patterns.php:84
#, gp-priority: normal
msgid "&#8220;Self portrait&#8221; by Berthe Morisot"
msgstr "&#8220;Autoritratto&#8221; di Berthe Morisot"

#: inc/block-patterns.php:84
#, gp-priority: normal
msgid "&#8220;Daffodils&#8221; by Berthe Morisot"
msgstr "&#8220;Narcisi&#8221; di Berthe Morisot"

#: inc/block-patterns.php:72 inc/block-patterns.php:107
#: inc/starter-content.php:43
#, gp-priority: normal
msgid "&#8220;Roses Trémières&#8221; by Berthe Morisot"
msgstr "&#8220;Malvarose&#8221; di Berthe Morisot"

#: inc/template-functions.php:424
#, gp-priority: normal
msgctxt "Post password form"
msgid "Enter"
msgstr "Inserisci"

#: inc/starter-content.php:128
#, gp-priority: normal
msgctxt "Theme starter content"
msgid "Check out the Support Forums"
msgstr "Visita i forum di supporto"

#: inc/starter-content.php:122
#, gp-priority: normal
msgctxt "Theme starter content"
msgid "Read the Theme Documentation"
msgstr "Leggi la documentazione del tema"

#: inc/starter-content.php:112
#, gp-priority: normal
msgctxt "Theme starter content"
msgid "Need help?"
msgstr "Serve aiuto?"

#: inc/starter-content.php:97
#, gp-priority: normal
msgctxt "Theme starter content"
msgid "Twenty Twenty-One also includes an overlap style for column blocks. With a Columns block selected, open the \"Styles\" panel within the Editor sidebar. Choose the \"Overlap\" block style to try it out."
msgstr "Twenty Twenty-One include anche uno stile di sovrapposizione per i blocchi di colonne. Quando selezioni un blocco Colonne, apri il pannello \"Stili\" all'interno della barra laterale dell'editor. Scegli lo stile del blocco \"Sovrapposizione\" per provarlo."

#: inc/starter-content.php:93
#, gp-priority: normal
msgctxt "Theme starter content"
msgid "Overlap columns"
msgstr "Sovrapponi colonne"

#: inc/starter-content.php:87
#, gp-priority: normal
msgctxt "Theme starter content"
msgid "Twenty Twenty-One includes stylish borders for your content. With an Image block selected, open the \"Styles\" panel within the Editor sidebar. Select the \"Frame\" block style to activate it."
msgstr "Twenty Twenty-One  bordi eleganti per i tuoi contenuti. Quando hau selezionato un blocco Immagine, apri il pannello \"Stili\" all'interno della barra laterale dell'editor. Seleziona lo stile del blocco \"Cornice\" per attivarlo."

#: inc/starter-content.php:83
#, gp-priority: normal
msgctxt "Theme starter content"
msgid "Frame your images"
msgstr "Incornicia le tue immagini"

#: inc/starter-content.php:77
#, gp-priority: normal
msgctxt "Theme starter content"
msgid "Block patterns are pre-designed groups of blocks. To add one, select the Add Block button [+] in the toolbar at the top of the editor. Switch to the Patterns tab underneath the search bar, and choose a pattern."
msgstr "I pattern dei blocchi sono gruppi di blocchi predefiniti. Per aggiungerne uno, seleziona il pulsante Aggiungi blocco [+] negli strumenti al di sopra dell'editor. Passa alla scheda Pattern sotto la barra di ricerca e scegli un pattern."

#: inc/starter-content.php:73
#, gp-priority: normal
msgctxt "Theme starter content"
msgid "Add block patterns"
msgstr "Aggiungi pattern di blocchi"

#: inc/starter-content.php:30 inc/starter-content.php:33
#, gp-priority: normal
msgctxt "Theme starter content"
msgid "Create your website with blocks"
msgstr "Crea il tuo sito web con i blocchi"

#: inc/block-patterns.php:107
#, gp-priority: normal
msgid "Reading"
msgstr "La lettura"

#: inc/block-patterns.php:107
#, gp-priority: normal
msgid "Young Woman in Mauve"
msgstr "Giovane donna in malva"

#: inc/block-patterns.php:107
#, gp-priority: normal
msgid "The Garden at Bougival"
msgstr "Il giardino di Bougival"

#: inc/block-patterns.php:107
#, gp-priority: normal
msgid "In the Bois de Boulogne"
msgstr "Al Bois de Boulogne"

#: inc/block-patterns.php:107
#, gp-priority: normal
msgid "Villa with Orange Trees, Nice"
msgstr "Villa con alberi di arancio, Nizza"

#: inc/block-patterns.php:107
#, gp-priority: normal
msgid "Roses Trémières"
msgstr "Malvarose"

#: inc/block-patterns.php:96
#, gp-priority: normal
msgid "&#8220;Villa with Orange Trees, Nice&#8221; by Berthe Morisot"
msgstr "&quot;Villa con alberi di arancio, Nizza&quot; di Berthe Morisot"

#: inc/block-patterns.php:96
#, gp-priority: normal
msgid "Beautiful gardens painted by Berthe Morisot in the late 1800s"
msgstr "Bellissimi giardini dipinti da Berthe Morisot alla fine del 1800"

#: inc/block-patterns.php:96 inc/block-patterns.php:107
#, gp-priority: normal
msgid "&#8220;The Garden at Bougival&#8221; by Berthe Morisot"
msgstr "&#8220;Il giardino di Bougival&#8221; di Berthe Morisot"

#: inc/block-patterns.php:72 inc/block-patterns.php:107
#: inc/starter-content.php:61
#, gp-priority: normal
msgid "&#8220;Young Woman in Mauve&#8221; by Berthe Morisot"
msgstr "&quot;Giovane donna in malva&quot; di Berthe Morisot"

#: inc/block-patterns.php:72 inc/block-patterns.php:107
#: inc/starter-content.php:51
#, gp-priority: normal
msgid "&#8220;In the Bois de Boulogne&#8221; by Berthe Morisot"
msgstr "&quot;Al Bois de Boulogne&quot; di Berthe Morisot"

#: inc/block-patterns.php:60
#, gp-priority: normal
msgid "Berthe Morisot<br>(French, 1841-1895)"
msgstr "Berthe Morisot<br>(Francese, 1841-1895)"

#: inc/block-patterns.php:60
#, gp-priority: normal
msgid "Playing in the Sand"
msgstr "Giocando nella sabbia"

#: inc/block-patterns.php:60
#, gp-priority: normal
msgid "&#8220;Playing in the Sand&#8221; by Berthe Morisot"
msgstr "&#8220;Giocando nella sabbia&#8221; di Berthe Morisot"

#. translators: %s: Parent post.
#: image.php:61
#, gp-priority: normal
msgid "Published in %s"
msgstr "Pubblicato in %s"

#: classes/class-twenty-twenty-one-customize.php:108
#, gp-priority: normal
msgid "Summary"
msgstr "Riepilogo"

#: classes/class-twenty-twenty-one-customize.php:85
#, gp-priority: normal
msgid "Excerpt Settings"
msgstr "Impostazioni del riassunto"

#: inc/block-styles.php:98
#, gp-priority: normal
msgid "Thick"
msgstr "Spessore"

#: classes/class-twenty-twenty-one-customize.php:106
#, gp-priority: normal
msgid "On Archive Pages, posts show:"
msgstr "Nelle pagine d'archivio, gli articoli mostrano:"

#. translators: %s: Author name.
#: template-parts/post/author-bio.php:31
#, gp-priority: normal
msgid "View all of %s's posts."
msgstr "Visualizza tutti gli articoli di %s."

#. translators: %s: Author name.
#: inc/template-tags.php:49 template-parts/post/author-bio.php:19
#, gp-priority: normal
msgid "By %s"
msgstr "Di %s"

#: template-parts/content/content-none.php:61
#, gp-priority: normal
msgid "It seems we can&rsquo;t find what you&rsquo;re looking for. Perhaps searching can help."
msgstr "Non riusciamo a trovare quello che cerchi. Forse eseguire una ricerca potrebbe essere di aiuto."

#: template-parts/content/content-none.php:56
#, gp-priority: normal
msgid "Sorry, but nothing matched your search terms. Please try again with some different keywords."
msgstr "Non c'è nessuna corrispondenza con i termini di ricerca che hai indicato. Riprova con termini diversi."

#. translators: %s: Link to WP admin new post page.
#: template-parts/content/content-none.php:43
#, gp-priority: normal
msgid "Ready to publish your first post? <a href=\"%s\">Get started here</a>."
msgstr "Pronto per pubblicare il tuo primo articolo? <a href=\"%s\">Inizia da qui</a>."

#: single.php:40
#, gp-priority: normal
msgid "Previous post"
msgstr "Articolo precedente"

#: single.php:39
#, gp-priority: normal
msgid "Next post"
msgstr "Articolo successivo"

#. translators: %s: Parent post link.
#: single.php:25
#, gp-priority: normal
msgid "<span class=\"meta-nav\">Published in</span><span class=\"post-title\">%s</span>"
msgstr "<span class=\"meta-nav\">Pubblicato in</span><span class=\"post-title\">%s</span>"

#: searchform.php:26
#, gp-priority: normal
msgctxt "submit button"
msgid "Search"
msgstr "Cerca"

#: searchform.php:24
#, gp-priority: normal
msgid "Search&hellip;"
msgstr "Cerca&hellip;"

#. translators: %d: The number of search results.
#: search.php:33
#, gp-priority: normal
msgid "We found %d result for your search."
msgid_plural "We found %d results for your search."
msgstr[0] "Abbiamo trovato %d risultato per la tua ricerca."
msgstr[1] "Abbiamo trovato %d risultati per la tua ricerca."

#. translators: %s: Search term.
#: search.php:21 template-parts/content/content-none.php:22
#, gp-priority: normal
msgid "Results for \"%s\""
msgstr "Risultati per \"%s\""

#. translators: %s: List of tags.
#: inc/template-tags.php:118 inc/template-tags.php:162
#, gp-priority: normal
msgid "Tagged %s"
msgstr "Taggato %s"

#. translators: %s: List of categories.
#: inc/template-tags.php:108 inc/template-tags.php:152
#, gp-priority: normal
msgid "Categorized as %s"
msgstr "Etichettato come %s"

#. translators: Used between list items, there is a space after the comma.
#: inc/template-tags.php:104 inc/template-tags.php:114
#: inc/template-tags.php:148 inc/template-tags.php:158
#, gp-priority: normal
msgid ", "
msgstr ", "

#. translators: %s: Name of current post. Only visible to screen readers.
#: image.php:70 image.php:95 inc/template-tags.php:92 inc/template-tags.php:135
#: template-parts/content/content-page.php:48
#, gp-priority: normal
msgid "Edit %s"
msgstr "Modifica %s"

#. translators: %s: Name of current post.
#: inc/template-functions.php:138
#, gp-priority: normal
msgid "Continue reading %s"
msgstr "Continua a leggere %s"

#: inc/block-styles.php:71
#, gp-priority: normal
msgid "Dividers"
msgstr "Divisori"

#: inc/block-styles.php:62
#, gp-priority: normal
msgid "Frame"
msgstr "Cornice"

#: inc/block-styles.php:35 inc/block-styles.php:44 inc/block-styles.php:53
#: inc/block-styles.php:80 inc/block-styles.php:89
#, gp-priority: normal
msgid "Borders"
msgstr "Bordi"

#: inc/block-styles.php:26
#, gp-priority: normal
msgid "Overlap"
msgstr "Sovrapposizione"

#: inc/block-patterns.php:116
#, gp-priority: normal
msgctxt "Block pattern description"
msgid "A block with 3 columns that display contact information and social media links."
msgstr "Un blocco con 3 colonne che visualizzano informazioni di contatto e link ai social media."

#: inc/block-patterns.php:114
#, gp-priority: normal
msgid "Contact information"
msgstr "Informazioni di contatto"

#: inc/block-patterns.php:106
#, gp-priority: normal
msgctxt "Block pattern description"
msgid "A list of projects with thumbnail images."
msgstr "Un elenco di progetti con immagini in miniatura."

#: inc/block-patterns.php:104
#, gp-priority: normal
msgid "Portfolio list"
msgstr "Portfolio in modalità elenco"

#: inc/block-patterns.php:95
#, gp-priority: normal
msgctxt "Block pattern description"
msgid "An overlapping columns block with two images and a text description."
msgstr "Un blocco di colonne sovrapposte con due immagini e una descrizione testuale."

#: inc/block-patterns.php:92
#, gp-priority: normal
msgid "Overlapping images and text"
msgstr "Sovrapposizione di immagini e testo"

#: inc/block-patterns.php:83
#, gp-priority: normal
msgctxt "Block pattern description"
msgid "A media & text block with a big image on the left and a smaller one with bordered frame on the right."
msgstr "Un blocco media e testo con un'immagine grande a sinistra e una più piccola con cornice bordata a destra."

#: inc/block-patterns.php:80
#, gp-priority: normal
msgid "Two images showcase"
msgstr "Vetrina di due immagini"

#: inc/block-patterns.php:71
#, gp-priority: normal
msgctxt "Block pattern description"
msgid "Three images inside an overlapping columns block."
msgstr "Tre immagini all'interno di un blocco di colonne sovrapposte."

#: inc/block-patterns.php:68
#, gp-priority: normal
msgid "Overlapping images"
msgstr "Immagini sovrapposte"

#: inc/block-patterns.php:59
#, gp-priority: normal
msgctxt "Block pattern description"
msgid "A Media & Text block with a big image on the left and a heading on the right. The heading is followed by a separator and a description paragraph."
msgstr "Un blocco media e testo con un'immagine grande a sinistra e un'intestazione a destra. L'intestazione è seguita da un separatore e da un paragrafo di descrizione."

#: inc/block-patterns.php:56
#, gp-priority: normal
msgid "Media and text article title"
msgstr "Titolo media e testo articolo"

#: inc/block-patterns.php:48
#, gp-priority: normal
msgid "<EMAIL>"
msgstr "<EMAIL>"

#: inc/block-patterns.php:48
#, gp-priority: normal
msgid "Dribbble"
msgstr "Dribbble"

#: inc/block-patterns.php:48
#, gp-priority: normal
msgid "Instagram"
msgstr "Instagram"

#: inc/block-patterns.php:48
#, gp-priority: normal
msgid "Twitter"
msgstr "Twitter"

#: inc/block-patterns.php:48
#, gp-priority: normal
msgid "Let&#8217;s Connect."
msgstr "Mettiamoci in contatto."

#: inc/block-patterns.php:47
#, gp-priority: normal
msgctxt "Block pattern description"
msgid "A huge text followed by social networks and email address links."
msgstr "Un testo enorme seguito dai link ai social network e all'indirizzo email."

#: inc/block-patterns.php:44
#, gp-priority: normal
msgid "Links area"
msgstr "Area dei link"

#: inc/block-patterns.php:36
#, gp-priority: normal
msgid "A new portfolio default theme for WordPress"
msgstr "Un nuovo tema per portfolio predefinito per WordPress"

#: inc/block-patterns.php:33
#, gp-priority: normal
msgid "Large text"
msgstr "Testo lungo"

#: image.php:83
#, gp-priority: normal
msgctxt "Used before full size attachment link."
msgid "Full size"
msgstr "A dimensione piena"

#. translators: %s: Publish date.
#: inc/template-tags.php:29
#, gp-priority: normal
msgid "Published %s"
msgstr "Pubblicato %s"

#: comments.php:61 image.php:45 inc/template-tags.php:228
#: template-parts/content/content-page.php:33
#: template-parts/content/content-single.php:27
#: template-parts/content/content.php:33
#, gp-priority: normal
msgid "Page"
msgstr "Pagina"

#: template-parts/header/site-nav.php:19
#, gp-priority: normal
msgid "Close"
msgstr "Chiudi"

#: template-parts/header/site-nav.php:16
#, gp-priority: normal
msgid "Menu"
msgstr "Menu"

#: functions.php:76 inc/starter-content.php:154
#: template-parts/header/site-nav.php:13
#, gp-priority: normal
msgid "Primary menu"
msgstr "Menu principale"

#: header.php:26
#, gp-priority: normal
msgid "Skip to content"
msgstr "Salta al contenuto"

#: functions.php:363
#, gp-priority: normal
msgid "Add widgets here to appear in your footer."
msgstr "Aggiungi i widget qui per farli apparire nel tuo footer."

#: functions.php:361
#, gp-priority: normal
msgid "Footer"
msgstr "Footer"

#: functions.php:309
#, gp-priority: normal
msgid "Red to purple"
msgstr "Da rosso a viola"

#: functions.php:304
#, gp-priority: normal
msgid "Purple to red"
msgstr "Da viola a rosso"

#: functions.php:299
#, gp-priority: normal
msgid "Yellow to red"
msgstr "Dal giallo al rosso"

#: functions.php:294
#, gp-priority: normal
msgid "Red to yellow"
msgstr "Dal rosso al giallo"

#: functions.php:289
#, gp-priority: normal
msgid "Yellow to green"
msgstr "Dal giallo al verde"

#: functions.php:284
#, gp-priority: normal
msgid "Green to yellow"
msgstr "Dal verde al giallo"

#: functions.php:279
#, gp-priority: normal
msgid "Yellow to purple"
msgstr "Dal giallo al viola"

#: functions.php:274
#, gp-priority: normal
msgid "Purple to yellow"
msgstr "Dal viola al giallo"

#: functions.php:263
#, gp-priority: normal
msgid "White"
msgstr "Bianco"

#: functions.php:258
#, gp-priority: normal
msgid "Yellow"
msgstr "Giallo"

#: functions.php:253
#, gp-priority: normal
msgid "Orange"
msgstr "Arancione"

#: functions.php:248
#, gp-priority: normal
msgid "Red"
msgstr "Rosso"

#: functions.php:243
#, gp-priority: normal
msgid "Purple"
msgstr "Viola"

#: functions.php:238
#, gp-priority: normal
msgid "Blue"
msgstr "Blu"

#: functions.php:233
#, gp-priority: normal
msgid "Green"
msgstr "Verde"

#: functions.php:228
#, gp-priority: normal
msgid "Gray"
msgstr "Grigio"

#: functions.php:223 inc/block-styles.php:107
#, gp-priority: normal
msgid "Dark gray"
msgstr "Grigio scuro"

#: functions.php:218
#, gp-priority: normal
msgid "Black"
msgstr "Nero"

#: functions.php:186
#, gp-priority: normal
msgid "Gigantic"
msgstr "Gigantesco"

#: functions.php:180
#, gp-priority: normal
msgid "Huge"
msgstr "Enorme"

#: functions.php:174
#, gp-priority: normal
msgid "Extra large"
msgstr "Molto grande"

#: functions.php:168
#, gp-priority: normal
msgid "Large"
msgstr "Grande"

#: functions.php:162
#, gp-priority: normal
msgid "Normal"
msgstr "Normale"

#: functions.php:156
#, gp-priority: normal
msgid "Small"
msgstr "Piccolo"

#: functions.php:150
#, gp-priority: normal
msgid "Extra small"
msgstr "Molto piccolo"

#. translators: %s: WordPress.
#: footer.php:67
#, gp-priority: normal
msgid "Proudly powered by %s."
msgstr "Proudly powered by %s."

#: footer.php:24 functions.php:77 inc/starter-content.php:165
#, gp-priority: normal
msgid "Secondary menu"
msgstr "Menu secondario"

#: comments.php:78
#, gp-priority: normal
msgid "Comments are closed."
msgstr "I commenti sono chiusi."

#: comments.php:70
#, gp-priority: normal
msgid "Newer comments"
msgstr "Commenti più recenti"

#: comments.php:66
#, gp-priority: normal
msgid "Older comments"
msgstr "Commenti meno recenti"

#: classes/class-twenty-twenty-one-customize.php:109
#, gp-priority: normal
msgid "Full text"
msgstr "Testo completo"

#: classes/class-twenty-twenty-one-customize.php:75
#, gp-priority: normal
msgid "Display Site Title & Tagline"
msgstr "Mostra il titolo del sito e il motto"

#: 404.php:21
#, gp-priority: normal
msgid "It looks like nothing was found at this location. Maybe try a search?"
msgstr "Non è stato trovato nulla nel posto in cui stavi cercando. Forse potresti provare con una ricerca?"

#: 404.php:16 template-parts/content/content-none.php:30
#, gp-priority: normal
msgid "Nothing here"
msgstr "Non trovato"

#. Author URI of the theme
#: footer.php:68
#, gp-priority: low
msgid "https://wordpress.org/"
msgstr "https://wordpress.org/"

#. Author of the theme
#, gp-priority: low
msgid "the WordPress team"
msgstr "the WordPress team"

#. Theme URI of the theme
#, gp-priority: low
msgid "https://wordpress.org/themes/twentytwentyone/"
msgstr "https://wordpress.org/themes/twentytwentyone/"
