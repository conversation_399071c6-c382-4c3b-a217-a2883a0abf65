msgid ""
msgstr ""
"PO-Revision-Date: 2023-06-08 11:54:03+0000\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: GlotPress/2.4.0-alpha\n"
"Language: it\n"
"Project-Id-Version: WooCommerce - WooCommerce Payments\n"

#. Translators: %1$s: Opening anchor tag. %2$s: Closing anchor tag.
#: includes/admin/class-wc-payments-admin.php:219
msgid "Icelandic Króna does not accept decimals. Please update your currency number of decimals to 0 or select a different currency. %1$sVisit settings%2$s"
msgstr "La corona islandese non accetta i decimali. Aggiorna il numero di decimali della valuta a 0 o seleziona una valuta differente. %1$sVisita le impostazioni%2$s"

#: client/settings/fraud-protection/advanced-settings/rule-toggle.tsx:28
msgid "When enabled, the payment will be blocked."
msgstr "Quando l'opzione è attivata, il pagamento verrà bloccato."

#: client/settings/fraud-protection/advanced-settings/rule-toggle.tsx:85
msgid "Filter action"
msgstr "Azione filtro"

#: client/settings/fraud-protection/advanced-settings/rule-toggle.tsx:31
msgid "The payment will be blocked."
msgstr "Il pagamento verrà bloccato."

#: client/settings/fraud-protection/advanced-settings/rule-toggle.tsx:19
msgid "Authorize and hold for review"
msgstr "Autorizza e trattieni per revisione"

#: includes/woopay/class-woopay-order-status-sync.php:55
msgid "WCPay woopay order status sync"
msgstr "Sincronizzazione dello stato dell'ordine WCPay woopay"

#: includes/fraud-prevention/class-order-fraud-and-risk-meta-box.php:146
msgid "The payment for this order was held for review by your risk filtering and manually approved."
msgstr "Il pagamento per questo ordine è stato trattenuto per revisione dal filtro del rischio approvato manualmente."

#: includes/fraud-prevention/class-order-fraud-and-risk-meta-box.php:95
msgid "Approved"
msgstr "Approvato"

#: includes/class-wc-payments-utils.php:250
msgid "Romania"
msgstr "Romania"

#: includes/class-wc-payments-utils.php:235
msgid "Croatia"
msgstr "Croazia"

#: includes/class-wc-payments-utils.php:225
msgid "Bulgaria"
msgstr "Bulgaria"

#: client/overview/modal/progressive-onboarding-eligibility/index.tsx:59
msgid "Enable payments only"
msgstr "Attiva solo i pagamenti"

#: client/overview/modal/progressive-onboarding-eligibility/index.tsx:58
msgid "Set up payments and deposits"
msgstr "Configura pagamenti e acconti"

#: client/overview/modal/progressive-onboarding-eligibility/index.tsx:56
msgid "You have a $5,000 balance limit or 30 days from your first transaction to verify and set up deposits in your account."
msgstr "Hai un limite di saldo di € 5.000 o 30 giorni dalla tua prima transazione per verificare e configurare gli acconti nel tuo account."

#: client/overview/modal/progressive-onboarding-eligibility/index.tsx:55
msgid "Flexible process"
msgstr "Processo flessibile"

#: client/overview/modal/progressive-onboarding-eligibility/index.tsx:52
msgid "The setup process is super simple and ensures your store is ready to accept payments."
msgstr "Il processo di configurazione è semplicissimo e garantisce che il tuo negozio sia pronto ad accettare pagamenti."

#: client/overview/modal/progressive-onboarding-eligibility/index.tsx:51
msgid "Quick and easy setup"
msgstr "Configurazione facile e veloce"

#: client/overview/modal/progressive-onboarding-eligibility/index.tsx:48
msgid "Woo Payments enables you to start processing payments right away."
msgstr "Woo Payments ti consente di iniziare subito a elaborare i pagamenti."

#: client/overview/modal/progressive-onboarding-eligibility/index.tsx:47
msgid "Start selling instantly"
msgstr "Inizia a vendere immediatamente"

#: client/overview/modal/progressive-onboarding-eligibility/index.tsx:43
msgid "Start selling now with these benefits or continue the process to set up deposits."
msgstr "Inizia a vendere ora con questi vantaggi o continua il processo per configurare gli acconti."

#: client/overview/modal/progressive-onboarding-eligibility/index.tsx:42
msgid "You’re eligible to start selling now and fast-track the setup process."
msgstr "Puoi iniziare a vendere ora e accelerare il processo di configurazione."

#: client/components/payments-status/index.tsx:29
msgid "Pending verification"
msgstr "Verifica in sospeso"

#: includes/woopay/class-woopay-session.php:99
#: includes/woopay/class-woopay-session.php:100
msgid "WooPay request is not signed correctly."
msgstr "La richiesta di WooPay non è firmata correttamente."

#: client/settings/fraud-protection/advanced-settings/cards/address-mismatch.tsx:12
msgid "This filter screens for differences between the shipping information and the billing information (country)."
msgstr "Questo filtro esamina le differenze tra le informazioni di spedizione e le informazioni di fatturazione (paese)."

#: includes/fraud-prevention/class-order-fraud-and-risk-meta-box.php:172
msgid "The payment for this order was done in person and has bypassed your risk filtering."
msgstr "Il pagamento per questo ordine è stato effettuato di persona e ha aggirato il filtro del rischio."

#: includes/fraud-prevention/class-order-fraud-and-risk-meta-box.php:165
msgid "The payment for this order was held for review by your risk filtering. The authorization for the charge appears to have failed."
msgstr "Il pagamento per questo ordine è stato trattenuto per revisione dal filtro del rischio. L'autorizzazione per l'addebito sembra non essere riuscita."

#: includes/fraud-prevention/class-order-fraud-and-risk-meta-box.php:158
msgid "The payment for this order was held for review by your risk filtering. The authorization for the charge appears to have expired."
msgstr "Il pagamento per questo ordine è stato trattenuto per revisione dal filtro del rischio. L'autorizzazione all'addebito risulta essere scaduta."

#: includes/fraud-prevention/class-order-fraud-and-risk-meta-box.php:134
msgid "The payment for this order has not yet been passed to the fraud and risk filters to determine its outcome status."
msgstr "Il pagamento per questo ordine non è ancora passato attraverso i filtri di rischio e frode per determinarne lo stato dell'esito."

#: includes/fraud-prevention/class-order-fraud-and-risk-meta-box.php:124
msgid "Risk filtering is only available for orders processed using credit cards with WooCommerce Payments."
msgstr "Il filtro del rischio è disponibile solo per gli ordini elaborati utilizzando carte di credito con WooCommerce Payments."

#: includes/core/server/request/class-update-account.php:54
msgid "No account settings provided"
msgstr "Nessuna impostazione account fornita"

#. Translators: %s is a provided username.
#: includes/core/server/class-request.php:659
msgid "%s is not a valid username."
msgstr "%s non è un nome utente valido."

#. Translators: %s is a currency code.
#: includes/core/server/class-request.php:637
msgid "%1$s is not a valid redirect URL. Use a URL in the allowed_redirect_hosts filter."
msgstr "%1$s non è un URL di reindirizzamento valido. Usa un URL incluso nel filtro  allowed_redirect_hosts."

#: client/components/deposits-overview/suspended-deposit-notice.tsx:21
msgid "Your deposits are {{strong}}temporarily suspended{{/strong}}. {{suspendLink}}Learn more{{/suspendLink}}"
msgstr "I versamenti sono {{strong}}temporaneamente sospesi{{/strong}}. {{suspendLink}}Scopri di più{{/suspendLink}}"

#: client/components/deposits-overview/recent-deposits-list.tsx:67
msgid "Dispatch date"
msgstr "Data di invio"

#: includes/class-wc-payment-gateway-wcpay.php:718
#: includes/payment-methods/class-upe-payment-gateway.php:472
msgid "Invalid phone number."
msgstr "Numero di telefono non valido."

#: client/settings/fraud-protection/components/protection-level-modal-notice/index.tsx:16
msgid "Provides basic anti-fraud protection only."
msgstr "Fornisce solo una protezione antifrode di base."

#: client/settings/fraud-protection/components/fp-modal/index.tsx:20
msgid "Basic filter level"
msgstr "Livello del filtro di base"

#: client/settings/fraud-protection/advanced-settings/index.tsx:189
msgid "Back to Payments Settings"
msgstr "Torna alle impostazioni di pagamento"

#: client/settings/fraud-protection/advanced-settings/cards/ip-address-mismatch.tsx:22
msgid "Fraudulent transactions often use fake addresses to place orders. If the IP address seems to be in one country, but the billing address is in another, that could signal potential fraud."
msgstr "Le transazioni fraudolente usano spesso indirizzi falsi per effettuare ordini. Se l'indirizzo IP sembra essere in un Paese, ma l'indirizzo di fatturazione si trova in un altro, potrebbe essere un segnale di una potenziale frode."

#: client/settings/fraud-protection/advanced-settings/cards/ip-address-mismatch.tsx:21
msgid "Screen transactions where the IP country and billing country don't match"
msgstr "Controlla le transazioni in cui il Paese dell'IP e il Paese di fatturazione non corrispondono"

#: client/settings/fraud-protection/advanced-settings/cards/ip-address-mismatch.tsx:15
msgid "This filter screens for customer's {{ipAddressLink}}IP address{{/ipAddressLink}} to see if it is in a different country than indicated in their billing address."
msgstr "Questo filtro controlla l'{{/ipAddressLink}}indirizzo IP{{ipAddressLink}} del cliente per vedere se si trova in un Paese diverso da quello indicato nell'indirizzo di fatturazione."

#: client/settings/fraud-protection/advanced-settings/cards/ip-address-mismatch.tsx:14
msgid "IP Address Mismatch"
msgstr "Mancata corrispondenza indirizzo IP"

#: client/settings/fraud-protection/advanced-settings/cards/international-ip-address.tsx:18
msgid "This filter screens for {{ipAddressLink}}IP addresses{{/ipAddressLink}} outside of your {{supportedCountriesLink}}supported countries{{/supportedCountriesLink}}."
msgstr "Questo filtro controlla gli {{ipAddressLink}}indirizzi IP{{/ipAddressLink}} al di fuori dei {{supportedCountriesLink}}Paesi supportati{{/supportedCountriesLink}}."

#: client/components/fraud-risk-tools-banner/components/banner-body/index.tsx:7
msgid "New features have been added to WooPayments to help reduce fraudulent transactions on your store. By using a set of rules to evaluate incoming orders, your store is better protected from fraudsters."
msgstr "Sono state aggiunte nuove funzionalità a WooPayments per ridurre le transazioni fraudolente nel tuo negozio. Usando una serie di regole per valutare gli ordini in arrivo, il tuo negozio è protetto meglio dai truffatori."

#: includes/fraud-prevention/class-order-fraud-and-risk-meta-box.php:177
msgid "Risk filtering through WooCommerce Payments was not found on this order, it may have been created while filtering was not enabled."
msgstr "Il filtro del rischio per WooCommerce Payments non è stato trovato su questo ordine, potrebbe essere stato creato mentre il filtro non era attivo."

#. translators: %s - Payment method title
#: includes/fraud-prevention/class-order-fraud-and-risk-meta-box.php:120
msgid "Risk filtering is only available for orders processed using credit cards with WooCommerce Payments. This order was processed with %s."
msgstr "Il filtro del rischio è disponibile solo per gli ordini elaborati utilizzando carte di credito con WooCommerce Payments. L'ordine è stato elaborato con %s."

#: includes/fraud-prevention/class-order-fraud-and-risk-meta-box.php:108
msgid "View more details"
msgstr "Mostra più dettagli"

#: includes/fraud-prevention/class-order-fraud-and-risk-meta-box.php:107
msgid "The payment for this order was blocked by your risk filtering. There is no pending authorization, and the order can be cancelled to reduce any held stock."
msgstr "Il pagamento per questo ordine è stato bloccato dal filtro del rischio. Non è presente alcuna autorizzazione in sospeso e l'ordine può essere annullato per ridurre le scorte in giacenza."

#: includes/fraud-prevention/class-order-fraud-and-risk-meta-box.php:151
msgid "This transaction was held for review by your risk filters, and the charge was manually blocked after review."
msgstr "Questa transazione è stata trattenuta per la revisione dai tuoi filtri dei rischi e l'addebito è stato bloccato manualmente dopo la revisione."

#: includes/fraud-prevention/class-order-fraud-and-risk-meta-box.php:89
msgid "Red shield outline"
msgstr "Contorno protezione rosso"

#: includes/fraud-prevention/class-order-fraud-and-risk-meta-box.php:140
#: includes/fraud-prevention/class-order-fraud-and-risk-meta-box.php:152
#: includes/fraud-prevention/class-order-fraud-and-risk-meta-box.php:159
#: includes/fraud-prevention/class-order-fraud-and-risk-meta-box.php:166
msgid "Review payment"
msgstr "Rivedi il pagamento"

#: includes/fraud-prevention/class-order-fraud-and-risk-meta-box.php:139
msgid "The payment for this order was held for review by your risk filtering. You can review the details and determine whether to approve or block the payment."
msgstr "Il pagamento per questo ordine è stato trattenuto per la revisione dal filtro del rischio. Puoi rivedere i dettagli e determinare se approvare o bloccare il pagamento."

#: includes/fraud-prevention/class-order-fraud-and-risk-meta-box.php:96
msgid "Held for review"
msgstr "Trattenuto per la revisione"

#: includes/fraud-prevention/class-order-fraud-and-risk-meta-box.php:85
msgid "Orange shield outline"
msgstr "Contorno protezione arancione"

#: includes/fraud-prevention/class-order-fraud-and-risk-meta-box.php:102
msgid "The payment for this order passed your risk filtering."
msgstr "Il pagamento per questo ordine è stato approvato dal filtro del rischio."

#: includes/fraud-prevention/class-order-fraud-and-risk-meta-box.php:97
msgid "No action taken"
msgstr "Nessuna azione intrapresa"

#: includes/fraud-prevention/class-order-fraud-and-risk-meta-box.php:81
msgid "Green check mark"
msgstr "Segno di spunta verde"

#: includes/fraud-prevention/class-order-fraud-and-risk-meta-box.php:50
msgid "Fraud &amp; Risk"
msgstr "Frode e rischio"

#: includes/class-wc-payments-order-service.php:1495
msgid "The requested order was not found."
msgstr "L'ordine richiesto non è stato trovato."

#. translators: %1: the blocked amount, %2: transaction ID of the payment
#: includes/class-wc-payments-order-service.php:1194
msgid "&#x1F6AB; A payment of %1$s was <strong>blocked</strong> by one or more risk filters.<br><br><a>View more details</a>."
msgstr "&#x1F6AB; Un pagamento di %1$s è stato <strong>bloccato</strong> da uno o più filtri dei rischi.<br><br><a>Mostra più dettagli</a>"

#. translators: %1: the authorized amount, %2: transaction ID of the payment
#: includes/class-wc-payments-order-service.php:1160
msgid "&#x26D4; A payment of %1$s was <strong>held for review</strong> by one or more risk filters.<br><br><a>View more details</a>."
msgstr "⛔ Un pagamento di %1$s è <strong>stato trattenuto per revisione</strong> da uno o più filtri dei rischi.<br><br><a>Mostra più dettagli</a>"

#: includes/admin/class-wc-payments-admin.php:1066
msgid "Transaction Fee:"
msgstr "Commissione di transazione:"

#: includes/admin/class-wc-payments-admin.php:1065
msgid "This represents the fee WooCommerce Payments collects for the transaction."
msgstr "Questa opzione rappresenta la commissione che WooCommerce Payments riscuote per la transazione."

#: includes/fraud-prevention/class-order-fraud-and-risk-meta-box.php:94
msgid "Blocked"
msgstr "Bloccato"

#: client/transactions/risk-review/index.tsx:92
msgid "transactions(s)"
msgstr "transazioni"

#: client/transactions/risk-review/index.tsx:116
msgid "Search by order number or customer name"
msgstr "Cerca per numero ordine o nome del cliente"

#: client/settings/fraud-protection/components/protection-levels/index.tsx:40
msgid "Basic"
msgstr "Base"

#: client/settings/fraud-protection/components/fp-help-text/index.tsx:16
msgid "Provides the base level of platform protection."
msgstr "Fornisce il livello base di protezione della piattaforma."

#: client/settings/fraud-protection/advanced-settings/index.tsx:168
#: client/settings/fraud-protection/components/protection-levels/index.tsx:33
msgid "There was an error retrieving your fraud protection settings. Please refresh the page to try again."
msgstr "Si è verificato un errore durante il recupero delle impostazioni di protezione dalle frodi. Aggiorna la pagina per riprovare."

#: client/settings/fraud-protection/advanced-settings/index.tsx:88
msgid "Current protection level is set to \"advanced\"."
msgstr "Il livello di protezione corrente è impostato su \"avanzato\"."

#: client/settings/fraud-protection/advanced-settings/cards/cvc-verification.tsx:22
msgid "For security, this filter is enabled and cannot be modified. Payments failing CVC verification will be blocked. {{learnMoreLink}}Learn more{{/learnMoreLink}}"
msgstr "Per motivi di sicurezza, questo filtro è abilitato e non può essere modificato. I pagamenti che non superano la verifica CVC verranno bloccati. {{learnMoreLink}}Scopri di più{{/learnMoreLink}}"

#: client/settings/fraud-protection/advanced-settings/cards/cvc-verification.tsx:30
msgid "This filter is disabled, and can not be modified."
msgstr "Questo filtro è disabilitato e non può essere modificato."

#: client/settings/fraud-protection/advanced-settings/allow-countries-notice.tsx:37
msgid "Enabling this filter will not have any effect because you are selling to all countries."
msgstr "L'abilitazione di questo filtro non avrà alcun effetto perché stai vendendo in tutti i paesi."

#: client/settings/fraud-protection/advanced-settings/allow-countries-notice.tsx:21
msgid "Orders from outside of the following countries will be screened by the filter: "
msgstr "Gli ordini al di fuori dei seguenti paesi verranno schermati dal filtro: "

#: client/settings/fraud-protection/advanced-settings/allow-countries-notice.tsx:20
msgid "Orders from outside of the following countries will be blocked by the filter: "
msgstr "Gli ordini al di fuori dei seguenti paesi verranno bloccati dal filtro: "

#: client/settings/fraud-protection/advanced-settings/allow-countries-notice.tsx:16
msgid "Orders from the following countries will be screened by the filter: "
msgstr "Gli ordini dai seguenti paesi verranno schermati dal filtro: "

#: client/settings/fraud-protection/advanced-settings/allow-countries-notice.tsx:15
msgid "Orders from the following countries will be blocked by the filter: "
msgstr "Gli ordini dai seguenti paesi verranno bloccati dal filtro: "

#: client/transactions/list/index.tsx:111
msgid "Tap to Pay on Android"
msgstr "Tocca per pagare su Android"

#: client/transactions/list/index.tsx:108
msgid "Tap to Pay on iPhone"
msgstr "Tocca per pagare su iPhone"

#: client/data/authorizations/actions.ts:203
msgid "There has been an error canceling the payment for order #%s. Please try again later."
msgstr "Si è verificato un errore durante l'annullamento del pagamento per l'ordine #%s. Riprova più tardi."

#: client/data/authorizations/actions.ts:194
msgid "Payment for order #%s canceled successfully."
msgstr "Pagamento per l'ordine #%s annullato correttamente."

#: client/components/transaction-status-chip/mappings.ts:13
msgid "Needs review"
msgstr "Richiede revisione"

#: client/components/fraud-risk-tools-banner/components/new-pill/index.tsx:11
msgid "New"
msgstr "Nuovo"

#: client/components/fraud-risk-tools-banner/components/banner-actions/index.tsx:18
msgid "Remind me later"
msgstr "Ricordamelo più tardi"

#. translators: %s: the error message.
#: includes/admin/class-wc-rest-payments-orders-controller.php:507
msgid "Payment cancel failed to complete with the following message: %s"
msgstr "Impossibile annullare l'acquisizione del pagamento con il seguente messaggio: %s"

#: includes/admin/class-wc-rest-payments-orders-controller.php:492
#: includes/admin/class-wc-rest-payments-orders-controller.php:495
msgid "The payment cannot be canceled"
msgstr "Il pagamento non può essere annullato"

#: includes/admin/class-wc-rest-payments-orders-controller.php:478
msgid "Payment cannot be canceled for partially or fully refunded orders."
msgstr "Il pagamento non può essere annullato per ordini parzialmente o completamente rimborsati."

#: client/settings/fraud-protection/components/protection-levels/index.tsx:58
msgid "Configure"
msgstr "Configura"

#: client/settings/fraud-protection/components/fp-help-text/index.tsx:12
msgid "Allows you to fine-tune the level of filtering according to your business needs."
msgstr "Consente di ottimizzare il livello di filtraggio in base alle proprie esigenze aziendali."

#: client/settings/fraud-protection/advanced-settings/rule-toggle.tsx:23
msgid "Block Payment"
msgstr "Blocco Pagamenti"

#: client/settings/fraud-protection/components/protection-levels/index.tsx:50
msgid "Advanced"
msgstr "Avanzato"

#: client/settings/fraud-protection/advanced-settings/rule-toggle.tsx:80
msgid "Enable filtering"
msgstr "Abilita filtro"

#: client/settings/fraud-protection/advanced-settings/rule-description.tsx:13
msgid "How does this filter protect me?"
msgstr "Come mi protegge questo filtro?"

#: client/settings/fraud-protection/advanced-settings/index.tsx:46
msgid "Advanced fraud protection"
msgstr "Protezione avanzata dalle frodi"

#: client/settings/fraud-protection/advanced-settings/index.tsx:152
msgid "Save Changes"
msgstr "Salva le modifiche"

#: client/settings/fraud-protection/advanced-settings/index.tsx:166
msgid "Settings were not saved."
msgstr "Le impostazioni non sono state salvate."

#: client/settings/fraud-protection/advanced-settings/cards/purchase-price-threshold.tsx:63
msgid "Maximum purchase price"
msgstr "Prezzo di acquisto massimo"

#: client/settings/fraud-protection/advanced-settings/cards/purchase-price-threshold.tsx:60
msgid "Minimum purchase price"
msgstr "Prezzo di acquisto minimo"

#: client/settings/fraud-protection/advanced-settings/cards/purchase-price-threshold.tsx:87
msgid "Maximum purchase price must be greater than the minimum purchase price."
msgstr "Il prezzo massimo di acquisto deve essere superiore al prezzo minimo di acquisto."

#: client/settings/fraud-protection/advanced-settings/cards/purchase-price-threshold.tsx:83
msgid "A price range must be set for the \"Purchase Price threshold\" filter."
msgstr "È necessario impostare una fascia di prezzo per il filtro \"Soglia prezzo di acquisto\"."

#: client/settings/fraud-protection/advanced-settings/cards/purchase-price-threshold.tsx:75
msgid "An unusually high purchase amount, compared to the average for your business, can indicate potential fraudulent activity."
msgstr "Un importo di acquisto insolitamente elevato, rispetto alla media della tua attività, può indicare una potenziale attività fraudolenta."

#: client/settings/fraud-protection/advanced-settings/cards/purchase-price-threshold.tsx:73
msgid "Block transactions for abnormal purchase prices"
msgstr "Blocca le transazioni per prezzi di acquisto anomali"

#: client/settings/fraud-protection/advanced-settings/cards/purchase-price-threshold.tsx:72
msgid "This filter compares the purchase price of an order to the minimum and maximum purchase amounts that you specify."
msgstr "Questo filtro confronta il prezzo di acquisto di un ordine con gli importi di acquisto minimo e massimo specificati."

#: client/settings/fraud-protection/advanced-settings/cards/purchase-price-threshold.tsx:72
msgid "Purchase Price Threshold"
msgstr "Soglia prezzo di acquisto"

#: client/settings/fraud-protection/advanced-settings/cards/purchase-price-threshold.tsx:67
msgid "A price range must be set for this filter to take effect."
msgstr "Affinché questo filtro abbia effetto, è necessario impostare una fascia di prezzo."

#: client/settings/fraud-protection/advanced-settings/cards/order-items-threshold.tsx:49
msgid "Maximum items per order"
msgstr "Articoli massimi per ordine"

#: client/settings/fraud-protection/advanced-settings/cards/order-items-threshold.tsx:44
msgid "Minimum items per order"
msgstr "Articoli minimi per ordine"

#: client/settings/fraud-protection/advanced-settings/cards/order-items-threshold.tsx:73
msgid "Maximum item count must be greater than the minimum item count on the \"Order Item Threshold\" rule."
msgstr "Il numero massimo di articoli deve essere maggiore del numero minimo di articoli nella regola \"Soglia articolo ordine\"."

#: client/settings/fraud-protection/advanced-settings/cards/order-items-threshold.tsx:69
msgid "An item range must be set for the \"Order Item Threshold\" filter."
msgstr "È necessario impostare un intervallo di articoli per il filtro \"Soglia articolo ordine\"."

#: client/settings/fraud-protection/advanced-settings/cards/order-items-threshold.tsx:63
msgid "An unusually high item count, compared to the average for your business, can indicate potential fraudulent activity."
msgstr "Un numero di articoli insolitamente elevato, rispetto alla media della tua attività, può indicare una potenziale attività fraudolenta."

#: client/settings/fraud-protection/advanced-settings/cards/order-items-threshold.tsx:61
msgid "Block transactions for abnormal item counts"
msgstr "Blocca le transazioni per i conteggi di elementi anomali"

#: client/settings/fraud-protection/advanced-settings/cards/order-items-threshold.tsx:60
msgid "This filter compares the amount of items in an order to the minimum and maximum counts that you specify."
msgstr "Questo filtro confronta la quantità di articoli di un ordine con i numeri minimo e massimo specificati."

#: client/settings/fraud-protection/advanced-settings/cards/order-items-threshold.tsx:60
msgid "Order Items Threshold"
msgstr "Soglia articoli ordine"

#: client/settings/fraud-protection/advanced-settings/cards/order-items-threshold.tsx:58
msgid "Maximum item count must be greater than the minimum item count."
msgstr "Il numero massimo di articoli deve essere maggiore del numero minimo di articoli."

#: client/settings/fraud-protection/advanced-settings/cards/order-items-threshold.tsx:55
msgid "An item range must be set for this filter to take effect."
msgstr "Affinché questo filtro abbia effetto, è necessario impostare una fascia di articoli."

#: client/settings/fraud-protection/advanced-settings/cards/order-items-threshold.tsx:52
#: client/settings/fraud-protection/advanced-settings/cards/purchase-price-threshold.tsx:64
msgid "Leave blank for no limit"
msgstr "Lascia vuoto per nessun limite"

#: client/settings/fraud-protection/advanced-settings/cards/international-ip-address.tsx:31
msgid "You should be especially wary when a customer has an international IP address but uses domestic billing and shipping information. Fraudsters often pretend to live in one location, but live and shop from another."
msgstr "Dovresti prestare particolare attenzione quando un cliente ha un indirizzo IP internazionale ma utilizza informazioni di fatturazione e spedizione nazionali. I truffatori spesso fingono di vivere in un luogo, ma vivono e fanno acquisti da un altro."

#: client/settings/fraud-protection/advanced-settings/cards/international-ip-address.tsx:30
msgid "Block transactions for international IP addresses"
msgstr "Blocca le transazioni per gli indirizzi IP internazionali"

#: client/settings/fraud-protection/advanced-settings/cards/international-ip-address.tsx:17
msgid "International IP Address"
msgstr "Indirizzo IP internazionale"

#: client/settings/fraud-protection/advanced-settings/cards/cvc-verification.tsx:18
msgid "Because the card security code appears only on the card and not on receipts or statements, the card security code provides some assurance that the physical card is in the possession of the buyer."
msgstr "Poiché il codice di sicurezza della carta appare solo sulla carta e non sulle ricevute o sugli estratti conto, il codice di sicurezza della carta fornisce alcune garanzie che la carta fisica sia in possesso dell'acquirente."

#: client/settings/fraud-protection/advanced-settings/cards/cvc-verification.tsx:17
msgid "This filter checks the security code submitted by the customer against the data on file with the card issuer."
msgstr "Questo filtro confronta il codice di sicurezza inviato dal cliente con i dati in archivio presso l'emittente della carta."

#: client/settings/fraud-protection/advanced-settings/cards/cvc-verification.tsx:17
msgid "CVC Verification"
msgstr "Verifica CVC"

#: client/settings/fraud-protection/advanced-settings/cards/avs-mismatch.tsx:16
msgid "Buyers who can provide the street number and post code on file with the issuing bank are more likely to be the actual account holder. AVS matches, however, are not a guarantee."
msgstr "È più probabile che gli acquirenti che possono fornire il numero civico e il codice postale registrati presso la banca emittente siano i titolari effettivi del conto. Le corrispondenze AVS, tuttavia, non sono una garanzia."

#: client/settings/fraud-protection/advanced-settings/cards/avs-mismatch.tsx:13
msgid "This filter compares the street number and the post code submitted by the customer against the data on file with the card issuer."
msgstr "Questo filtro confronta il numero civico e il codice postale inviato dal cliente con i dati in archivio presso l'emittente della carta."

#: client/settings/fraud-protection/advanced-settings/cards/avs-mismatch.tsx:13
msgid "AVS Mismatch"
msgstr "Mancata corrispondenza AVS"

#: client/settings/fraud-protection/advanced-settings/cards/address-mismatch.tsx:15
msgid "There are legitimate reasons for a billing/shipping mismatch with a customer purchase, but a mismatch could also indicate that someone is using a stolen identity to complete a purchase."
msgstr "Esistono ragioni legittime per una mancata corrispondenza di fatturazione/spedizione con l'acquisto di un cliente, ma una mancata corrispondenza potrebbe anche indicare che qualcuno sta utilizzando un'identità rubata per completare un acquisto."

#: client/settings/fraud-protection/advanced-settings/rule-toggle.tsx:30
msgid "The payment method will not be charged until you review and approve the transaction."
msgstr "Il metodo di pagamento non verrà addebitato finché non esamini e approvi la transazione."

#: client/settings/fraud-protection/advanced-settings/cards/address-mismatch.tsx:14
msgid "Block transactions for mismatched addresses"
msgstr "Blocca le transazioni per gli indirizzi non corrispondenti"

#: client/settings/fraud-protection/advanced-settings/cards/address-mismatch.tsx:12
msgid "Address Mismatch"
msgstr "Mancata corrispondenza indirizzo"

#: client/components/welcome/index.tsx:16
msgid "Good morning, %s"
msgstr "Buongiorno, %s"

#: client/components/welcome/index.tsx:25
msgid "Good evening"
msgstr "Buonasera"

#: client/components/welcome/index.tsx:24
msgid "Good afternoon"
msgstr "Buon pomeriggio"

#: client/components/welcome/index.tsx:23
msgid "Good morning"
msgstr "Buongiorno"

#: client/components/welcome/index.tsx:20
msgid "Good evening, %s"
msgstr "Buonasera, %s"

#: client/components/welcome/index.tsx:18
msgid "Good afternoon, %s"
msgstr "Buon pomeriggio, %s"

#: client/settings/fraud-protection/components/protection-level-modal-notice/index.tsx:17
msgid "Provides a standard level of filtering that's suitable for most business."
msgstr "Fornisce un livello standard di filtro che è idoneo per la maggior parte delle attività."

#: client/settings/fraud-protection/components/fp-modal/index.tsx:32
msgid "The card's issuing bank cannot verify the CVV."
msgstr "La banca che emette la carta non può verificare il CVV."

#: client/settings/fraud-protection/components/fp-modal/index.tsx:31
msgid "The billing address does not match what is on file with the card issuer."
msgstr "L'indirizzo di fatturazione non corrisponde a quanto registrato dall'emittente della carta."

#: client/settings/fraud-protection/components/fp-modal/index.tsx:25
msgid "Payments will be {{blocked}}blocked{{/blocked}} if:"
msgstr "Il pagamento verrà {{blocked}}bloccato{{/blocked}} se:"

#: client/settings/fraud-protection/components/fp-help-text/index.tsx:14
msgid "Provides a standard level of filtering that's suitable for most businesses."
msgstr "Fornisce un livello standard di filtro che è idoneo per la maggior parte delle attività."

#: client/settings/fraud-protection/components/fp-help-text/index.tsx:10
#: client/settings/fraud-protection/components/protection-level-modal-notice/index.tsx:18
msgid "Offers the highest level of filtering for stores, but may catch some legitimate transactions."
msgstr "Offre il più alto livello di filtro per i negozi, ma può rilevare alcune transazioni legittime."

#: includes/core/server/request/class-list-disputes.php:130
msgid "The search parameter must be a string, or an array of strings."
msgstr "Il parametro di ricerca deve essere una stringa o una matrice di stringhe."

#: includes/class-wc-payments-utils.php:252
msgid "Slovakia"
msgstr "Slovacchia"

#: includes/class-wc-payments-utils.php:251
msgid "Slovenia"
msgstr "Slovenia"

#: includes/class-wc-payments-utils.php:246
msgid "Norway"
msgstr "Norvegia"

#: includes/class-wc-payments-utils.php:244
msgid "Malta"
msgstr "Malta"

#: includes/class-wc-payments-utils.php:243
msgid "Latvia"
msgstr "Lettonia"

#: includes/class-wc-payments-utils.php:242
msgid "Lithuania"
msgstr "Lituania"

#: includes/class-wc-payments-utils.php:238
msgid "Greece"
msgstr "Grecia"

#: includes/class-wc-payments-utils.php:236
msgid "Luxembourg"
msgstr "Lussemburgo"

#: includes/class-wc-payments-utils.php:232
msgid "Finland"
msgstr "Finlandia"

#: includes/class-wc-payments-utils.php:231
msgid "Estonia"
msgstr "Estonia"

#: includes/class-wc-payments-utils.php:230
msgid "Denmark"
msgstr "Danimarca"

#: includes/class-wc-payments-utils.php:228
msgid "Cyprus"
msgstr "Cipro"

#: includes/core/server/request/class-create-and-confirm-intention.php:86
msgid "Intentions require at least one payment method"
msgstr "Le intenzioni richiedono almeno un metodo di pagamento"

#. Translators: %1$s is a provided date string, %2$s is a date format.
#: includes/core/server/class-request.php:614
msgid "%1$s is not a valid date for format %2$s."
msgstr "%1$s non è una data valida per il formato %2$s."

#. Translators: %s is a currency code.
#: includes/core/server/class-request.php:567
msgid "%s is not a supported currency for payments."
msgstr "%s non è una valuta supportata per i pagamenti."

#. Translators: %s is a Stripe ID.
#: includes/core/server/class-request.php:527
msgid "%s is not a valid Stripe identifier"
msgstr "%s non è un identificativo di Stripe valido"

#: includes/core/server/class-request.php:497
msgid "Empty parameter is not allowed"
msgstr "Non è consentito il parametro vuoto"

#. translators: %s: documentation URL
#: includes/class-wc-payments.php:1274
msgid "The WooCommerce version you have installed is not compatible with WooCommerce Payments for a Norwegian business. Please update WooCommerce to version 7.5 or above. You can do that via the <a1>the plugins page.</a1>"
msgstr "La versione di WooCommerce che hai installato non è compatibile con WooCommerce Payments per un'azienda norvegese. Aggiorna WooCommerce alla versione 7.5 o versioni superiori. Puoi farlo tramite la <a1>pagina dei plugin.</a1>"

#: includes/payment-methods/class-sepa-payment-method.php:39
msgid "<strong>Test mode:</strong> use the test account number ********************. Other payment methods may redirect to a Stripe test page to authorize payment. More test card numbers are listed <a>here</a>."
msgstr "<strong>Modalità di test:</strong> usa il numero di account di test ********************. Altri metodi possono reindirizzare alla pagina di prova di Stripe per l'autorizzazione al pagamento. Ulteriori numeri della carta di credito di prova sono elencati <a>qui</a>."

#: includes/payment-methods/class-becs-payment-method.php:39
msgid "<strong>Test mode:</strong> use the test account number *********. Other payment methods may redirect to a Stripe test page to authorize payment. More test card numbers are listed <a>here</a>."
msgstr "<strong>Modalità di test:</strong> usa il numero di account di test *********. Altri metodi possono reindirizzare alla pagina di prova di Stripe per l'autorizzazione al pagamento. Ulteriori numeri della carta di credito di prova sono elencati <a>qui</a>."

#: includes/subscriptions/class-wc-payments-subscriptions-event-handler.php:88
msgid "Suspended WCPay Subscription in invoice.upcoming webhook handler because subscription next_payment date is 0."
msgstr "Abbonamento WCPay sospeso nel gestore webhook bill.upcoming perché la data next_payment dell'abbonamento è 0."

#: includes/subscriptions/class-wc-payments-subscription-service.php:486
msgid "Suspended WCPay Subscription because subscription status changed to on-hold."
msgstr "Abbonamento WCPay sospeso perché lo stato dell'abbonamento è cambiato in sospeso."

#. translators: tosLink: Link to terms of service page, privacyLink: Link to
#. privacy policy page
#: includes/class-wc-payment-gateway-wcpay.php:220
msgid ""
"WooCommerce Payments gives your store flexibility to accept credit cards, debit cards, and Apple Pay. Enable popular local payment methods and other digital wallets like Google Pay to give customers even more choice.<br/><br/>\n"
"\t\t\tBy using WooCommerce Payments you agree to be bound by our <tosLink>Terms of Service</tosLink>  and acknowledge that you have read our <privacyLink>Privacy Policy</privacyLink>"
msgstr ""
"WooCommerce Payments offre al tuo negozio la flessibilità di accettare carte di credito, carte di debito e Apple Pay. Attiva i metodi di pagamento locali più diffusi e altri portafogli digitali come Google Pay per offrire ai clienti ancora più scelta.<br/><br/>\n"
"\t\t\tUsando WooCommerce Payments, accetti i <tosLink>Termini di servizio</tosLink>   e confermi di aver letto la <privacyLink>Politica sulla privacy</privacyLink>"

#: includes/class-wc-payments-order-success-page.php:102
msgid "We prevented multiple payments for the same order. If this was a mistake and you wish to try again, please create a new order."
msgstr "Abbiamo evitato pagamenti multipli per lo stesso ordine. Se si è trattato di un errore e desideri riprovare, crea un nuovo ordine."

#: includes/class-wc-payments-order-success-page.php:84
msgid "We detected and prevented an attempt to pay for a duplicate order. If this was a mistake and you wish to try again, please create a new order."
msgstr "Abbiamo rilevato e impedito un tentativo di pagamento per un ordine duplicato. Se si è trattato di un errore e desideri riprovare, crea un nuovo ordine."

#. translators: order ID integer number
#: includes/class-wc-payment-gateway-wcpay.php:2057
msgid "WooCommerce Payments: detected and deleted order ID %d, which has duplicate cart content with this order."
msgstr "WooCommerce Payments: ID ordine %d rilevato ed eliminato, in quanto presenta un contenuto del carrello duplicato con questo ordine."

#: includes/wc-payment-api/class-wc-payments-api-client.php:797
msgid "Currency From parameter is required"
msgstr "Il parametro Valuta da è obbligatorio"

#. translators: 1) date in date_format or 'F j, Y'; 2) time in time_format or
#. 'g:i a'
#: includes/compat/subscriptions/trait-wc-payment-gateway-wcpay-subscriptions.php:942
msgid "The customer must authorize this payment via a notification sent to them by the bank which issued their card. The authorization must be completed before %1$s at %2$s, when the charge will be attempted."
msgstr "Il cliente deve autorizzare questo pagamento tramite una notifica inviatagli dalla banca che ha emesso la sua carta. Quando verrà tentato l'addebito, l'autorizzazione deve essere completata prima del %1$s alle %2$s."

#. translators: %s Stripe error message.
#: includes/class-wc-payments-webhook-processing-service.php:758
msgid "With the following message: <code>%s</code>"
msgstr "Con il seguente messaggio: <code>%s</code>"

#: includes/class-wc-payments-webhook-processing-service.php:753
msgid "For recurring payment greater than mandate amount or INR 15000, payment was not approved by the card holder."
msgstr "Per pagamenti ricorrenti superiori all'importo del mandato o a INR 15000, il pagamento non è stato approvato dal titolare della carta."

#: includes/class-wc-payments-webhook-processing-service.php:751
msgid "The customer's bank could not send pre-debit notification for the payment."
msgstr "La banca del cliente non ha potuto inviare la notifica di pre-addebito per il pagamento."

#: includes/subscriptions/templates/html-wcpay-deactivate-warning.php:48
msgid "Yes, deactivate WooCommerce Payments"
msgstr "Sì, disattiva WooCommerce Payments"

#: includes/subscriptions/templates/html-wcpay-deactivate-warning.php:43
msgid "Are you sure you want to deactivate WooCommerce Payments?"
msgstr "Desideri disattivare WooCommerce Payments?"

#. translators: $1 $2 placeholders are opening and closing HTML link tags,
#. linking to documentation.
#: includes/subscriptions/templates/html-wcpay-deactivate-warning.php:37
msgid "If you do not want these subscriptions to continue to be billed, you should %1$scancel all subscriptions%2$s prior to deactivating WooCommerce Payments. "
msgstr "Se non desideri che questi abbonamenti continuino a essere fatturati, devi %1$sannullare tutti gli abbonamenti%2$s prima di disattivare WooCommerce Payments. "

#. translators: $1 $2 $3 placeholders are opening and closing HTML link tags,
#. linking to documentation. $4 $5 placeholders are opening and closing strong
#. HTML tags.
#: includes/subscriptions/templates/html-wcpay-deactivate-warning.php:24
msgid "Your store has active subscriptions using the built-in WooCommerce Payments functionality. Due to the %1$soff-site billing engine%3$s these subscriptions use, %4$sthey will continue to renew even after you deactivate WooCommerce Payments%5$s. %2$sLearn more%3$s."
msgstr "Per il tuo negozio sono attivi abbonamenti che usano la funzionalità WooCommerce Payments integrata. A causa del %1$smotore di fatturazione esterno al sito%3$s usato da questi abbonamenti, %4$scontinueranno a rinnovarsi anche dopo la disattivazione di WooCommerce Payments%5$s. %2$sScopri di più%3$s."

#. Translators: $1 and $2 placeholders are opening and closing strong HTML
#. tags. $3 and $4 are opening and closing link HTML tags. $5 is an opening
#. link HTML tag.
#: includes/subscriptions/templates/html-subscriptions-plugin-notice.php:33
msgid "Existing subscriptions will %1$s%3$srenew manually%4$s%2$s, meaning that subscribers will need to log in to pay for renewal. Access to the advanced features of the Subscriptions extension will be removed. %5$sLearn more.%4$s"
msgstr "Gli abbonamenti esistenti verranno %1$s%3$srinnovati manualmente%4$s%2$s, ciò significa che gli abbonati dovranno effettuare l'accesso per pagare il rinnovo. L'accesso alle funzionalità avanzate dell'estensione Abbonamento verrà rimosso. %5$sScopri di più.%4$s"

#. Translators: placeholders are opening and closing strong HTML tags.
#: includes/subscriptions/templates/html-subscriptions-plugin-notice.php:24
msgid "By deactivating the %1$sWooCommerce Subscriptions%2$s plugin, your store will switch to using the subscriptions functionality %1$sbuilt into WooCommerce Payments%2$s."
msgstr "Disattivando il plugin %1$sWooCommerce Subscriptions%2$s, il tuo negozio passerà all'uso della funzionalità abbonamenti %1$sintegrata in WooCommerce Payments%2$s."

#: client/data/authorizations/resolvers.ts:138
msgid "Error retrieving uncaptured transactions."
msgstr "Errore durante il recupero delle transazioni non acquisite."

#: client/data/authorizations/resolvers.ts:106
msgid "Error retrieving authorization."
msgstr "Errore durante il recupero dell'autorizzazione."

#: client/data/authorizations/actions.ts:127
msgid "There has been an error capturing the payment for order #%s. Please try again later."
msgstr "Si è verificato un errore durante l'acquisizione del pagamento per l'ordine #%s. Riprova più tardi."

#: client/data/authorizations/actions.ts:118
msgid "Payment for order #%s captured successfully."
msgstr "Pagamento per l'ordine #%s acquisito correttamente."

#: includes/notes/class-wc-payments-notes-set-up-stripelink.php:78
msgid "Reduce cart abandonment and create a frictionless checkout experience with Link by Stripe. Link autofills your customer’s payment and shipping details, so they can check out in just six seconds with the Link optimized experience."
msgstr "Riduci l'abbandono del carrello e crea un'esperienza di pagamento senza interruzioni con Link by Stripe. Link compila automaticamente i dettagli di pagamento e spedizione dei tuoi clienti in modo che possano effettuare il check-out in soli sei secondi grazie a un'esperienza ottimizzata per Link."

#: client/components/file-upload/index.tsx:53
msgid ": Evidence file was not uploaded"
msgstr ": il file di prova non è stato caricato"

#: includes/notes/class-wc-payments-notes-set-up-stripelink.php:77
msgid "Increase conversion at checkout"
msgstr "Aumenta la conversione alla cassa"

#: client/connect-account-page/info-notice-modal.tsx:70
#: client/settings/fraud-protection/components/fp-modal/index.tsx:33
#: client/settings/fraud-protection/tour/steps.tsx:60
msgid "Got it"
msgstr "Fatto"

#. translators: customer email
#: includes/class-wc-payment-token-wcpay-link.php:53
msgid "Stripe Link email %s"
msgstr "E-mail di collegamento a Stripe %s"

#: includes/notes/class-wc-payments-notes-set-https-for-checkout.php:52
msgid "Enable HTTPS on your checkout pages to display all available payment methods and protect your customers data."
msgstr "Attiva HTTPS sulle pagine di pagamento per mostrare tutti i metodi di pagamento disponibili e proteggere i dati dei clienti."

#: includes/notes/class-wc-payments-notes-set-https-for-checkout.php:51
msgid "Enable secure checkout"
msgstr "Attiva il pagamento sicuro"

#. Translators: %d is the numeric ID of the product without a price.
#: includes/class-wc-payments-payment-request-button-handler.php:228
msgid "Express checkout does not support products without prices! Please add a price to product #%d"
msgstr "Il checkout rapido non supporta i prodotti senza prezzi! Aggiungere un prezzo al prodotto #%d"

#: includes/admin/class-wc-rest-payments-settings-controller.php:157
msgid "Monthly anchor for deposit scheduling when interval is set to monthly"
msgstr "Ancora mensile per la pianificazione degli acconti quando l'intervallo è impostato su mensile"

#: includes/admin/class-wc-rest-payments-settings-controller.php:153
msgid "Weekly anchor for deposit scheduling when interval is set to weekly"
msgstr "Ancora settimanale per la pianificazione degli acconti quando l'intervallo è impostato su settimanale"

#: includes/admin/class-wc-rest-payments-settings-controller.php:149
msgid "An interval for deposit scheduling."
msgstr "Un intervallo per la pianificazione dell'acconto."

#: includes/class-wc-payments-payment-request-button-handler.php:1191
msgid "Unable to determine payment success."
msgstr "Impossibile determinare la riuscita del pagamento."

#: includes/class-wc-payments-payment-request-button-handler.php:1179
msgid "This order does not require payment!"
msgstr "Questo ordine non richiede il pagamento!"

#: includes/class-wc-payments-payment-request-button-handler.php:1175
msgid "Invalid order!"
msgstr "Ordine non valido!"

#: includes/class-wc-payments-payment-request-button-handler.php:1159
msgid "Invalid request"
msgstr "Richiesta non valida."

#. Translators: %s is the name of the shipping method.
#: includes/class-wc-payments-payment-request-button-handler.php:357
msgid "Shipping (%s)"
msgstr "Spedizione (%s)"

#: client/transactions/list/index.tsx:145
msgid "Channel"
msgstr "Canale"

#: includes/admin/class-wc-rest-user-exists-controller.php:43
msgid "Email address."
msgstr "Indirizzo E-mail:"

#: includes/class-wc-payments-order-service.php:408
msgid "<strong>Fee details:</strong>"
msgstr "<strong>Dettagli sulle tariffe:</strong>"

#: client/transactions/filters/config.ts:131
msgid "Select a customer currency"
msgstr "Seleziona una valuta cliente"

#. Translators: The placeholder is the number of disputes.
#: includes/admin/tasks/class-wc-payments-task-disputes.php:40
msgid "%d disputed payment needs your response"
msgid_plural "%d disputed payments need your response"
msgstr[0] "%d pagamento contestato necessita della tua risposta"
msgstr[1] "%d pagamenti contestati necessitano della tua risposta"

#: includes/class-wc-payments-apple-pay-registration.php:406
msgid "<a>Learn more</a>."
msgstr "<a>Scopri di più</a>."

#. translators: a: Link to the logs page
#: includes/class-wc-payments-apple-pay-registration.php:399
msgid "Please check the <a>logs</a> for more details on this issue. Debug log must be enabled under <strong>Advanced settings</strong> to see recorded logs."
msgstr "Controlla i <a>log</a> per ulteriori dettagli su questo problema. Il log di debug deve essere abilitato in <strong>Impostazioni avanzate</strong> per visualizzare i log registrati."

#: includes/class-wc-payments-apple-pay-registration.php:394
msgid "Express checkouts:"
msgstr "Pagamenti rapidi:"

#: client/settings/express-checkout-settings/file-upload.tsx:146
msgid "Use a custom logo to WooPay if the one taken from your store doesn’t look right. For best results, upload a high-resolution horizontal image with white or transparent background."
msgstr "Usa un logo personalizzato per WooPay se quello preso dal tuo negozio non sembra giusto. Per risultati migliori, carica un'immagine orizzontale ad alta risoluzione con sfondo bianco o trasparente."

#: client/settings/express-checkout-settings/file-upload.tsx:144
msgid "Replace"
msgstr "Sostituisci"

#: client/settings/express-checkout-settings/file-upload.tsx:139
msgid "Upload custom logo"
msgstr "Carica logo personalizzato"

#. translators: %1$s: <h3> tag, %2$s: </h3> tag, %3$s: <p> tag, %4$s: <em> tag,
#. %5$s: </em> tag, %6$s: <em> tag, %7$s: </em> tag, %8$s: </p> tag.
#: includes/subscriptions/class-wc-payments-subscriptions-onboarding-handler.php:257
msgctxt "used in admin pointer script params in javascript as type pointer content"
msgid "%1$sChoose Subscription%2$s%3$sWooCommerce Payments adds two new subscription product types - %4$sSimple subscription%5$s and %6$sVariable subscription%7$s.%8$s"
msgstr "%1$sScegli l'abbonamento %2$s%3$sWooCommerce Payments aggiunge due nuovi tipi di prodotti in abbonamento: %4$sabbonamento semplice%5$s e %6$sabbonamento variabile%7$s.%8$s"

#: includes/admin/class-wc-rest-payments-settings-controller.php:213
msgid "Store logo to display to WooPay customers."
msgstr "Logo del negozio da mostrare ai clienti WooPay."

#: includes/class-wc-payments-order-success-page.php:55
msgid "Payment method:"
msgstr "Metodo di pagamento:"

#: includes/class-wc-payments-order-success-page.php:38
msgid "Thank you! We’ve received your order."
msgstr "Grazie! Abbiamo ricevuto il tuo ordine."

#: includes/admin/class-wc-payments-admin.php:1043
#: includes/class-wc-payments-order-success-page.php:61
msgid "Card ending in"
msgstr "Carta in scadenza tra"

#: includes/admin/class-wc-payments-admin.php:1039
msgid "Paid with"
msgstr "Pagato con"

#: includes/payment-methods/class-link-payment-method.php:27
msgid "Link"
msgstr "Collegamento"

#: includes/class-wc-payments-account.php:1380
msgid "Failed to update Account locale. "
msgstr "Impossibile aggiornare le impostazioni locali dell'account. "

#: includes/class-wc-payment-gateway-wcpay.php:1533
msgid "You shall refund this payment in the same application where the payment was made."
msgstr "Dovrai rimborsare questo pagamento tramite la stessa applicazione da cui è stato effettuato."

#: client/onboarding-experiment/tasks/setup-complete-task/index.tsx:15
msgid "Connect your account and finish setup"
msgstr "Connetti il tuo account e termina l'impostazione"

#: client/onboarding-experiment/tasks/add-business-info-task/index.tsx:63
msgid "What’s the legal structure of your business?"
msgstr "Qual è la struttura giuridica della tua attività?"

#: client/onboarding-experiment/tasks/add-business-info-task/index.tsx:63
msgid "Business Structure"
msgstr "Struttura dell'attività"

#: client/onboarding-experiment/tasks/add-business-info-task/index.tsx:57
msgid "What type of business do you run?"
msgstr "Quale tipo di attività hai?"

#: client/onboarding-experiment/tasks/add-business-info-task/index.tsx:57
msgid "Business type"
msgstr "Tipo di attività"

#: woocommerce-payments.php:278
msgid "<strong>Notice:</strong> It appears that your 'wp-config.php' file might be using dynamic site URL values. Dynamic site URLs could cause WooCommerce Payments to enter Safe Mode. <dynamicSiteUrlSupportLink>Learn how to set a static site URL.</dynamicSiteUrlSupportLink>"
msgstr "<strong>Nota:</strong> sembra che valori URL dinamici del sito possano essere in uso nel tuo file \"wp-config.php\". Gli URL del sito dinamico potrebbero far sì che WooCommerce Payments entri in Modalità sicura. <dynamicSiteUrlSupportLink>Scopri come impostare un URL del sito statico.</dynamicSiteUrlSupportLink>"

#: includes/admin/class-wc-rest-payments-settings-controller.php:208
msgid "Custom message to display to WooPay customers."
msgstr "Messaggio personalizzato da mostrare ai clienti di WooPay."

#: includes/admin/class-wc-rest-payments-settings-controller.php:203
msgid "If WooPay should be enabled."
msgstr "Se WooPay deve essere attivato."

#: includes/admin/class-wc-rest-payments-onboarding-controller.php:156
msgid "Country or type parameter was missing"
msgstr "Il parametro del Paese o del tipo non era presente"

#: includes/admin/class-wc-payments-admin.php:327
msgid "Onboarding"
msgstr "Avviamento"

#: client/data/payment-intents/resolvers.ts:57
msgid "Error retrieving transaction."
msgstr "Errore durante il recupero della transazione."

#: templates/emails/email-ipp-receipt-compliance-details.php:33
#: templates/emails/plain/email-ipp-receipt-compliance-details.php:21
msgid "Application Name"
msgstr "Nome applicazione"

#: templates/emails/email-ipp-receipt-compliance-details.php:25
#: templates/emails/plain/email-ipp-receipt-compliance-details.php:19
msgid "Payment Method"
msgstr "Metodo di pagamento"

#. translators: %s: Order number
#: templates/emails/customer-ipp-receipt.php:26
#: templates/emails/plain/customer-ipp-receipt.php:26
msgid "This is the receipt for your order #%s:"
msgstr "Ecco la ricevuta per il tuo ordine #%s:"

#. translators: %s: Customer first name
#: templates/emails/customer-ipp-receipt.php:24
#: templates/emails/plain/customer-ipp-receipt.php:24
msgid "Hi %s,"
msgstr ""
"Ciao\n"
" %s,"

#: includes/emails/class-wc-payments-email-ipp-receipt.php:247
msgid "Thanks for using {site_url}!"
msgstr "Grazie per aver usato {site_url}."

#: includes/emails/class-wc-payments-email-ipp-receipt.php:92
msgid "Your receipt for order: #{order_number}"
msgstr "La ricevuta per l'ordine: #{order_number}"

#: includes/emails/class-wc-payments-email-ipp-receipt.php:82
msgid "Your {site_title} Receipt"
msgstr "La tua {site_title} ricevuta"

#: includes/emails/class-wc-payments-email-ipp-receipt.php:47
msgid "New receipt emails are sent to customers when a new order is paid for with a card reader."
msgstr "Le nuove e-mail di ricevuta vengono inviate ai clienti quando si effettua il pagamento di un nuovo ordine con un lettore di carte."

#: includes/emails/class-wc-payments-email-ipp-receipt.php:46
msgid "New receipt"
msgstr "Nuova ricevuta"

#. translators: %1: the successfully charged amount, %2: refund id, %3: reason
#: includes/class-wc-payment-gateway-wcpay.php:1582
msgid "A refund of %1$s was successfully processed using WooCommerce Payments. Reason: %2$s. (<code>%3$s</code>)"
msgstr "Un rimborso di %1$s è stato elaborato correttamente usando WooCommerce Payments. Motivo: %2$s (<code>%3$s</code>)."

#: includes/in-person-payments/templates/html-in-person-payment-receipt.php:176
msgid "Shipping:"
msgstr "Spedizione:"

#: includes/in-person-payments/templates/html-in-person-payment-receipt.php:168
msgid "Fees:"
msgstr "Tariffe:"

#. translators: %1: The document ID. %2: The error message.
#: includes/admin/class-wc-rest-payments-documents-controller.php:93
msgid "There was an error accessing document %1$s. %2$s"
msgstr "Si è verificato un errore durante l'accesso al documento %1$s. %2$s"

#: includes/class-wc-payments-order-service.php:1416
msgid "Intent id was not included for payment complete status change."
msgstr "L'ID intento non è stato incluso per la modifica dello stato completo del pagamento."

#. translators: %1: the authorized amount, %2: transaction ID of the payment
#: includes/class-wc-payments-order-service.php:1082
msgid "A capture of %1$s <strong>failed</strong> to complete using WooCommerce Payments (<a>%2$s</a>)."
msgstr "Acquisizione di %1$s <strong>non riuscita</strong> usando WooCommerce Payments (<a>%2$s</a>)."

#: includes/class-woopay-tracker.php:82
msgid "No valid event name or type."
msgstr "Nessun nome o tipo di evento valido."

#: includes/class-wc-payments-woopay-button-handler.php:182
#: includes/class-wc-payments.php:1402 includes/class-wc-payments.php:1513
#: includes/class-woopay-tracker.php:75
msgid "You aren’t authorized to do that."
msgstr "Non sei autorizzato a fare ciò."

#: includes/notes/class-wc-payments-notes-loan-approved.php:75
msgid "View loan details"
msgstr "Vedi i dettagli del prestito"

#. Translators: %1: total amount lent to the merchant formatted in the account
#. currency.
#: includes/notes/class-wc-payments-notes-loan-approved.php:50
msgid "Congratulations! Your capital loan has been approved and %1$s was deposited in to the bank account linked to WooCommerce Payments. You'll automatically repay the loan, plus a flat fee, through a fixed percentage of each WooCommerce Payments transaction."
msgstr "Complimenti! Il tuo prestito di capitale è stato approvato e questo importo è stato depositato sul conto bancario collegato a WooCommerce Payments: %1$s. Ripagherai automaticamente il prestito, più un prezzo forfettario, tramite una percentuale fissa di ogni transazione WooCommerce Payments."

#: includes/notes/class-wc-payments-notes-loan-approved.php:46
msgid "Your capital loan has been approved!"
msgstr "Il tuo prestito di capitale è stato approvato."

#: includes/in-person-payments/templates/html-in-person-payment-receipt.php:217
msgid "Powered by WooCommerce"
msgstr "Con tecnologia WooCommerce"

#: includes/admin/class-wc-rest-payments-files-controller.php:83
msgid "Sorry, you are not allowed to do that."
msgstr "Non hai i permessi per farlo."

#: client/transactions/strings.ts:18
msgid "Loan repayment"
msgstr "Restituzione del prestito"

#: client/data/capital/resolvers.ts:93
msgid "Error retrieving the active loan summary."
msgstr "Errore durante il recupero del riepilogo del prestito attivo"

#: client/components/active-loan-summary/index.tsx:90
msgid "First paydown"
msgstr "Primo pagamento"

#: client/components/active-loan-summary/index.tsx:87
msgid "Withhold rate"
msgstr "Aliquota della ritenuta"

#: client/components/active-loan-summary/index.tsx:86
msgid "Fixed fee"
msgstr "Commissione fissa"

#: client/connect-account-page/strings.tsx:66
#: client/settings/phone-input/index.tsx:93
msgid "Mobile phone number"
msgstr "Numero di cellulare"

#: client/connect-account-page/strings.tsx:65
msgid "Email address"
msgstr "Indirizzo e-mail"

#: includes/class-wc-payments.php:1556
msgid "Contact information"
msgstr "Informazioni di contatto"

#: includes/admin/class-wc-rest-payments-settings-controller.php:327
msgid "Error: Invalid address format!"
msgstr "Errore: formato dell'indirizzo non valido."

#: includes/admin/class-wc-rest-payments-settings-controller.php:301
msgid "Error: Invalid phone number: "
msgstr "Errore: numero di telefono non valido: "

#: includes/admin/class-wc-rest-payments-settings-controller.php:277
msgid "Error: Invalid email address: "
msgstr "Errore: indirizzo e-mail non valido: "

#. translators: %1$s: The current site domain name. %2$s: The original site
#. domain name. Please keep hostname tags in your translation so that they can
#. be formatted properly.
#: woocommerce-payments.php:299
msgid "Create a new connection to WooCommerce Payments for <hostname>%1$s</hostname>. You’ll have to re-verify your business details to begin accepting payments. Your <hostname>%2$s</hostname> connection will remain as is."
msgstr "Crea una nuova connessione a WooCommerce Payments per <hostname>%1$s</hostname>. Devi verificare nuovamente i dettagli dell'attività per iniziare ad accettare pagamenti. La tua connessione a <hostname>%2$s</hostname> rimarrà la stessa."

#. translators: %1$s: The current site domain name. %2$s: The original site
#. domain name. Please keep hostname tags in your translation so that they can
#. be formatted properly.
#: woocommerce-payments.php:289
msgid "Transfer your WooCommerce Payments connection from <hostname>%2$s</hostname> to this site <hostname>%1$s</hostname>. <hostname>%2$s</hostname> will be disconnected from WooCommerce Payments."
msgstr "Trasferisci la tua connessione di WooCommerce Payments da <hostname>%2$s</hostname> a questo sito <hostname>%1$s</hostname>. <hostname>%2$s</hostname> verrà disconnesso da WooCommerce Payments."

#: woocommerce-payments.php:277
msgid "WooCommerce Payments Safe Mode"
msgstr "WooCommerce Payments in Modalità sicura"

#: woocommerce-payments.php:275
msgid "We’ve detected that you have duplicate sites connected to WooCommerce Payments. When Safe Mode is active, payments will not be interrupted. However, some features may not be available until you’ve resolved this issue below. Safe Mode is most frequently activated when you’re transferring your site from one domain to another, or creating a staging site for testing. A site adminstrator can resolve this issue. <safeModeLink>Learn more</safeModeLink>"
msgstr "Abbiamo rilevato che hai duplicato siti connessi a WooCommerce Payments. Quando è attiva la Modalità sicura, i pagamenti non vengono interrotti. Tuttavia, alcune funzionalità potrebbero non essere disponibili finché non viene risolto il problema di seguito. La Modalità sicura viene attivata più frequentemente quando trasferisci il tuo sito da un dominio a un altro o crei un sito di gestione temporanea per la prova. Un amministratore del sito può risolvere questo problema. <safeModeLink>Scopri di più</safeModeLink>"

#: woocommerce-payments.php:272 woocommerce-payments.php:273
msgid "Create a new connection"
msgstr "Crea una nuova connessione"

#: woocommerce-payments.php:271
msgid "Transfer your connection"
msgstr "Trasferisci la tua connessione"

#: woocommerce-payments.php:270
msgid "Transfer connection"
msgstr "Trasferisci la connessione"

#: woocommerce-payments.php:269
msgid "Safe Mode has been deactivated and WooCommerce Payments is fully functional."
msgstr "La Modalità sicura è stata disattivata e WooCommerce Payments è completamente funzionante."

#: woocommerce-payments.php:268
msgid "WooCommerce Payments connection successfully transferred"
msgstr "Connessione a WooCommerce Payments trasferita correttamente"

#: woocommerce-payments.php:267
msgid "We’ve detected that you have duplicate sites connected to WooCommerce Payments. When Safe Mode is active, payments will not be interrupted. However, some features may not be available until you’ve resolved this issue below. Safe Mode is most frequently activated when you’re transferring your site from one domain to another, or creating a staging site for testing. <safeModeLink>Learn more</safeModeLink>"
msgstr "Abbiamo rilevato che hai duplicato siti connessi a WooCommerce Payments. Quando è attiva la Modalità sicura, i pagamenti non vengono interrotti. Tuttavia, alcune funzionalità potrebbero non essere disponibili finché non viene risolto il problema di seguito. La Modalità sicura viene attivata più frequentemente quando trasferisci il tuo sito da un dominio a un altro o crei un sito di gestione temporanea per la prova. <safeModeLink>Scopri di più</safeModeLink>"

#: woocommerce-payments.php:266 woocommerce-payments.php:274
msgid "Safe Mode activated"
msgstr "Modalità sicura attivata"

#: woocommerce-payments.php:265
msgid "Safe Mode"
msgstr "Modalità sicura"

#: client/disputes/filters/config.ts:88
msgid "Select a dispute date"
msgstr "Seleziona una data per il ricorso"

#: client/disputes/filters/config.ts:85
msgid "Select a dispute date filter match"
msgstr "Seleziona una corrispondenza del filtro della data del ricorso"

#: client/disputes/filters/config.ts:84
msgid "Remove dispute date filter"
msgstr "Rimuovi il filtro delle date del ricorso"

#: client/disputes/filters/config.ts:83
msgid "Disputed on date"
msgstr "Data del ricorso"

#: client/disputes/filters/config.ts:79
msgid "Disputes match {{select /}} filters"
msgstr "I ricorsi corrispondono a {{select /}} filtri"

#: client/disputes/filters/config.ts:66
msgid "All disputes"
msgstr "Tutti i ricorsi"

#: client/disputes/filters/config.ts:23
msgid "Dispute currency"
msgstr "Valuta del ricorso"

#: client/disputes/filters/config.ts:115
msgid "Select a dispute status"
msgstr "Seleziona uno stato del ricorso"

#: client/disputes/filters/config.ts:112
msgid "Select a dispute status filter match"
msgstr "Seleziona una corrispondenza del filtro dello stato del ricorso"

#: client/disputes/filters/config.ts:111
msgid "Remove dispute status filter"
msgstr "Rimuovi il filtro dello stato del ricorso"

#. Translators: The %1 placeholder is a currency formatted price string
#. ($0.50). The %2 and %3 placeholders are opening and closing strong HTML
#. tags.
#: includes/subscriptions/class-wc-payments-subscription-service.php:410
msgid "There was a problem creating your subscription. %1$s doesn't meet the %2$sminimum recurring amount%3$s this payment method can process."
msgstr "Si è verificato un problema durante la creazione dell'abbonamento. %1$s non soddisfa l'%2$simporto minimo ricorrente%3$s che questo metodo di pagamento può elaborare."

#: includes/class-wc-payments-webhook-processing-service.php:747
msgid "You have exceeded the number of allowed verification attempts."
msgstr "Hai superato il numero di tentativi per la verifica consentiti. "

#: includes/class-wc-payments-webhook-processing-service.php:745
msgid "Microdeposit transfers failed. Please check the account, institution and transit numbers."
msgstr "Trasferimenti di microacconti non riusciti. Controlla il conto, l'istituto e i numeri delle transazioni."

#: includes/class-wc-payments-webhook-processing-service.php:743
msgid "The customer's bank account could not be located."
msgstr "Impossibile individuare il conto bancario del cliente."

#: includes/class-wc-payments-webhook-processing-service.php:741
msgid "The customer's account has insufficient funds to cover this payment."
msgstr "Il conto del cliente non dispone di fondi sufficienti per effettuare questo pagamento."

#: includes/class-wc-payments-webhook-processing-service.php:739
msgid "The customer has notified their bank that this payment was unauthorized."
msgstr "Il cliente ha comunicato alla propria banca che tale pagamento non era autorizzato."

#: includes/class-wc-payments-webhook-processing-service.php:737
msgid "The customer's bank account has been closed."
msgstr "Il conto bancario del cliente è stato chiuso."

#: client/disputes/index.tsx:293 client/transactions/blocked/index.tsx:160
#: client/transactions/list/index.tsx:444
#: client/transactions/risk-review/index.tsx:145
msgid "There was a problem generating your export."
msgstr "Si è verificato un problema durante la generazione dell'esportazione."

#: client/disputes/index.tsx:284 client/transactions/list/index.tsx:435
msgid "Your export will be emailed to %s"
msgstr "La tua esportazione verrà inviata tramite e-mail all'indirizzo %s"

#. translators: %s: the error message.
#: includes/class-wc-payment-gateway-wcpay.php:2996
msgid "Intent creation failed with the following message: %s"
msgstr "Creazione dell'intento non riuscita con il seguente messaggio: %s"

#: client/vat/form/tasks/company-data-task.tsx:97
msgid "Business name"
msgstr "Nome dell'azienda"

#: client/settings/fraud-protection/components/protection-levels/index.tsx:57
msgid "Edit"
msgstr "Modifica"

#: client/card-readers/settings/file-upload.tsx:59
#: client/settings/express-checkout-settings/file-upload.tsx:62
msgid "The file you have attached is exceeding the maximum limit."
msgstr "Il file che hai allegato supera il limite massimo."

#: client/card-readers/list/list-item.tsx:13
msgid "Active"
msgstr "Attiva"

#: includes/in-person-payments/templates/html-in-person-payment-receipt.php:216
#: templates/emails/email-ipp-receipt-compliance-details.php:49
#: templates/emails/plain/email-ipp-receipt-compliance-details.php:25
msgid "Account Type"
msgstr "Tipo di account"

#: includes/in-person-payments/templates/html-in-person-payment-receipt.php:215
#: templates/emails/email-ipp-receipt-compliance-details.php:41
#: templates/emails/plain/email-ipp-receipt-compliance-details.php:23
msgid "AID"
msgstr "ASSISTENZA"

#: includes/in-person-payments/templates/html-in-person-payment-receipt.php:214
msgid "Application name"
msgstr "Nome dell'applicazione"

#: includes/in-person-payments/templates/html-in-person-payment-receipt.php:204
msgid "AMOUNT PAID"
msgstr "IMPORTO PAGATO"

#: includes/in-person-payments/templates/html-in-person-payment-receipt.php:195
msgid "TOTAL"
msgstr "TOTALE"

#: includes/in-person-payments/templates/html-in-person-payment-receipt.php:154
msgid "SUBTOTAL"
msgstr "SUBTOTALE"

#: includes/in-person-payments/templates/html-in-person-payment-receipt.php:143
msgid "SKU"
msgstr "COD"

#: includes/class-wc-payment-gateway-wcpay.php:3259
msgid "Next we’ll ask you to share a few details about your business to create your account."
msgstr "In seguito, ti chiederemo di condividere alcune informazioni sulla tua attività per creare un account."

#: includes/class-wc-payment-gateway-wcpay.php:2390
msgid "Failed to update Stripe account. "
msgstr "Aggiornamento dell'account Stripe non riuscito. "

#: includes/admin/class-wc-rest-payments-settings-controller.php:145
msgid "A CSS hex color value representing the secondary branding color for this account."
msgstr "Un valore del colore esadecimale di CSS che rappresenta il colore del branding secondario per questo account."

#: includes/admin/class-wc-rest-payments-settings-controller.php:141
msgid "A CSS hex color value representing the primary branding color for this account."
msgstr "Un valore del colore esadecimale di CSS che rappresenta il colore del branding principale per questo account."

#: includes/admin/class-wc-rest-payments-settings-controller.php:137
msgid "An icon for the account."
msgstr "Un'icona per l'account."

#: includes/admin/class-wc-rest-payments-settings-controller.php:133
msgid "A logo id for the account that will be used in Checkout"
msgstr "Un ID logo per l'account che verrà usato nel pagamento"

#: includes/admin/class-wc-rest-payments-settings-controller.php:128
msgid "A publicly available phone number to call with support issues."
msgstr "Un numero di telefono disponibile pubblicamente da chiamare in caso di problemi con il supporto."

#: includes/admin/class-wc-rest-payments-settings-controller.php:123
msgid "A publicly available email address for sending support issues to."
msgstr "Un indirizzo e-mail disponibile pubblicamente a cui inviare i problemi con il supporto."

#: includes/admin/class-wc-rest-payments-settings-controller.php:118
msgid "A publicly available mailing address for sending support issues to."
msgstr "Un indirizzo di posta disponibile pubblicamente a cui inviare problemi con il supporto."

#: includes/admin/class-wc-rest-payments-settings-controller.php:114
msgid "The business’s publicly available website."
msgstr "Un sito web disponibile pubblicamente dell'attività."

#: includes/admin/class-wc-rest-payments-settings-controller.php:110
msgid "The customer-facing business name."
msgstr "Il nome dell'attività rivolto al cliente."

#: includes/admin/class-wc-rest-payments-reader-controller.php:278
msgid "Invalid payment intent"
msgstr "Intento di pagamento non valido"

#: includes/admin/class-wc-payments-admin.php:359
msgid "Card Readers"
msgstr "Lettori di carte"

#: includes/admin/class-wc-payments-admin.php:466
msgid "Add new payment methods"
msgstr "Aggiungi nuovi metodi di pagamento"

#: includes/class-wc-payments-utils.php:549
msgid "We couldn’t verify the postal code in your billing address. Make sure the information is current with your card issuing bank and try again."
msgstr "Non è stato possibile verificare il codice postale nell'indirizzo di fatturazione. Verifica che le informazioni siano quelle di uso corrente con la banca che ha emesso la carta e riprova."

#. translators: %s a formatted price.
#: includes/class-wc-payments-utils.php:538
msgid "The selected payment method requires a total amount of at least %s."
msgstr "Il metodo di pagamento selezionato richiede un importo totale di almeno %s."

#: includes/class-wc-payments-dependency-service.php:292
msgid "You have installed a development version of WooCommerce Payments which requires files to be built. From the plugin directory, run <code>npm run build:client</code> to build and minify assets. Alternatively, you can download a pre-built version of the plugin from the <a1>WordPress.org repository</a1> or by visiting the <a2>releases page in the GitHub repository</a2>."
msgstr "Hai installato una versione di sviluppo di WooCommerce Payments che richiede la creazione di file. Dalla directory dei plugin, esegui <code>npm run build:client</code> per creare e minimizzare le risorse. In alternativa, puoi scaricare una versione predefinita del plugin dall'<a1>archivio di WordPress.org</a1> o visitando la <a2>pagina dei rilasci nell'archivio di GitHub</a2>."

#: includes/class-wc-payment-gateway-wcpay.php:810
msgid "We couldn’t verify the postal code in the billing address. If the issue persists, suggest the customer to reach out to the card issuing bank."
msgstr "Non è stato possibile verificare il codice postale nell'indirizzo di fatturazione. Se il problema persiste, suggerisci al cliente di contattare la banca che ha emesso la carta."

#. translators: %1: the failed payment amount, %2: error message
#: includes/class-wc-payment-gateway-wcpay.php:805
msgid "A payment of %1$s <strong>failed</strong>. %2$s"
msgstr "Un pagamento di  %1$s <strong>non è riuscito</strong>. %2$s"

#. translators: %1: the dispute message, %2: the dispute details URL
#: includes/class-wc-payments-webhook-processing-service.php:615
msgid "%1$s. See <a href=\"%2$s\">dispute overview</a> for more details."
msgstr "%1$s. Consulta la <a href=\"%2$s\">panoramica relativa al ricorso</a> per ulteriori dettagli."

#: includes/class-wc-payments-webhook-processing-service.php:610
msgid "Payment dispute has been updated"
msgstr "Il ricorso del pagamento è stato aggiornato"

#: includes/class-wc-payments-webhook-processing-service.php:607
msgid "Payment dispute funds have been reinstated"
msgstr "I fondi per il ricorso del pagamento sono stati ripristinati"

#: includes/class-wc-payments-webhook-processing-service.php:604
msgid "Payment dispute funds have been withdrawn"
msgstr "I fondi per il ricorso del pagamento sono stati ritirati"

#: includes/class-wc-payments-order-service.php:322
msgid "Dispute lost."
msgstr "Ricorso perso."

#. translators: %1: the dispute status
#: includes/class-wc-payments-order-service.php:1244
msgid "Payment dispute has been closed with status %1$s. See <a>dispute overview</a> for more details."
msgstr "Il ricorso del pagamento è stato chiuso con lo stato %1$s. Consulta la <a>panoramica relativa al ricorso</a> per ulteriori dettagli."

#. translators: %1: the dispute reason
#: includes/class-wc-payments-order-service.php:1222
msgid "Payment has been disputed as %1$s. See <a>dispute overview</a> for more details."
msgstr "È stato effettuato il ricorso del pagamento poiché %1$s. Consulta la <a>panoramica relativa al ricorso</a> per ulteriori dettagli."

#: includes/admin/class-wc-rest-payments-orders-controller.php:175
msgid "WooCommerce In-Person Payments"
msgstr "WooCommerce In-Person Payments"

#: includes/class-wc-payments-status.php:91
msgid "Account ID"
msgstr "ID account"

#: includes/class-wc-payments-status.php:86
msgid "Blog ID"
msgstr "ID del blog"

#: includes/class-wc-payments-status.php:83
msgid "No"
msgstr "No"

#: includes/class-wc-payments-status.php:83
msgid "Yes"
msgstr "Sì"

#: includes/class-wc-payments-status.php:81
msgid "Connected to WPCOM"
msgstr "Connesso a WPCOM"

#: includes/class-wc-payments-status.php:76
msgid "Version"
msgstr "Versione"

#: client/transactions/list/index.tsx:338
msgid "N/A"
msgstr "N/A"

#. translators: %1$s Opening strong tag, %2$s Closing strong tag
#: includes/subscriptions/class-wc-payments-product-service.php:522
msgid "%1$sThere was an issue saving your variations!%2$s A subscription product's billing period cannot be longer than one year. We have updated one or more of this product's variations to renew every %3$s."
msgstr "%1$sSi è verificato un problema durante l'uso delle varianti.%2$s Il periodo di fatturazione di un prodotto in abbonamento non può essere superiore a un anno. Abbiamo aggiornato uno o più delle varianti di questo prodotto per il rinnovo ogni %3$s."

#. translators: %1$s Opening strong tag, %2$s Closing strong tag, %3$s The
#. subscription renewal interval (every x time)
#: includes/subscriptions/class-wc-payments-product-service.php:477
msgid "%1$sThere was an issue saving your product!%2$s A subscription product's billing period cannot be longer than one year. We have updated this product to renew every %3$s."
msgstr "%1$sSi è verificato un problema durante il salvataggio del prodotto.%2$s Il periodo di fatturazione di un prodotto in abbonamento non può essere superiore a un anno. Abbiamo aggiornato questo prodotto per il rinnovo ogni %3$s."

#: includes/subscriptions/class-wc-payments-subscription-service.php:682
msgid "WooCommerce Payments Subscription ID"
msgstr "ID abbonamento di WooCommerce Payments."

#: client/payment-methods/activation-modal.tsx:36
msgid "You need to provide more information to enable %s on your checkout."
msgstr "Devi fornire ulteriori informazioni per attivare %s durante il tuo pagamento."

#: client/payment-methods/activation-modal.tsx:32
msgid "You need to provide more information to enable %s on your checkout:"
msgstr "Devi fornire ulteriori informazioni per attivare %s durante il tuo pagamento:"

#: client/payment-methods/activation-modal.tsx:27
msgid "One more step to enable %s"
msgstr "Ancora un passaggio per attivare %s"

#: client/payment-methods/activation-modal.tsx:38
msgid "If you choose to continue, our payment partner Stripe will send an e-mail to {{merchantEmail /}} to collect the required information"
msgstr "Se scegli di continuare, il nostro partner di pagamento Stripe invierà una e-mail a {{merchantEmail /}} per ricevere le informazioni richieste"

#: includes/admin/class-wc-rest-payments-settings-controller.php:95
msgid "WooCommerce Payments Multi-Currency feature flag setting."
msgstr "Impostazione del contrassegno della funzionalità della multivaluta di WooCommerce Payments."

#: includes/subscriptions/class-wc-payments-invoice-service.php:299
msgid "The payment info couldn't be added to the order."
msgstr "Impossibile aggiungere le informazioni di pagamento all'ordine. "

#: includes/subscriptions/templates/html-subscriptions-plugin-notice.php:47
msgid "Yes, deactivate WooCommerce Subscriptions"
msgstr "Sì, disattiva WooCommerce Subscriptions"

#: includes/subscriptions/templates/html-subscriptions-plugin-notice.php:42
msgid "Are you sure you want to deactivate WooCommerce Subscriptions?"
msgstr "Desideri disattivare WooCommerce Subscriptions?"

#: includes/subscriptions/templates/html-subscriptions-plugin-notice.php:14
#: includes/subscriptions/templates/html-wcpay-deactivate-warning.php:14
msgid "Are you sure?"
msgstr "Sei sicuro?"

#: includes/class-wc-payment-gateway-wcpay.php:1054
msgid "Credit / Debit Card"
msgstr "Carta di credito/debito"

#: includes/admin/class-wc-rest-payments-orders-controller.php:153
#: includes/admin/class-wc-rest-payments-orders-controller.php:253
msgid "Payment cannot be captured for partially or fully refunded orders."
msgstr "Il pagamento non può essere acquisito per ordini parzialmente o completamente rimborsati."

#: includes/wc-payment-api/class-wc-payments-api-client.php:1102
msgid "Price ID is required"
msgstr "È richiesto l'ID del prezzo"

#: includes/wc-payment-api/class-wc-payments-api-client.php:1078
msgid "Product ID is required"
msgstr "È richiesto l'ID del prodotto"

#. Translators: %s Property name not found in event data array.
#: includes/subscriptions/class-wc-payments-subscriptions-event-handler.php:269
msgid "%s not found in array"
msgstr "%s non trovato nella matrice"

#. Translators: %d Number of failed renewal attempts.
#: includes/subscriptions/class-wc-payments-subscriptions-event-handler.php:238
msgid "WCPay subscription renewal attempt %d failed."
msgid_plural "WCPay subscription renewal attempt %d failed."
msgstr[0] "%d tentativo/i di rinnovo dell'abbonamento WCPay non riuscito/i."
msgstr[1] ""

#: includes/subscriptions/class-wc-payments-subscriptions-event-handler.php:230
msgid "Unable to generate renewal order for subscription to record the incoming \"invoice.payment_failed\" event."
msgstr "Impossibile generare l'ordine di rinnovo per l'abbonamento per registrare l'evento \"invoice.payment_failed\" in entrata."

#: includes/subscriptions/class-wc-payments-subscriptions-event-handler.php:221
msgid "Cannot find subscription for the incoming \"invoice.payment_failed\" event."
msgstr "Impossibile trovare l'abbonamento per l'evento \"invoice.payment_failed\" in entrata."

#: includes/subscriptions/class-wc-payments-subscriptions-event-handler.php:149
msgid "Unable to generate renewal order for subscription on the \"invoice.paid\" event."
msgstr "Impossibile generare l'ordine di rinnovo per l'abbonamento sull'evento \"invoice.paid\"."

#: includes/subscriptions/class-wc-payments-subscriptions-event-handler.php:135
msgid "Cannot find subscription for the incoming \"invoice.paid\" event."
msgstr "Impossibile trovare l'abbonamento per l'evento \"invoice.paid\" in entrata."

#. Translators: %s Scheduled/upcoming payment date in Y-m-d H:i:s format.
#: includes/subscriptions/class-wc-payments-subscriptions-event-handler.php:99
msgid "Next automatic payment scheduled for %s."
msgstr "Prossimo pagamento automatico previsto: %s."

#: includes/subscriptions/class-wc-payments-subscriptions-event-handler.php:76
msgid "Cannot find subscription to handle the \"invoice.upcoming\" event."
msgstr "Impossibile trovare l'abbonamento per gestire l'evento \"invoice.upcoming\"."

#. Translators: %s Stripe subscription item ID.
#: includes/subscriptions/class-wc-payments-subscription-service.php:927
msgid "Unable to set subscription item ID meta for WCPay subscription item %s."
msgstr "Impossibile impostare il meta ID dell'elemento dell'abbonamento per l'elemento dell'abbonamento WCPay %s."

#: includes/subscriptions/class-wc-payments-subscription-service.php:703
msgid "The subscription's next payment date has been updated to match WCPay server."
msgstr "La data di pagamento successiva dell'abbonamento è stata aggiornata in modo che corrisponda al server WCPay."

#: includes/subscriptions/class-wc-payments-subscription-service.php:625
msgid "We've successfully collected payment for your subscription using your new payment method."
msgstr "Abbiamo ricevuto correttamente il pagamento per il tuo abbonamento usando il tuo nuovo metodo di pagamento."

#: includes/subscriptions/class-wc-payments-subscription-service.php:379
msgid "There was a problem creating your subscription. Please try again or contact us for assistance."
msgstr "Si è verificato un problema durante la creazione dell'abbonamento. Prova di nuovo o contattaci per ricevere supporto."

#. Translators: %s Coupon code.
#: includes/subscriptions/class-wc-payments-subscription-service.php:329
msgid "Coupon - %s"
msgstr "Codice promozionale: %s"

#: includes/subscriptions/class-wc-payments-subscription-change-payment-method-handler.php:219
msgid "Update and retry payment"
msgstr "Aggiorna e riprova a pagare"

#: includes/subscriptions/class-wc-payments-subscription-change-payment-method-handler.php:168
msgid "Your subscription's last renewal failed payment. Please update your payment details so we can reattempt payment."
msgstr "Il pagamento dell'ultimo rinnovo dell'abbonamento non è riuscito. Aggiorna i tuoi dettagli di pagamento in modo da poterlo ritentare."

#: includes/subscriptions/class-wc-payments-subscription-change-payment-method-handler.php:152
msgid "Update payment details"
msgstr "Aggiorna i dettagli di pagamento"

#: includes/subscriptions/class-wc-payments-subscription-change-payment-method-handler.php:49
msgid "Update payment method"
msgstr "Aggiorna il metodo di pagamento"

#: includes/subscriptions/class-wc-payments-invoice-service.php:361
msgid "The WCPay invoice items do not match WC subscription items."
msgstr "Gli elementi della fattura WCPay non corrispondono a quelli dell'abbonamento WC"

#. translators: a1: link to the Plugins page, a2: link to the page having all
#. previous versions
#: includes/class-wc-payments-dependency-service.php:238
msgid "<a1>Update WooCommerce</a1> <strong>(recommended)</strong> or manually re-install <a2>a previous version</a2> of WooCommerce Payments."
msgstr "<a1>Aggiorna WooCommerce</a1> <strong>(consigliato)</strong> o reinstalla manualmente <a2>una versione precedente</a2> di WooCommerce Payments."

#: includes/multi-currency/CurrencySwitcherWidget.php:50
msgid "Currency Switcher Widget"
msgstr "Widget del pulsante di cambio della valuta"

#: client/components/deposits-status/index.tsx:57
msgid "Manual"
msgstr "Manuale"

#: includes/admin/class-wc-rest-payments-settings-controller.php:100
msgid "WooCommerce Payments Subscriptions feature flag setting."
msgstr "Impostazione del contrassegno della funzionalità Abbonamenti di WooCommerce Payments."

#: includes/wc-payment-api/class-wc-payments-api-client.php:1454
msgid "Address country and line 1 are required."
msgstr "Il Paese dell'indirizzo e la riga 1 sono richiesti."

#: includes/class-wc-payments-utils.php:545
#: includes/class-wc-payments-utils.php:547
msgid "We're not able to process this request. Please refresh the page and try again."
msgstr "Impossibile elaborare questa richiesta. Aggiorna la pagina e riprova."

#: includes/class-wc-payments-account.php:1017
msgid "There was a duplicate attempt to initiate account setup. Please wait a few seconds and try again."
msgstr "Si è verificato un tentativo duplicato di avviare la configurazione dell'account. Attendi qualche secondo e riprova."

#. translators: %1: the failed payment amount
#: includes/class-wc-payment-gateway-wcpay.php:838
msgid "A payment of %1$s <strong>failed</strong> to complete because of too many failed transactions. A rate limiter was enabled for the user to prevent more attempts temporarily."
msgstr "Un pagamento di %1$s <strong>non è stato completato</strong> a causa di troppe transazioni non riuscite. È stato attivato un limitatore di tariffa per consentire all'utente di prevenire temporaneamente più tentativi."

#: includes/class-wc-payment-gateway-wcpay.php:736
#: includes/payment-methods/class-upe-payment-gateway.php:505
msgid "Your payment was not processed."
msgstr "Il pagamento non è stato elaborato."

#: includes/admin/class-wc-rest-payments-orders-controller.php:333
msgid "Invalid order status"
msgstr "Stato ordine non valido"

#: includes/notes/class-wc-payments-notes-additional-payment-methods.php:109
msgid "We detected a temporary issue with your account. Please try and connect your Stripe account."
msgstr "Abbiamo rilevato un problema temporaneo con il tuo account. Prova a collegare il tuo account Stripe."

#: includes/multi-currency/SettingsOnboardCta.php:60
msgid "Get started"
msgstr "Inizia ora"

#: includes/multi-currency/SettingsOnboardCta.php:57
msgid "To add new currencies to your store, please finish setting up WooCommerce Payments."
msgstr "Per aggiungere nuove valute al tuo negozio, completa l'impostazione di WooCommerce Payments."

#: client/transactions/filters/config.ts:126
msgid "Customer currency"
msgstr "Valuta cliente"

#: includes/payment-methods/class-upe-payment-gateway.php:104
msgid "Popular payment methods"
msgstr "Metodi di pagamento popolari"

#: includes/payment-methods/class-upe-split-payment-gateway.php:82
msgid "Payments made simple, with no monthly fees - designed exclusively for WooCommerce stores. Accept credit cards, debit cards, and other popular payment methods."
msgstr "Pagamenti semplificati, senza commissioni mensili, progettati esclusivamente per i negozi WooCommerce. Accetta carte di credito, carte di debito e altri metodi di pagamento popolari."

#: includes/class-wc-payment-gateway-wcpay.php:1505
msgid "The refund amount is not valid."
msgstr "L'importo del rimborso non è valido."

#: includes/admin/class-wc-payments-admin.php:190
msgid "The selected currency is not available for the country set in your WooCommerce Payments account."
msgstr "La valuta selezionata non è disponibile per il Paese impostato sul tuo account WooCommerce Payments."

#: includes/admin/class-wc-payments-admin.php:187
#: includes/admin/class-wc-payments-admin.php:212
msgid "Unsupported currency:"
msgstr "Valuta non supportata:"

#. translators: %1$s: name of the blog, %2$s: link to payment re-authentication
#. URL, note: no full stop due to url at the end.
#. translators: %1$s: name of the blog, %2$s: link to checkout payment url,
#. note: no full stop due to url at the end.
#: includes/compat/subscriptions/emails/failed-renewal-authentication.php:18
#: includes/compat/subscriptions/emails/plain/failed-renewal-authentication.php:17
msgctxt "In failed renewal authentication email"
msgid "The automatic payment to renew your subscription with %1$s has failed. To reactivate the subscription, please login and authorize the renewal from your account page: %2$s"
msgstr "Il pagamento automatico per rinnovare l'abbonamento con %1$s non è riuscito. Per riattivare l'abbonamento, accedi e autorizza il rinnovo dalla pagina del tuo ccount: %2$s"

#. translators: %1$s: an order number, %2$s: the customer's full name, %3$s:
#. lowercase human time diff in the form returned by wcs_get_human_time_diff(),
#. e.g. 'in 12 hours'.
#: includes/compat/subscriptions/emails/failed-renewal-authentication-requested.php:22
#: includes/compat/subscriptions/emails/plain/failed-renewal-authentication-requested.php:18
msgctxt "In admin renewal failed email"
msgid "The automatic recurring payment for order %1$s from %2$s has failed. The customer was sent an email requesting authentication of payment. If the customer does not authenticate the payment, they will be requested by email again %3$s."
msgstr "Il pagamento ricorrente automatico per l'ordine %1$s di %2$s non è riuscito. Al cliente è stata inviata un'e-mail con richiesta di autenticazione del pagamento. Se il cliente non effettua l'autenticazione del pagamento, gli verrà nuovamente richiesto per e-mail %3$s."

#: includes/compat/subscriptions/class-wc-payments-email-failed-renewal-authentication.php:116
msgctxt "an email notification"
msgid "Enable/disable"
msgstr "Abilita/Disabilita"

#: includes/payment-methods/class-upe-payment-gateway.php:1016
msgid "Unable to update UPE appearance values at this time."
msgstr "Impossibile aggiornare i valori UPE dell'aspetto in questo momento."

#: client/components/fraud-risk-tools-banner/components/banner-actions/index.tsx:18
#: client/overview/connection-sucess-notice.tsx:18
msgid "Dismiss"
msgstr "Ignora"

#. translators: %1 User's country, %2 Selected currency name, %3 Default store
#. currency name
#: includes/multi-currency/MultiCurrency.php:810
msgid "We noticed you're visiting from %1$s. We've updated our prices to %2$s for your shopping convenience. <a>Use %3$s instead.</a>"
msgstr "Abbiamo notato che hai iniziato la visita da questo Paese: %1$s. Riportiamo i prezzi in %2$s per tua comodità. <a>Usa invece questa valuta: %3$s.</a>"

#: includes/compat/subscriptions/trait-wc-payment-gateway-wcpay-subscriptions.php:262
msgid ""
"Almost there!\n"
"\n"
"Your order has already been created, the only thing that still needs to be done is for you to authorize the payment with your bank."
msgstr ""
"Ci sei quasi!\n"
"\n"
"L'ordine è già stato creato. L'unica cosa che deve ancora essere fatta è autorizzare il pagamento con la banca."

#: includes/compat/subscriptions/emails/failed-renewal-authentication.php:18
msgid "Authorize the payment &raquo;"
msgstr "Autorizza il pagamento »"

#: includes/compat/subscriptions/emails/failed-renewal-authentication-requested.php:34
#: includes/compat/subscriptions/emails/plain/failed-renewal-authentication-requested.php:27
msgid "The renewal order is as follows:"
msgstr "L'ordine di rinnovo è il seguente:"

#: includes/compat/subscriptions/class-wc-payments-email-failed-renewal-authentication.php:143
msgid "Payment authorization needed for renewal of order {order_number}"
msgstr "Autorizzazione per il pagamento necessaria per il rinnovo di {order_number}"

#: includes/compat/subscriptions/class-wc-payments-email-failed-renewal-authentication.php:134
msgid "Payment authorization needed for renewal of {site_title} order {order_number}"
msgstr "Autorizzazione per il pagamento necessaria per il rinnovo dell’ordine {order_number} relativo a {site_title}"

#: includes/compat/subscriptions/class-wc-payments-email-failed-renewal-authentication.php:118
msgid "Enable this email notification"
msgstr "Abilita questa notifica email"

#: includes/compat/subscriptions/class-wc-payments-email-failed-renewal-authentication.php:33
msgid "Sent to a customer when a renewal fails because the transaction requires an SCA verification. The email contains renewal order information and payment links."
msgstr "Invia a un cliente quando un rinnovo non avviene perché la transazione richiede una verifica SCA. L'e-mail contiene informazioni sull'ordine di rinnovo e link per eseguire il pagamento."

#: includes/compat/subscriptions/class-wc-payments-email-failed-renewal-authentication.php:32
msgid "Failed subscription renewal SCA authentication"
msgstr "Autenticazione SCA per il rinnovo dell'abbonamento non riuscita"

#: includes/compat/subscriptions/class-wc-payments-email-failed-authentication-retry.php:30
msgid "[{site_title}] Automatic payment failed for {order_number}. Customer asked to authenticate payment and will be notified again {retry_time}"
msgstr "[{site_title}] Automatic payment failed for {order_number}. Customer asked to authenticate payment and will be notified again {retry_time}"

#: includes/compat/subscriptions/class-wc-payments-email-failed-authentication-retry.php:29
msgid "Automatic renewal payment failed due to authentication required"
msgstr "Mancato pagamento automatico del rinnovo a causa dell'autenticazione richiesta"

#: includes/compat/subscriptions/class-wc-payments-email-failed-authentication-retry.php:27
msgid "Payment authentication requested emails are sent to chosen recipient(s) when an attempt to automatically process a subscription renewal payment fails because the transaction requires an SCA verification, the customer is requested to authenticate the payment, and a retry rule has been applied to notify the customer again within a certain time period."
msgstr "Le e-mail delle richieste di autenticazione del pagamento vengono inviate ai destinatari scelti quando un tentativo di elaborare automaticamente un pagamento di rinnovo dell'abbonamento non va a buon fine perché la transazione richiede una verifica SCA, il cliente deve autenticare il pagamento ed è stata applicata una regola di ripetizione del tentativo di notifica al cliente entro un determinato periodo di tempo."

#: includes/compat/subscriptions/class-wc-payments-email-failed-authentication-retry.php:26
msgid "Payment authentication requested email"
msgstr "E-mail di richiesta di autenticazione del pagamento"

#: includes/class-wc-payments-utils.php:253
msgid "Singapore"
msgstr "Singapore"

#: includes/class-wc-payments-utils.php:249
msgid "Portugal"
msgstr "Portogallo"

#: includes/class-wc-payments-utils.php:248
msgid "Poland"
msgstr "Polonia"

#: includes/class-wc-payments-utils.php:245
msgid "Netherlands"
msgstr "Paesi Bassi"

#: includes/class-wc-payments-utils.php:239
msgid "Hong Kong"
msgstr "Hong Kong"

#: includes/class-wc-payments-utils.php:227
msgid "Switzerland"
msgstr "Svizzera"

#: includes/class-wc-payments-utils.php:224
msgid "Belgium"
msgstr "Belgio"

#: includes/class-wc-payments-utils.php:222
msgid "Austria"
msgstr "Austria"

#: includes/admin/class-wc-rest-payments-settings-controller.php:85
msgid "If WooCommerce Payments \"Saved cards\" should be enabled."
msgstr "Se la funzionalità \"Carte salvate\" di WooCommerce Payments deve essere abilitata."

#: includes/notes/class-wc-payments-notes-additional-payment-methods.php:73
msgid "Enable on your store"
msgstr "Abilita nel tuo negozio"

#: includes/notes/class-wc-payments-notes-additional-payment-methods.php:66
msgid "Get early access to additional payment methods and an improved checkout experience, coming soon to WooCommerce Payments. <a href=\"https://woocommerce.com/document/payments/additional-payment-methods/\" target=\"wcpay_upe_learn_more\">Learn more</a>"
msgstr "Ottieni accesso anticipato a metodi di pagamento aggiuntivi e un'esperienza di pagamento migliorata, presto disponibili su WooCommerce Payments. <a href=\"https://woocommerce.com/document/payments/additional-payment-methods/\" target=\"wcpay_upe_learn_more\">Scopri di più</a>"

#. translators: %s List of currencies that are already translated in
#. WooCommerce core.
#: includes/multi-currency/AdminNotices.php:110
msgid "The store currency was recently changed. The following currencies are set to manual rates and may need updates: %s"
msgstr "La valuta del negozio è stata modificata di recente. Le seguenti valute sono impostate su tariffe manuali e possono essere necessari aggiornamenti: %s"

#: includes/multi-currency/AdminNotices.php:74
msgid "Cheatin&#8217; huh?"
msgstr "Volevi fare il furbo, eh?"

#: includes/multi-currency/AdminNotices.php:70
msgid "Action failed. Please refresh the page and retry."
msgstr "Azione fallita. Aggiorna la pagina e riprova."

#: includes/multi-currency/CurrencySwitcherWidget.php:66
msgid "Display flags in supported devices"
msgstr "Mostra contrassegni sui dispositivi supportati"

#: includes/notes/class-wc-payments-notes-additional-payment-methods.php:65
msgid "Boost your sales by accepting new payment methods"
msgstr "Aumenta le vendite accettando nuovi metodi di pagamento"

#: includes/admin/class-wc-rest-upe-flag-toggle-controller.php:74
msgid "Determines if the UPE feature flag is enabled."
msgstr "Determina se il flag delle funzionalità UPE è abilitato."

#: includes/admin/class-wc-payments-admin.php:669
msgid "Refunding manually requires reimbursing your customer offline via cash, check, etc. The refund amounts entered here will only be used to balance your analytics."
msgstr "Il rimborso manuale richiede che il cliente venga rimborsato offline tramite contanti, assegno, ecc. Gli importi di rimborso inseriti qui saranno utilizzati solo per bilanciare le tue analisi."

#: includes/payment-methods/class-upe-payment-gateway.php:660
msgid "Payment method successfully added."
msgstr "Metodo di pagamento aggiunto correttamente."

#: includes/class-wc-payments-utils.php:254
msgid "United States (US)"
msgstr "Stati Uniti (US)"

#: includes/class-wc-payments-utils.php:247
msgid "New Zealand"
msgstr "Nuova Zelanda"

#: includes/class-wc-payments-utils.php:241
msgid "Italy"
msgstr "Italia"

#: includes/class-wc-payments-utils.php:240
msgid "Ireland"
msgstr "Irlanda"

#: includes/class-wc-payments-utils.php:237
msgid "United Kingdom (UK)"
msgstr "Regno Unito (UK)"

#: includes/class-wc-payments-utils.php:234
msgid "France"
msgstr "Francia"

#: includes/class-wc-payments-utils.php:233
msgid "Spain"
msgstr "Spagna"

#: includes/class-wc-payments-utils.php:229
msgid "Germany"
msgstr "Germania"

#: includes/class-wc-payments-utils.php:226
msgid "Canada"
msgstr "Canada"

#: includes/class-wc-payments-utils.php:223
msgid "Australia"
msgstr "Australia"

#. translators: The text encapsulated in `**` can be replaced with "Apple Pay"
#. or "Google Pay". Please translate this text, but don't remove the `**`.
#: includes/class-wc-payments-payment-request-button-handler.php:1636
msgid "To complete your transaction with **the selected payment method**, you must log in or create an account with our site."
msgstr "Per completare la transazione con **il metodo di pagamento selezionato**, devi accedere o creare un account sul nostro sito."

#: includes/notes/class-wc-payments-notes-instant-deposits-eligible.php:43
msgid "Request an instant deposit"
msgstr "Richiedi un acconto immediato"

#: includes/notes/class-wc-payments-notes-instant-deposits-eligible.php:33
msgid "Get immediate access to your funds when you need them – including nights, weekends, and holidays. With WooCommerce Payments' <a>Instant Deposits feature</a>, you're able to transfer your earnings to a debit card within minutes."
msgstr "Ottieni accesso immediato ai tuoi fondi quando ne hai bisogno, comprese le notti, i fine settimana e i giorni festivi. Con la <a>funzionalità Acconti immediati</a> di WooCommerce Payments, puoi trasferire i tuoi guadagni su una carta di debito in pochi minuti."

#: includes/notes/class-wc-payments-notes-instant-deposits-eligible.php:30
msgid "You’re now eligible to receive Instant Deposits with WooCommerce Payments"
msgstr "Ora sei idoneo a ricevere acconti immediati con WooCommerce Payments"

#: includes/multi-currency/Notes/NoteMultiCurrencyAvailable.php:54
#: includes/notes/class-wc-payments-notes-set-up-stripelink.php:85
msgid "Set up now"
msgstr "Configura ora"

#: includes/multi-currency/Notes/NoteMultiCurrencyAvailable.php:47
msgid "Boost your international sales by allowing your customers to shop and pay in their local currency."
msgstr "Aumenta le vendite internazionali consentendo ai clienti di fare acquisti e pagare con la loro valuta locale."

#: includes/multi-currency/Notes/NoteMultiCurrencyAvailable.php:46
msgid "Sell worldwide in multiple currencies"
msgstr "Vendi in tutto il mondo con molteplici valute"

#: includes/class-wc-payment-gateway-wcpay.php:378
msgid "Large"
msgstr "Grande"

#: includes/class-wc-payment-gateway-wcpay.php:377
msgid "Medium"
msgstr "Medio"

#: includes/class-wc-payment-gateway-wcpay.php:372
msgid "Select the size of the button."
msgstr "Seleziona la dimensione del pulsante."

#: includes/class-wc-payment-gateway-wcpay.php:370
msgid "Size of the button displayed for Express Checkouts"
msgstr "Dimensione del pulsante visualizzata per i Pagamenti rapidi"

#: includes/admin/class-wc-rest-payments-settings-controller.php:194
msgid "1-click checkout button themes."
msgstr "Temi del pulsante per il pagamento con un clic."

#: includes/admin/class-wc-rest-payments-settings-controller.php:184
msgid "1-click checkout button sizes."
msgstr "Dimensioni del pulsante per il pagamento con un clic."

#: includes/admin/class-wc-rest-payments-settings-controller.php:175
msgid "1-click checkout button types."
msgstr "Tipi di pulsante per il pagamento con un clic."

#: includes/multi-currency/Settings.php:46
#: includes/multi-currency/SettingsOnboardCta.php:38
msgctxt "Settings tab label"
msgid "Multi-currency"
msgstr "Multivaluta"

#: includes/multi-currency/UserSettings.php:62
msgid "Select your preferred currency for shopping and payments."
msgstr "Seleziona la valuta preferita per shopping e pagamenti."

#. translators: %s: url to documentation.
#: includes/multi-currency/SettingsOnboardCta.php:78
msgid "Accept payments in multiple currencies. Prices are converted based on exchange rates and rounding rules. <a href=\"%s\">Learn more</a>"
msgstr "Accetta pagamenti in più valute. I prezzi vengono convertiti in base ai tassi di cambio e alle regole di approssimazione. <a href=\"%s\">Approfondisci</a>"

#: includes/multi-currency/CurrencySwitcherWidget.php:61
msgid "Display currency symbols"
msgstr "Mostra i simboli delle valute"

#: includes/multi-currency/CurrencySwitcherWidget.php:56
msgid "Title"
msgstr "Titolo:"

#: includes/multi-currency/CurrencySwitcherWidget.php:51
msgid "Let your customers switch between your enabled currencies"
msgstr "Consenti ai clienti di passare da una valuta abilitata all'altra"

#: includes/class-wc-payment-gateway-wcpay.php:314
msgid "Book"
msgstr "Libro"

#: client/payment-methods/delete-modal.tsx:19
msgid "Remove"
msgstr "Elimina"

#: client/onboarding-prototype/strings.tsx:80
#: client/payment-methods/activation-modal.tsx:29
#: client/vat/form/tasks/vat-number-task.tsx:116
msgid "Continue"
msgstr "Continua"

#: includes/payment-methods/class-cc-payment-method.php:69
msgid "<strong>Test mode:</strong> use the test VISA card **************** with any expiry date and CVC. Other payment methods may redirect to a Stripe test page to authorize payment. More test card numbers are listed <a>here</a>."
msgstr "<strong>Modalità di prova:</strong> usa la carta VISA di prova **************** con qualsiasi data di scadenza e CVC. Altri metodi possono reindirizzare alla pagina di prova di Stripe per l'autorizzazione al pagamento. Ulteriori numeri della carta di credito di prova sono elencati <a>qui</a>."

#. translators: localized exception message
#: includes/payment-methods/class-upe-payment-gateway.php:821
msgid "UPE payment failed: %s"
msgstr "Pagamento UPE non riuscito: %s"

#: includes/admin/class-wc-rest-payments-settings-controller.php:166
#: includes/admin/class-wc-rest-payments-settings-controller.php:218
msgid "Express checkout locations that should be enabled."
msgstr "Posizioni dei pagamenti rapidi che dovrebbero essere abilitate."

#: includes/admin/class-wc-rest-payments-settings-controller.php:161
msgid "If WooCommerce Payments express checkouts should be enabled."
msgstr "Se i pagamenti rapidi di WooCommerce Payments deve essere abilitati."

#: includes/admin/class-wc-rest-payments-settings-controller.php:105
msgid "WooCommerce Payments bank account descriptor to be displayed in customers' bank accounts."
msgstr "Descrittore del conto bancario di WooCommerce Payments da visualizzare nei conti bancari dei clienti. "

#: includes/admin/class-wc-rest-payments-settings-controller.php:90
msgid "WooCommerce Payments test mode setting."
msgstr "Impostazioni della modalità di prova di WooCommerce Payments."

#: includes/admin/class-wc-rest-payments-settings-controller.php:80
msgid "If WooCommerce Payments manual capture of charges should be enabled."
msgstr "Se deve essere abilitata l'acquisizione manuale degli addebiti di WooCommerce Payments."

#: includes/admin/class-wc-rest-payments-settings-controller.php:71
msgid "Payment method IDs that should be enabled. Other methods will be disabled."
msgstr "ID dei metodi di pagamento che dovrebbero essere abilitati. Gli altri metodi verranno disabilitati."

#: includes/admin/class-wc-rest-payments-settings-controller.php:66
msgid "If WooCommerce Payments should be enabled."
msgstr "Se WooCommerce Payments deve essere abilitato."

#: client/deposits/list/index.tsx:191 client/transactions/list/index.tsx:485
#: client/transactions/uncaptured/index.tsx:171
msgid "total"
msgstr "Totale"

#: includes/admin/class-wc-rest-payments-orders-controller.php:227
#: includes/admin/class-wc-rest-payments-orders-controller.php:299
#: includes/admin/class-wc-rest-payments-orders-controller.php:361
#: includes/admin/class-wc-rest-payments-orders-controller.php:402
#: includes/admin/class-wc-rest-payments-orders-controller.php:524
msgid "Unexpected server error"
msgstr "Errore del server imprevisto"

#: includes/admin/class-wc-rest-payments-orders-controller.php:209
#: includes/admin/class-wc-rest-payments-orders-controller.php:283
#: includes/admin/class-wc-rest-payments-orders-controller.php:508
#: includes/class-wc-payment-gateway-wcpay.php:2997
msgid "Unknown error"
msgstr "Errore sconosciuto"

#. translators: %s: the error message.
#: includes/admin/class-wc-rest-payments-orders-controller.php:208
#: includes/admin/class-wc-rest-payments-orders-controller.php:282
msgid "Payment capture failed to complete with the following message: %s"
msgstr "Impossibile completare l'acquisizione del pagamento con il seguente messaggio: %s"

#: includes/admin/class-wc-rest-payments-orders-controller.php:167
#: includes/admin/class-wc-rest-payments-orders-controller.php:170
#: includes/admin/class-wc-rest-payments-orders-controller.php:267
#: includes/admin/class-wc-rest-payments-orders-controller.php:270
msgid "The payment cannot be captured"
msgstr "Impossibile acquisire il pagamento"

#: includes/admin/class-wc-rest-payments-charges-controller.php:79
#: includes/admin/class-wc-rest-payments-orders-controller.php:146
#: includes/admin/class-wc-rest-payments-orders-controller.php:246
#: includes/admin/class-wc-rest-payments-orders-controller.php:320
#: includes/admin/class-wc-rest-payments-orders-controller.php:376
#: includes/admin/class-wc-rest-payments-orders-controller.php:471
#: includes/admin/class-wc-rest-payments-reader-controller.php:289
msgid "Order not found"
msgstr "Ordine non trovato"

#: includes/subscriptions/templates/html-subscriptions-plugin-notice.php:46
#: includes/subscriptions/templates/html-wcpay-deactivate-warning.php:47
#: client/components/cancel-authorization-button/index.tsx:21
#: client/deposits/instant-deposits/modal.tsx:42
#: client/overview/modal/update-business-details/strings.tsx:12
#: client/payment-methods/activation-modal.tsx:28
#: client/payment-methods/delete-modal.tsx:20
msgid "Cancel"
msgstr "Cancella"

#: includes/class-wc-payment-gateway-wcpay.php:231
#: client/payment-methods-map.tsx:33
msgid "Credit card / debit card"
msgstr "Carta di credito/carta di debito"

#: client/components/file-upload/index.tsx:45
#: client/settings/express-checkout-settings/file-upload.tsx:145
msgid "Remove file"
msgstr "Rimuovi file"

#: client/connect-account-page/strings.tsx:9
#: client/empty-state-table/list.tsx:48
#: client/onboarding-experiment/strings.ts:7
#: client/overview/modal/update-business-details/strings.tsx:7
msgid "Finish setup"
msgstr "Completa l'impostazione"

#: client/connect-account-page/strings.tsx:93
msgid "Setup complete!"
msgstr "Impostazione completata."

#: includes/class-wc-payments-token-service.php:261
msgid "SEPA IBAN"
msgstr "IBAN SEPA"

#. translators: last 4 digits of IBAN account
#: includes/class-wc-payment-token-wcpay-sepa.php:53
msgid "SEPA IBAN ending in %s"
msgstr "IBAN SEPA terminante in %s"

#. translators: %1: intent ID
#: includes/class-wc-payments-webhook-processing-service.php:712
msgid "Could not find order via intent ID: %1$s"
msgstr "Impossibile trovare l'ordine tramite l'ID dell'intento: %1$s"

#: client/transactions/strings.ts:13
msgid "Refund failure"
msgstr "Rimborso non riuscito"

#: client/transactions/uncaptured/index.tsx:47
msgid "Order number"
msgstr "Numero dell'ordine"

#: client/transactions/list/index.tsx:127
msgid "Date and time"
msgstr "Data e ora"

#: client/transactions/list/index.tsx:126
#: client/transactions/risk-review/columns.tsx:25
msgid "Date / Time"
msgstr "Data/Ora"

#: client/documents/list/index.tsx:129
msgid "Download"
msgstr "Scarica"

#: client/transactions/uncaptured/index.tsx:52
msgid "Risk level"
msgstr "Livello di rischio"

#: client/documents/filters/config.ts:46
#: client/transactions/filters/config.ts:103
msgid "{{title}}Date{{/title}} {{rule /}} {{filter /}}"
msgstr "{{title}}Data{{/title}} {{rule /}} {{filter /}}"

#: client/documents/filters/config.ts:29
#: client/transactions/filters/config.ts:81
msgid "Advanced filters"
msgstr "Filtri avanzati"

#: client/documents/filters/config.ts:19
#: client/transactions/filters/config.ts:64
msgid "Show"
msgstr "Visualizza"

#: client/transactions/filters/config.ts:57
msgid "All currencies"
msgstr "Tutte le valute"

#: client/transactions/filters/config.ts:156
msgid "{{title}}Type{{/title}} {{rule /}} {{filter /}}"
msgstr "{{title}}Tipo{{/title}} {{rule /}} {{filter /}}"

#: client/documents/filters/config.ts:60
#: client/transactions/filters/config.ts:117
msgid "Between"
msgstr "Tra"

#: client/documents/filters/config.ts:56
#: client/transactions/filters/config.ts:113
msgid "After"
msgstr "Dopo"

#: client/documents/filters/config.ts:52
#: client/transactions/filters/config.ts:109
msgid "Before"
msgstr "Prima"

#: client/transactions/list/index.tsx:166
msgid "Net"
msgstr "Netto"

#: client/payment-details/summary/index.tsx:149
msgid "Fee"
msgstr "Commissioni"

#: client/vat/form/tasks/company-data-task.tsx:98
msgid "Address"
msgstr "Indirizzo"

#: client/payment-details/summary/index.tsx:94
msgid "Payment method"
msgstr "Metodo di pagamento"

#: client/onboarding-experiment/tasks/setup-complete-task/index.tsx:17
msgid "Connect"
msgstr "Connetti"

#: client/disputes/strings.ts:53
msgid "If there have been two or more separate payments, you should get in touch with your customer. If you understand what their complaint is, there is a chance for you to explain the misunderstanding or to make it right. If you’re able to resolve the issue with your customer, you can ask that they withdraw the dispute."
msgstr "Se sono stati effettuati due o più pagamenti separati, dovresti metterti in contatto con il tuo cliente. Se capisci qual è il reclamo, hai la possibilità di spiegare il malinteso o di rimediare. Se sei in grado di risolvere il problema con il cliente, puoi chiedere di ritirare il ricorso."

#: client/disputes/strings.ts:52
msgid "If they were not, collect any and all information documenting that each payment was made separately, such as copies of receipts. If the receipts don’t include the items purchased, be sure to include an itemized list. Each receipt should clearly indicate that the payments are for separate purchases of items or services. If you’ve been able to get in touch with the customer you should be sure to address any concerns they had in your evidence."
msgstr "Se non lo fa, raccogli qualsiasi informazione che documenta ogni pagamento effettuato separatamente, come le copie delle ricevute. Se le ricevute non includono gli articoli acquistati, assicurati di includere un elenco dettagliato.  Ogni ricevuta dovrebbe chiaramente indicare chiaramente che i pagamenti sono relativi ad acquisti di articoli o servizi separati. Se riesci a contattare il cliente, dovresti assicurarti di affrontare eventuali dubbi relativi alla tua prova."

#: client/disputes/strings.ts:51
msgid "Determine if your customer was incorrectly charged multiple times."
msgstr "Determina se sono stati effettuati più volte addebiti in modo non corretto al tuo cliente."

#: client/disputes/strings.ts:48
msgid "Demonstrate that each payment was for a separate product or service."
msgstr "Dimostra che ogni pagamento era relativo a un prodotto o un servizio separato."

#: client/disputes/strings.ts:45
msgid "The customer claims they were charged multiple times for the same product or service."
msgstr "Il cliente afferma di aver ricevuto un addebito più volte per lo stesso prodotto o servizio."

#: client/disputes/strings.ts:42
msgid "If there were duplicate payments, you should accept the dispute. You cannot issue a refund while a payment is being disputed. The credit card networks place liability for accepting disputed payments with you, the business."
msgstr "Se sono stati effettuati pagamenti duplicati, dovresti accettare il ricorso. Non puoi emettere un rimborso durante il ricorso di un pagamento. Le reti delle carte di credito attribuiscono a te, l'azienda, la responsabilità per l'accettazione di pagamenti contestati."

#: client/disputes/strings.ts:39
msgid "Duplicate"
msgstr "Duplica"

#: client/disputes/strings.ts:36
msgid "Debit not authorized"
msgstr "Debito non autorizzato"

#: client/disputes/strings.ts:33
msgid "Customer initiated"
msgstr "Iniziata dal cliente"

#: client/disputes/strings.ts:29
msgid "If the cardholder agrees to withdraw the dispute, you should still submit evidence for the dispute using the forms on the next screen. In addition to the following evidence, your submission should include correspondence with the cardholder saying they would withdraw the dispute and a written statement from their card issuer confirming that the dispute has been withdrawn."
msgstr "Se il titolare della carta di credito concorda con il ritiro del ricorso, dovrebbe tuttavia inviare la prova per il ricorso usando i moduli sulla schermata successiva. In aggiunta alla seguente prova, l'invio dovrebbe includere la corrispondenza con il titolare della carta che in cui dichiara che vorrebbe ritirare il ricorso e una dichiarazione scritta dell'emittente della carta che conferma che il ricorso è stato ritirato."

#: client/disputes/strings.ts:28
msgid "You should first get in touch with your customer. If you understand what their complaint is, there is a chance for you to explain the misunderstanding or to make it right. If you’re able to resolve the issue with your customer, you can ask that they withdraw the dispute."
msgstr "Dovresti prima metterti in contatto con il cliente. Se capisci qual è il reclamo, hai la possibilità di spiegare il malinteso o di rimediare. Se sei in grado di risolvere il problema con il cliente, puoi chiedere di ritirare il ricorso."

#: client/disputes/strings.ts:25
msgid "Demonstrate that you have refunded your customer through other means or that your customer is not entitled to a refund. You cannot issue a refund while a payment is being disputed. If you believe that your customer was entitled a refund that you did not provide, you can accept the dispute."
msgstr "Dimostra che hai rimborsato il cliente tramite altri mezzi o che il cliente non ha diritto a un rimborso. Non puoi emettere un rimborso durante il ricorso di un pagamento. Se credi che il cliente abbia avuto diritto a un rimborso che non hai fornito, puoi accettare il ricorso."

#: client/disputes/strings.ts:159
msgid "It may be more efficient—and provide a better customer experience—to accept an accidental dispute and charge the customer again, if appropriate. Even when a dispute is withdrawn, it usually takes approximately 75 days to be finalized. Remember, it doesn’t matter to the card networks whether you win or lose a dispute; what matters is how many disputes a business receives, regardless of how many disputes are won."
msgstr "Potrebbe essere più efficiente (e fornire una migliore esperienza al cliente) accettare un ricorso accidentale e addebitare nuovamente al cliente, se appropriato. Anche quando un ricorso viene ritirato, di solito occorrono circa 75 giorni affinché venga finalizzato. Ricorda che le reti delle carte non sono interessate al fatto che tu vinca o perda un ricorso; ciò che conta è il numero di ricorsi che un'azienda riceve, indipendentemente dal numero di ricorsi vinti."

#: client/disputes/strings.ts:22
msgid "The customer claims that the purchased product was returned or the transaction was otherwise canceled, but you have not yet provided a refund or credit."
msgstr "Il cliente afferma che il prodotto acquistato è stato rimborsato o la transazione è stata diversamente annullata, ma tu non hai ancora fornito un rimborso o un credito."

#: client/disputes/strings.ts:158
msgid "First, try to get in touch with your customer. Sometimes people forget about payments they make or don’t recognize the way they appear on their card statement. If this is the case, ask them to contact their card issuer and let them know they no longer dispute the transaction. Even if your customer agrees to withdraw the dispute, you must still submit appropriate evidence. Simply saying that your customer is going to withdraw the dispute is not sufficient evidence."
msgstr "Per prima cosa, prova a metterti in contatto con il cliente. A volte le persone dimenticano i pagamenti che effettuano o non riconoscono il modo in cui compaiono sull'estratto conto della carta. Se è questo il caso, chiedi di contattare l'emittente della carta di credito e di informarlo che la transazione non è più contestata. Anche se il cliente concorda con il ritiro del ricorso, devi tuttavia inviare la prova adeguata. Dire semplicemente che il tuo cliente ritirerà il ricorso non è una prova sufficiente."

#: client/disputes/strings.ts:155
msgid "As with fraudulent disputes, get your customer to withdraw the dispute by helping them identify the payment. Otherwise challenge the dispute with appropriate evidence that proves the purchase was legitimate. "
msgstr "Come con i ricorsi fraudolenti, convinci il cliente a ritirare il ricorso aiutandolo a identificare il pagamento. Altrimenti, contesta il ricorso con prove adeguate che dimostrino la legittimità dell'acquisto. "

#: client/disputes/strings.ts:152
msgid "The customer doesn’t recognize the payment appearing on their card statement."
msgstr "Il cliente non riconosce il pagamento che compare sull'estratto conto della carta."

#: client/disputes/strings.ts:149
msgid "If you can not prove the customer’s subscription was canceled, and or they did not follow your cancelation policy, you should accept the dispute. You cannot issue a refund while a payment is being disputed. The credit card networks place liability for accepting disputed payments with you, the business."
msgstr "Se non puoi provare che l'abbonamento del cliente è stato annullato e/o non è stata seguita la tua politica di annullamento, dovresti accettare il ricorso. Non puoi emettere un rimborso durante il ricorso di un pagamento. Le reti delle carte di credito attribuiscono a te, l'azienda, la responsabilità per l'accettazione di pagamenti contestati."

#: client/disputes/strings.ts:148
msgid "If you believe the dispute is invalid, you can challenge it by submitting the appropriate evidence using the response forms on the next screen."
msgstr "Se credi che il ricorso non sia valido, puoi contestarlo inviando la prova adeguata tramite i moduli di risposta nella schermata successiva."

#: client/disputes/strings.ts:146
msgid "Unrecognized"
msgstr "Non riconosciuto"

#: client/disputes/strings.ts:142
msgid "Otherwise, use the forms on the next screen to submit evidence that the subscription was still active and that the customer was aware of, and did not follow, your cancellation procedure."
msgstr "Altrimenti, usa i moduli presenti nella prossima schermata per inviare la prova che l'abbonamento era ancora attivo e che il cliente ne era a conoscenza e non ha seguito la procedura di annullamento."

#: client/disputes/strings.ts:141
msgid "Even if your customer agrees to withdraw the dispute, you must still submit appropriate evidence using the forms on the next screen. Simply saying that your customer is going to withdraw the dispute is not sufficient evidence."
msgstr "Anche se il cliente concorda con il ritiro del ricorso, devi tuttavia inviare la prova adeguata usando i moduli presenti nella schermata successiva. Dire semplicemente che il tuo cliente ritirerà il ricorso non è una prova sufficiente."

#: client/disputes/strings.ts:140
msgid "First, get in touch with your customer. If you understand what they believe happened, there is a chance for you to explain the misunderstanding or to make it right. "
msgstr "Per prima cosa, mettiti in contatto con il cliente. Se capisci cosa crede sia accaduto, hai la possibilità di spiegare il malinteso o di rimediare. "

#: client/disputes/strings.ts:137
msgid "Prove that the subscription was still active and that the customer was aware of, and did not follow, your cancellation procedure."
msgstr "Prova che l'abbonamento era ancora attivo e che il cliente ne era a conoscenza e non ha seguito la procedura di annullamento."

#: client/disputes/strings.ts:134
msgid "The customer claims that you continued to charge them after a subscription was canceled."
msgstr "Il cliente afferma che continua hai continuato a effettuare gli addebiti dopo l'annullamento dell'abbonamento."

#: client/disputes/strings.ts:19
msgid "If your customer was not refunded appropriately, you will need to accept the dispute, or resolve the issue with your customer. The credit card networks place liability for accepting disputed payments with you, the business."
msgstr "Se il cliente non è stato rimborsato in modo adeguato, dovrai accettare il ricorso o risolvere il problema con il cliente. Le reti delle carte di credito attribuiscono a te, l'azienda, la responsabilità per l'accettazione di pagamenti contestati."

#: client/disputes/strings.ts:128
msgid "Subscription canceled"
msgstr "Abbonamento annullato"

#: client/disputes/strings.ts:124
msgid "If the customer withdraws their dispute you should still submit evidence using the forms on the next screen. Be sure to provide a letter or email from the cardholder stating that they are no longer in dispute."
msgstr "Se il cliente rifiuta il ricorso, dovresti tuttavia inviare la prova usando i moduli sulla schermata successiva. Assicurati di fornire una lettere o una e-mail del titolare della carta che afferma che il ricorso non è più attivo."

#: client/disputes/strings.ts:123
msgid "If your customer made no attempt to return the product or cancel the service, or if you provided a replacement product or service, make sure to note that as well."
msgstr "Se il tuo cliente non ha fatto alcun tentativo di restituire il prodotto o annullare il servizio o se hai fornito un prodotto o servizio sostitutivo, assicurati di prenderne nota."

#: client/disputes/strings.ts:122
msgid "For products that have been repaired or replaced, provide evidence that the cardholder agreed to a repair or replacement, it has been received by the customer, and the repair or replacement has not since been disputed."
msgstr "Per i prodotti che sono stati riparati o sostituiti, fornisci la prova che il titolare della carta era d'accordo con la riparazione o la sostituzione, il prodotto o servizio è stato ricevuto dal cliente e che la riparazione o la sostituzione non è stata contestata da allora."

#: client/disputes/strings.ts:121
msgid "If the customer has not yet returned the product or canceled the service, provide specific information to that effect. You should double-check your incoming shipping records to verify that you have not received a return before you respond. If you have processed a credit or reversal for this transaction, provide evidence of this which includes the amount and date processed."
msgstr "Se il cliente non ha ancora restituito il prodotto o annullato il servizio, fornisci informazioni specifiche a tal fine. Dovresti controllare nuovamente i record della spedizione in entrata per verificare di non aver ricevuto alcun articolo restituito prima di rispondere. Se hai elaborato un credito o un reso per questa transazione, fornisci una prova che includa l'importo e la data di elaborazione."

#: client/disputes/strings.ts:120
msgid "If the product or service is as described, provide specific information (invoice, contract, etc.) to refute the cardholder’s claims. Quality disputes are where the customer does not agree with the condition of merchandise or service received (e.g., a car repair situation or quality of a hotel room). There may be instances where you will need to obtain a neutral third-party opinion to help corroborate your claim against the cardholder. Provide as much specific information and documentation as possible to refute the cardholder’s claims. It is recommended that you address each point that the cardholder has made."
msgstr "Se il prodotto o il servizio è come descritto, fornisci informazioni specifiche (fattura, contratto, ecc.) per il rifiuto dei reclami del titolare della carta. I ricorsi sulla qualità avvengono quando il cliente non concorda con le condizioni del merchandise o del servizio ricevuto (ad esempio, una situazione di riparazione dell'auto o la qualità di una stanza d'albergo). Potrebbero verificarsi casi in cui sarà necessario ottenere un'opinione neutrale di terze parti per avvalorare il tuo reclamo nei confronti del titolare della carta. Fornisci più informazioni e documentazione specifiche possibile per rifiutare i reclami del titolare della carta. È consigliabile affrontare ogni punto sollevato dal titolare della carta."

#: client/disputes/strings.ts:117
msgid "Demonstrate that the product or service was delivered as described at the time of purchase."
msgstr "Dimostra che il servizio o il prodotto è stato fornito come descritto al momento dell'acquisto."

#: client/disputes/strings.ts:114
msgid "The product or service was received but was defective, damaged, or not as described."
msgstr "Il prodotto o il servizio è stato ricevuto, ma è difettoso, è danneggiato o non è come descritto."

#: client/disputes/strings.ts:111
msgid "If you can not prove the customer received their product or service as described, you should accept the dispute. You cannot issue a refund while a payment is being disputed. The credit card networks place liability for accepting disputed payments with you, the business."
msgstr "Se non puoi provare che il cliente ha ricevuto il prodotto o il servizio come descritto, dovresti accettare il ricorso. Non puoi emettere un rimborso durante il ricorso di un pagamento. Le reti delle carte di credito attribuiscono a te, l'azienda, la responsabilità per l'accettazione di pagamenti contestati."

#: client/disputes/strings.ts:108
msgid "Product unacceptable"
msgstr "Prodotto non accettabile"

#: client/disputes/strings.ts:16
msgid "Credit not processed"
msgstr "Credito non elaborato"

#: client/disputes/strings.ts:103
msgid "First, get in touch with your customer. Understanding why they filed the dispute will be important for helping make sure your customer gets the product and will give you critical information to prevent this from happening to others."
msgstr "Per prima cosa, mettiti in contatto con il cliente. Comprendere perché sia stato presentato il ricorso sarà importante per aiutare a garantire che il tuo cliente riceva il prodotto e ti fornirà informazioni importanti per evitare che ciò accada ad altri."

#: client/disputes/strings.ts:100
msgid "Prove that the customer received a physical product or offline service, or made use of a digital product or online service. This must have occurred prior to the date the dispute was initiated."
msgstr "Prova che il cliente ha ricevuto un prodotto fisico o un servizio offline o che ha usato un prodotto digitale o un servizio online. Questo deve essere avvenuto prima della data di inizio del ricorso."

#: client/disputes/strings.ts:97
msgid "The customer claims they did not receive the products or services purchased."
msgstr "Il cliente afferma di non aver ricevuto i prodotti o i servizi acquistati."

#: client/disputes/strings.ts:94
msgid "If you can not prove the customer received their product or service, you should accept the dispute. You cannot issue a refund while a payment is being disputed. The credit card networks place liability for accepting disputed payments with you, the business."
msgstr "Se non puoi provare che il cliente ha ricevuto il prodotto o il servizio, dovresti accettare il ricorso. Non puoi emettere un rimborso durante il ricorso di un pagamento. Le reti delle carte di credito attribuiscono a te, l'azienda, la responsabilità per l'accettazione di pagamenti contestati."

#: client/disputes/strings.ts:91
msgid "Product not received"
msgstr "Prodotto non ricevuto"

#: client/disputes/strings.ts:88
msgid "Insufficient funds"
msgstr "Fondi insufficienti"

#: client/disputes/strings.ts:13
msgid "Check returned"
msgstr "Assegno restituito"

#: client/disputes/strings.ts:85
msgid "Incorrect account details"
msgstr "Dati del conto non corretti"

#: client/disputes/strings.ts:81
msgid "This is an uncategorized dispute, so you should contact the customer for additional details to find out why the payment was disputed."
msgstr "Questo è un ricorso senza categoria, quindi dovresti contattare il cliente per dettagli aggiuntivi per scoprire il motivo del ricorso del pagamento."

#: client/disputes/strings.ts:10
msgid "Bank cannot process"
msgstr "La banca non può elaborare"

#: client/disputes/strings.ts:70
msgid "Try to get in touch with your customer. Sometimes people forget about payments they make or don’t recognize the way they appear on their card statement. If this is the case, ask them to contact their card issuer and let them know they no longer dispute the transaction."
msgstr "Prova a contattare il cliente. A volte le persone dimenticano i pagamenti che effettuano o non riconoscono il modo in cui compaiono sull'estratto conto della carta. Se è questo il caso, chiedi di contattare l'emittente della carta di credito e di informarlo che la transazione non è più contestata."

#: client/disputes/strings.ts:67
msgid "Provide adequate payment and order details so that a legitimate customer recognizes it, or proves to the card issuer that their cardholder authorized the transaction."
msgstr "Fornisci un pagamento adeguato e dettagli dell'ordine in modo che un cliente legittimo li riconosca o dimostri all'emittente della carta che il titolare della carta ha autorizzato la transazione."

#: client/disputes/strings.ts:64
msgid "This is the most common reason for a dispute, and happens when a cardholder claims that they didn’t authorize the payment. This can happen if the card was lost or stolen and used to make a fraudulent purchase. It can also happen if the cardholder doesn’t recognize the payment as it appears on the billing statement from their card issuer."
msgstr "Questo è il motivo più comune per un ricorso e ciò accade quando un titolare della carta afferma che il pagamento non è stato autorizzato. Questo può avvenire se la carta è stata persa o rubata e usata per un acquisto fraudolento. Può anche avvenire se il titolare della carta non riconosce il pagamento come appare sull'estratto conto dell'emittente della carta."

#: client/disputes/strings.ts:61
msgid "If you believe the payment was actually made using a stolen credit card, you will need to accept the dispute. The credit card networks place liability for accepting fraudulent payments with you, the business."
msgstr "Se ritieni che il pagamento sia stato effettivamente effettuato usando una carta di credito rubata, dovrai accettare il ricorso. Le reti delle carte di credito attribuiscono a te, l'azienda, la responsabilità per l'accettazione di pagamenti fraudolenti."

#: client/disputes/strings.ts:58
msgid "Fraudulent"
msgstr "Fraudolento"

#: includes/in-person-payments/templates/html-in-person-payment-receipt.php:132
#: client/payment-details/summary/index.tsx:83
#: client/transactions/uncaptured/index.tsx:46
msgid "Order"
msgstr "Ordine"

#: client/disputes/info/index.tsx:23
msgid "Respond by"
msgstr "Rispondi tramite"

#: client/disputes/index.tsx:154
#: client/onboarding-experiment/tasks/add-business-info-task/index.tsx:52
#: client/transactions/list/index.tsx:204
#: client/transactions/uncaptured/index.tsx:73
msgid "Country"
msgstr "Paese"

#: client/transactions/list/index.tsx:197
#: client/transactions/uncaptured/index.tsx:66
msgid "Email"
msgstr "E-mail"

#: client/disputes/info/index.tsx:26
#: client/payment-details/summary/index.tsx:79
#: client/transactions/blocked/columns.tsx:41
#: client/transactions/list/index.tsx:191
#: client/transactions/risk-review/columns.tsx:43
msgid "Customer"
msgstr "Cliente"

#: client/transactions/list/index.tsx:185
msgid "Source"
msgstr "Sorgente"

#: client/disputes/info/index.tsx:24
msgid "Reason"
msgstr "Motivo"

#: client/components/file-upload/index.tsx:42
msgid "Upload file"
msgstr "Carica file"

#: client/transactions/list/deposit.tsx:22
msgid "Estimated"
msgstr "Stimato"

#: client/capital/index.tsx:34 client/card-readers/list/index.tsx:29
#: client/components/deposits-overview/next-deposit.tsx:75
#: client/components/deposits-overview/recent-deposits-list.tsx:68
#: client/disputes/filters/config.ts:110 client/disputes/index.tsx:114
#: client/transactions/blocked/columns.tsx:48
#: client/transactions/risk-review/columns.tsx:50
msgid "Status"
msgstr "Stato"

#: client/components/deposits-overview/next-deposit.tsx:77
#: client/components/deposits-overview/recent-deposits-list.tsx:69
#: client/deposits/list/index.tsx:110 client/disputes/index.tsx:100
#: client/transactions/blocked/columns.tsx:34
#: client/transactions/list/index.tsx:152
#: client/transactions/risk-review/columns.tsx:36
#: client/transactions/uncaptured/index.tsx:59
msgid "Amount"
msgstr "Valore"

#: client/documents/filters/config.ts:69 client/documents/list/index.tsx:38
#: client/transactions/filters/config.ts:152
#: client/transactions/list/index.tsx:138
msgid "Type"
msgstr "Tipo"

#: client/deposits/list/index.tsx:92 client/documents/filters/config.ts:42
#: client/documents/list/index.tsx:26
#: client/payment-details/summary/index.tsx:67
#: client/transactions/filters/config.ts:99
msgid "Date"
msgstr "Data"

#: client/deposits/list/index.tsx:275
msgid "Deposit history"
msgstr "Cronologia acconto"

#: client/transactions/filters/config.ts:39
msgid "Deposit currency"
msgstr "Valuta dell'acconto"

#: client/transactions/list/index.tsx:218
msgid "Deposit date"
msgstr "Data dell'acconto"

#: client/components/transaction-status-chip/mappings.ts:17
msgid "Payment blocked"
msgstr "Pagamento bloccato"

#: client/deposits/strings.ts:11
msgid "Paid"
msgstr "Pagato"

#: client/payment-details/summary/index.tsx:146
msgid "Refunded"
msgstr "Rimborsato"

#: client/components/dispute-status-chip/mappings.ts:37
#: client/disputes/strings.ts:172
msgid "Lost"
msgstr "Perso"

#: client/components/dispute-status-chip/mappings.ts:33
#: client/disputes/strings.ts:171
msgid "Won"
msgstr "Vinto"

#: client/components/dispute-status-chip/mappings.ts:29
#: client/disputes/strings.ts:170
msgid "Charge refunded"
msgstr "Addebito rimborsato"

#: client/components/dispute-status-chip/mappings.ts:25
#: client/disputes/strings.ts:169
msgid "Under review"
msgstr "In fase di revisione"

#: client/components/dispute-status-chip/mappings.ts:21
#: client/disputes/filters/config.ts:62 client/disputes/strings.ts:168
msgid "Needs response"
msgstr "Necessita di risposta"

#: client/components/dispute-status-chip/mappings.ts:17
#: client/disputes/strings.ts:167
msgid "Inquiry: Closed"
msgstr "Domanda: chiusa"

#: client/components/dispute-status-chip/mappings.ts:13
#: client/disputes/strings.ts:166
msgid "Inquiry: Under review"
msgstr "Domanda: in fase di revisione"

#: client/components/dispute-status-chip/mappings.ts:9
#: client/disputes/strings.ts:165
msgid "Inquiry: Needs response"
msgstr "Domanda: necessita di risposta"

#: client/components/payments-status/index.tsx:23
msgid "Disabled"
msgstr "Disabilitato"

#: client/components/deposit-status-pill/index.tsx:29
#: client/components/deposits-status/index.tsx:60
msgid "Unknown"
msgstr "Sconosciuto"

#: includes/fraud-prevention/class-order-fraud-and-risk-meta-box.php:127
#: client/components/fraud-risk-tools-banner/components/banner-actions/index.tsx:17
#: client/onboarding-experiment/strings.ts:9
#: client/utils/account-fees.tsx:133
msgid "Learn more"
msgstr "Scopri di più"

#. translators: This is an error API response.
#: includes/wc-payment-api/class-wc-payments-api-client.php:1990
msgctxt "API error message to throw as Exception"
msgid "Error: %1$s"
msgstr "Errore: %1$s"

#: woocommerce-payments.php:253
msgid "The version of Jetpack installed is too old to be used with WooCommerce Payments. WooCommerce Payments has been disabled. Please deactivate or update Jetpack."
msgstr "La versione di Jetpack installata è troppo vecchia per essere usata con WooCommerce Payments. WooCommerce Payments è stato disabilitato. Disattiva o aggiorna Jetpack."

#. translators: %1: original error message.
#: includes/class-wc-payments.php:1479
#: includes/wc-payment-api/class-wc-payments-http.php:114
msgid "Http request failed. Reason: %1$s"
msgstr "Richiesta HTML non riuscita. Motivo: %1$s"

#: includes/wc-payment-api/class-wc-payments-http.php:55
msgid "Site is not connected to WordPress.com"
msgstr "Il sito non è collegato a WordPress.com"

#: includes/wc-payment-api/class-wc-payments-api-client.php:1985
msgid "Server error. Please try again."
msgstr "Errore del server. Riprova."

#: includes/wc-payment-api/class-wc-payments-api-client.php:1949
msgid "Unable to decode response from WooCommerce Payments API"
msgstr "Impossibile decodificare la risposta dell'API di WooCommerce Payments."

#: includes/wc-payment-api/class-wc-payments-api-client.php:1762
msgid "Unable to encode body for request to WooCommerce Payments API."
msgstr "Impossibile codificare il corpo della richiesta all'API di WooCommerce Payments."

#: includes/wc-payment-api/class-wc-payments-api-client.php:1035
msgid "Customer ID is required"
msgstr "È richiesto l'ID del cliente"

#: includes/wc-payment-api/class-wc-payments-api-client.php:652
msgid "Max file size exceeded."
msgstr "Dimensione massima del file superata."

#: includes/notes/class-wc-payments-notes-set-up-refund-policy.php:36
msgid "Protect your merchant account from fraudulent disputes by defining the policy and making it accessible to customers."
msgstr "Proteggi il tuo conto commerciante da ricorsi fraudolenti definendo la politica e rendendola accessibile ai clienti."

#: includes/notes/class-wc-payments-notes-set-up-refund-policy.php:35
msgid "Set up refund policy"
msgstr "Configura la politica di rimborso"

#: includes/notes/class-wc-payments-notes-qualitative-feedback.php:56
msgid "Share feedback"
msgstr "Condividi il tuo feedback"

#: includes/notes/class-wc-payments-notes-qualitative-feedback.php:49
msgid "Share your feedback in this 2 minute survey about how we can make the process of accepting payments more useful for your store."
msgstr "Condividi il tuo feedback tramite questa indagine di 2 minuti su come possiamo rendere più valido il processo di accettazione dei pagamenti per il tuo negozio."

#: includes/notes/class-wc-payments-notes-qualitative-feedback.php:48
msgid "Help us make improvements to WooCommerce Payments"
msgstr "Aiutaci a migliorare WooCommerce Payments"

#: includes/notes/class-wc-payments-notes-set-https-for-checkout.php:59
#: includes/notes/class-wc-payments-notes-set-up-refund-policy.php:43
msgid "Read more"
msgstr "Continua a leggere"

#. translators: 1: payment method likely credit card, 2: last 4 digit.
#: includes/compat/subscriptions/trait-wc-payment-gateway-wcpay-subscriptions.php:609
#: includes/compat/subscriptions/trait-wc-payment-gateway-wcpay-subscriptions.php:628
#: includes/compat/subscriptions/trait-wc-payment-gateway-wcpay-subscriptions.php:669
#: includes/compat/subscriptions/trait-wc-payment-gateway-wcpay-subscriptions.php:677
msgid "%1$s ending in %2$s"
msgstr "%1$s che termina in %2$s"

#: includes/compat/subscriptions/trait-wc-payment-gateway-wcpay-subscriptions.php:521
#: includes/compat/subscriptions/trait-wc-payment-gateway-wcpay-subscriptions.php:575
msgid "Please select a payment method"
msgstr "Seleziona un metodo di pagamento"

#: includes/compat/subscriptions/trait-wc-payment-gateway-wcpay-subscriptions.php:452
msgid "The saved payment method selected does not belong to this order's customer."
msgstr "Il metodo di pagamento salvato selezionato non è legato al cliente di questo ordine."

#: includes/compat/subscriptions/trait-wc-payment-gateway-wcpay-subscriptions.php:445
msgid "The saved payment method selected is invalid or does not exist."
msgstr "Il metodo di pagamento salvato selezionato non è valido o non esiste."

#: includes/compat/subscriptions/trait-wc-payment-gateway-wcpay-subscriptions.php:436
msgid "A customer saved payment method was not selected for this order."
msgstr "Un metodo di pagamento salvato del cliente non è stato selezionato per questo ordine. "

#: includes/compat/subscriptions/trait-wc-payment-gateway-wcpay-subscriptions.php:365
msgid "Saved payment method"
msgstr "Metodo di pagamento salvato"

#: includes/class-wc-payments-dependency-service.php:287
msgid "Update WordPress"
msgstr "Aggiorna WordPress"

#. translators: %1: required WP version number, %2: currently installed WP
#. version number
#: includes/class-wc-payments-dependency-service.php:280
msgid "WooCommerce Payments requires <strong>WordPress %1$s</strong> or greater (you are using %2$s)."
msgstr "WooCommerce Payments richiede <strong>WordPress %1$s</strong> o versioni superiori (stai usando %2$s)."

#: includes/class-wc-payments-dependency-service.php:272
msgid "Use the bundled version of WooCommerce Admin"
msgstr "Usa la versione inclusa di WooCommerce Admin"

#: includes/class-wc-payments-dependency-service.php:268
msgid "There is a newer version of WooCommerce Admin bundled with WooCommerce."
msgstr "Esiste una versione più recente di WooCommerce Admin inclusa con WooCommerce."

#. translators: %1: required WC-Admin version number, %2: currently installed
#. WC-Admin version number
#: includes/class-wc-payments-dependency-service.php:260
msgid "WooCommerce Payments requires <strong>WooCommerce Admin %1$s</strong> or greater to be installed (you are using %2$s)."
msgstr "WooCommerce Payments richiede <strong>WordPress Admin %1$s</strong> o versioni superiori per essere installato (stai usando %2$s)."

#: includes/class-wc-payments-dependency-service.php:251
msgid "WooCommerce Payments requires WooCommerce Admin to be enabled. Please remove the <code>woocommerce_admin_disabled</code> filter to use WooCommerce Payments."
msgstr "WooCommerce Payments richiede WordPress Admin per essere abilitato. Rimuovi il filtro <code>woocommerce_admin_disabled</code> per usare WooCommerce Payments."

#. translators: %1: current WooCommerce Payment version, %2: required WC
#. version number, %3: currently installed WC version number
#: includes/class-wc-payments-dependency-service.php:225
msgid "WooCommerce Payments %1$s requires <strong>WooCommerce %2$s</strong> or greater to be installed (you are using %3$s). "
msgstr "WooCommerce Payments %1$s richiede <strong>WooCommerce %2$s</strong> o versioni superiori per essere installato (stai usando %3$s). "

#: includes/class-wc-payments-dependency-service.php:215
msgid "Activate WooCommerce"
msgstr "Attiva WooCommerce"

#: includes/class-wc-payments-dependency-service.php:211
msgid "Install WooCommerce"
msgstr "Installa WooCommerce"

#: includes/class-wc-payments-dependency-service.php:203
msgid "WooCommerce Payments requires <a>WooCommerce</a> to be installed and active."
msgstr "WooCommerce Payments richiede <a>WooCommerce</a> per essere installato e attivato."

#: includes/class-wc-payments-utils.php:287
#: includes/wc-payment-api/class-wc-payments-api-client.php:403
#: client/transactions/list/index.tsx:179
msgid "Subscription #"
msgstr "Abbonamento n."

#: includes/class-wc-payments-utils.php:277
#: includes/wc-payment-api/class-wc-payments-api-client.php:405
#: client/disputes/index.tsx:135 client/transactions/list/index.tsx:173
msgid "Order #"
msgstr "Ordine n."

#. translators: %1: the authorized amount, %2: transaction ID of the payment
#: includes/class-wc-payments-order-service.php:1113
msgid "Payment authorization has <strong>expired</strong> (<a>%1$s</a>)."
msgstr "L'autorizzazione di pagamento è <strong>scaduta</strong> (<a>%1$s</a>)."

#: includes/class-wc-payments-captured-event-note.php:323
#: includes/class-wc-payments-payment-request-button-handler.php:1556
#: includes/class-wc-payments-woopay-button-handler.php:312
#: includes/in-person-payments/templates/html-in-person-payment-receipt.php:160
msgid "Discount"
msgstr "Sconto"

#: includes/class-wc-payments-payment-request-button-handler.php:1375
msgid "Empty cart"
msgstr "Carrello vuoto"

#. translators: %s: country.
#: includes/class-wc-payments-payment-request-button-handler.php:1362
msgid "The payment request button is not supported in %s because some required fields couldn't be verified. Please proceed to the checkout page and try again."
msgstr "Il pulsante di richiesta di pagamento non è supportato in questa località: %s. Alcuni campi richiesti non possono essere verificati. Vai alla pagina di pagamento e prova di nuovo."

#. translators: 1: product name 2: quantity in stock
#: includes/class-wc-payments-payment-request-button-handler.php:1034
msgid "You cannot add that amount of \"%1$s\"; to the cart because there is not enough stock (%2$s remaining)."
msgstr "Non puoi aggiungere questa quantità di \"%1$s\"; al carrello perché le score di magazzino non sono sufficienti (disponibili %2$s)."

#. translators: product ID
#: includes/class-wc-payments-payment-request-button-handler.php:1013
msgid "Product with the ID (%d) cannot be found."
msgstr "Impossibile trovare il prodotto con ID (%d)."

#: includes/class-wc-payments-payment-request-button-handler.php:901
#: includes/class-wc-payments-payment-request-button-handler.php:914
msgid "Unable to find shipping method for address."
msgstr "Impossibile trovare il metodo di spedizione per l'indirizzo."

#: includes/class-wc-payments-express-checkout-button-display-handler.php:79
msgid "OR"
msgstr "OR"

#: includes/class-wc-payments-payment-request-button-handler.php:307
#: includes/class-wc-payments-payment-request-button-handler.php:1070
#: client/deposits/strings.ts:12
msgid "Pending"
msgstr "In sospeso"

#: includes/class-wc-payments-payment-request-button-handler.php:300
#: includes/class-wc-payments-payment-request-button-handler.php:1063
#: includes/class-wc-payments-payment-request-button-handler.php:1549
#: includes/class-wc-payments-woopay-button-handler.php:305
msgid "Shipping"
msgstr "Spedizione"

#: includes/class-wc-payments-payment-request-button-handler.php:292
#: includes/class-wc-payments-payment-request-button-handler.php:349
#: includes/class-wc-payments-payment-request-button-handler.php:1055
#: includes/class-wc-payments-payment-request-button-handler.php:1541
#: includes/class-wc-payments-woopay-button-handler.php:297
#: includes/in-person-payments/templates/html-in-person-payment-receipt.php:185
msgid "Tax"
msgstr "Imposta"

#. translators: %1$s Name.
#: includes/class-wc-payments-customer-service.php:305
msgid "Name: %1$s, Guest"
msgstr "Nome: %1$s, ospite"

#. translators: %1$s Name, %2$s Username.
#: includes/class-wc-payments-customer-service.php:301
msgid "Name: %1$s, Username: %2$s"
msgstr "Nome: %1$s, nome utente: %2$s"

#: includes/class-wc-payments-apple-pay-registration.php:396
msgid "Apple Pay domain verification failed with the following error:"
msgstr "La verifica del dominio di Apple Pay non è riuscita con il seguente errore:"

#: includes/class-wc-payments-apple-pay-registration.php:395
msgid "Apple Pay domain verification failed."
msgstr "Verifica del dominio di Apple Pay non riuscita."

#: includes/class-wc-payments-apple-pay-registration.php:361
msgid "Express checkouts are enabled. To use Apple Pay, please use a live WooCommerce Payments account."
msgstr "I pagamenti rapidi sono abilitati. Per usare Apple Pay, utilizza un account WooCommerce Payments live."

#: includes/class-wc-payments-apple-pay-registration.php:272
msgid "Your domain has been verified with Apple Pay!"
msgstr "Il dominio è stato verificato con Apple Pay."

#: includes/class-wc-payments-apple-pay-registration.php:196
msgid "Domain association file updated."
msgstr "File di associazione del dominio aggiornato."

#. translators: expected domain association file URL
#: includes/class-wc-payments-apple-pay-registration.php:193
msgid "To enable Apple Pay, domain association file must be hosted at %s."
msgstr "Per abilitare Apple Pay, il file di associazione del dominio deve essere ospitato in %s."

#: includes/class-wc-payments-apple-pay-registration.php:173
msgid "Unable to copy domain association file to domain root."
msgstr "Impossibile copiare il file di associazione del dominio per la root del dominio."

#: includes/class-wc-payments-apple-pay-registration.php:169
msgid "Unable to create domain association folder to domain root."
msgstr "Impossibile creare la cartella di associazione del dominio per la root del dominio."

#: includes/class-wc-payments-account.php:1147
msgid "There was a problem processing your account data. Please try again."
msgstr "Si è verificato un problema durante l'elaborazione dei dati dell'account. Riprova."

#: includes/class-wc-payments-account.php:812
msgid "There was a problem redirecting you to the account connection page. Please try again."
msgstr "Si è verificato un problema durante il reindirizzamento alla pagina di connessione dell'account. Riprova."

#. translators: error message.
#: includes/class-wc-payments-account.php:802
msgid "There was a problem connecting this site to WordPress.com: \"%s\""
msgstr "Si è verificato un problema durante la connessione a WordPress.com: \"%s\""

#: includes/class-wc-payments-account.php:792
msgid "Connection to WordPress.com failed. Please connect to WordPress.com to start using WooCommerce Payments."
msgstr "Connessione a WordPress.com non riuscita. Connettiti a WordPress.com per iniziare a usare WooCommerce Payments."

#: includes/class-wc-payments-account.php:152
msgid "Failed to detect connection status"
msgstr "Impossibile rilevare lo stato della connessione"

#: includes/class-wc-payments-status.php:53
msgid "This tool will clear the account cached values used in WooCommerce Payments."
msgstr "Questo strumento eliminerà i valori dell'account memorizzati nella cache usati in WooCommerce Payments."

#: includes/class-wc-payments-status.php:52
msgid "Clear"
msgstr "Pulisci"

#: includes/class-wc-payments-status.php:51
msgid "Clear WooCommerce Payments account cache"
msgstr "Cancella la cache dell'account di WooCommerce Payments"

#: includes/class-wc-payment-gateway-wcpay.php:3046
#: includes/payment-methods/class-upe-payment-gateway.php:384
#: includes/payment-methods/class-upe-split-payment-gateway.php:291
msgid "We're not able to add this payment method. Please refresh the page and try again."
msgstr "Impossibile aggiungere questo metodo di pagamento. Aggiorna la pagina e riprova."

#: includes/class-wc-payment-gateway-wcpay.php:2890
msgid "Failed to add the provided payment method. Please try again later"
msgstr "Impossibile aggiungere il metodo di pagamento fornito. Riprova più tardi."

#: includes/class-wc-payment-gateway-wcpay.php:2881
msgid "We're not able to add this payment method. Please try again later"
msgstr "Impossibile aggiungere questo metodo di pagamento. Riprova più tardi."

#: includes/class-wc-payment-gateway-wcpay.php:2869
msgid "A WooCommerce Payments payment method was not provided"
msgstr "Non è stato fornito un metodo di pagamento WooCommerce Payments"

#. translators: %1: transaction ID of the payment or a translated string
#. indicating an unknown ID.
#: includes/class-wc-payment-gateway-wcpay.php:2825
msgid "A payment with ID <code>%1$s</code> was used in an attempt to pay for this order. This payment intent ID does not match any payments for this order, so it was ignored and the order was not updated."
msgstr "È stato usato un pagamento con ID <code>%1$s</code> nel tentativo di pagare questo ordine. Questo ID dell'intento di pagamento non corrisponde ad alcun pagamento per questo ordine, quindi è stato ignorato e l'ordine non è stato aggiornato."

#. translators: %1: the authorized amount, %2: transaction ID of the payment
#: includes/class-wc-payments-order-service.php:972
msgid "A payment of %1$s <strong>failed</strong> using WooCommerce Payments (<a>%2$s</a>)."
msgstr "Pagamento di %1$s <strong>non riuscito</strong> usando WooCommerce Payments (<a>%2$s</a>)."

#. translators: This will be used to indicate an unknown value for an ID.
#: includes/class-wc-payment-gateway-wcpay.php:2742
msgid "unknown"
msgstr "sconosciuto"

#: includes/class-wc-payment-gateway-wcpay.php:1101
#: includes/class-wc-payment-gateway-wcpay.php:1186
#: includes/class-wc-payment-gateway-wcpay.php:2733
#: includes/class-wc-payment-gateway-wcpay.php:2746
#: includes/class-wc-payment-gateway-wcpay.php:2763
#: includes/payment-methods/class-upe-payment-gateway.php:764
msgid "We're not able to process this payment. Please try again later."
msgstr "Non è stato possibile elaborare questo pagamento. Riprova più tardi."

#: includes/class-wc-payment-gateway-wcpay.php:2604
msgid "Canceling authorization <strong>failed</strong> to complete."
msgstr "Completamento dell'annullamento dell'autorizzazione <strong>non riuscito</strong>."

#. translators: %1: error message
#: includes/class-wc-payment-gateway-wcpay.php:2589
msgid "Canceling authorization <strong>failed</strong> to complete with the following message: <code>%1$s</code>."
msgstr "Completamento dell'annullamento dell'autorizzazione <strong>non riuscito</strong> con il seguente messaggio: <code>%1$s</code>."

#: includes/class-wc-payments-order-service.php:1131
msgid "Payment authorization was successfully <strong>cancelled</strong>."
msgstr "<strong>Annullamento</strong> dell'autorizzazione di pagamento completato correttamente."

#. translators: %1: the successfully charged amount, %2: transaction ID of the
#. payment
#: includes/class-wc-payments-order-service.php:1055
msgid "A payment of %1$s was <strong>successfully captured</strong> using WooCommerce Payments (<a>%2$s</a>)."
msgstr "Il pagamento di %1$s è stato <strong>acquisito correttamente</strong> usando WooCommerce Payments (<a>%2$s</a>)."

#: includes/class-wc-payment-gateway-wcpay.php:2451
msgid "Cancel authorization"
msgstr "Annulla autorizzazione"

#: includes/class-wc-payment-gateway-wcpay.php:2450
msgid "Capture charge"
msgstr "Acquisisci addebito"

#: includes/class-wc-payment-gateway-wcpay.php:2418
msgid "Customer bank statement is invalid. Statement should be between 5 and 22 characters long, contain at least single Latin character and does not contain special characters: ' \" * &lt; &gt;"
msgstr "Estratto conto della banca del cliente non valido. L'estratto conto dovrebbe contenere da 5 a 22 caratteri, almeno un carattere latino e non contenere caratteri speciali: ' \" * &lt; &gt;"

#. translators: %1: the successfully charged amount, %2: refund id
#: includes/class-wc-payment-gateway-wcpay.php:1570
msgid "A refund of %1$s was successfully processed using WooCommerce Payments (<code>%2$s</code>)."
msgstr "Un rimborso di %1$s è stato elaborato correttamente usando WooCommerce Payments (<code>%2$s</code>)."

#. translators: %1: the successfully charged amount, %2: error message
#: includes/class-wc-payment-gateway-wcpay.php:1552
msgid "A refund of %1$s failed to complete: %2$s"
msgstr "Completamento di un rimborso di %1$s non riuscito: %2$s"

#. translators: an error message which will appear if a user tries to refund an
#. order which is has been authorized but not yet charged.
#: includes/class-wc-payment-gateway-wcpay.php:1497
msgid "This payment is not captured yet. To cancel this order, please go to 'Order Actions' > 'Cancel authorization'. To proceed with a refund, please go to 'Order Actions' > 'Capture charge' to charge the payment card, and then trigger a refund via the 'Refund' button."
msgstr "Questo pagamento non è stato ancora acquisito. Per annullare questo ordine, vai su \"Azioni dell'ordine\" > \"Annulla autorizzazione\". Per procedere con un rimborso, vai su \"Azioni dell'ordine\" > \"Acquisisci addebito\" per l'addebito sulla carta di pagamento, quindi attiva un rimborso tramite il pulsante \"Rimborsa\"."

#. translators: %1: the authorized amount, %2: transaction ID of the payment
#: includes/class-wc-payments-order-service.php:1028
msgid "A payment of %1$s was <strong>started</strong> using WooCommerce Payments (<code>%2$s</code>)."
msgstr "Il pagamento di %1$s è stato <strong>avviato</strong> usando WooCommerce Payments (<code>%2$s</code>)."

#. translators: %1: the authorized amount, %2: transaction ID of the payment
#: includes/class-wc-payments-order-service.php:1003
msgid "A payment of %1$s was <strong>authorized</strong> using WooCommerce Payments (<a>%2$s</a>)."
msgstr "Il pagamento di %1$s è stato <strong>autorizzato</strong> usando WooCommerce Payments (<a>%2$s</a>)."

#. translators: %1: the successfully charged amount, %2: transaction ID of the
#. payment
#: includes/class-wc-payments-order-service.php:946
msgid "A payment of %1$s was <strong>successfully charged</strong> using WooCommerce Payments (<a>%2$s</a>)."
msgstr "Il pagamento di %1$s è stato <strong>addebitato correttamente</strong> usando WooCommerce Payments (<a>%2$s</a>)."

#. translators: %1: the last 4 digit of the credit card
#: includes/class-wc-payment-gateway-wcpay.php:1042
msgid "Payment method is changed to: <strong>Credit card ending in %1$s</strong>."
msgstr "Il metodo di pagamento è stato modificato a: <strong>Carta di credito terminante in %1$s</strong>"

#. translators: %1: the failed payment amount, %2: error message
#: includes/class-wc-payment-gateway-wcpay.php:796
#: includes/compat/subscriptions/trait-wc-payment-gateway-wcpay-subscriptions.php:318
msgid "A payment of %1$s <strong>failed</strong> to complete with the following message: <code>%2$s</code>."
msgstr "Completamento del pagamento di %1$s <strong>non riuscito</strong> con il seguente messaggio: <code>%2$s</code>."

#: includes/class-wc-payment-gateway-wcpay.php:728
#: includes/class-wc-payment-gateway-wcpay.php:2724
#: includes/payment-methods/class-upe-payment-gateway.php:178
#: includes/payment-methods/class-upe-payment-gateway.php:274
#: includes/payment-methods/class-upe-payment-gateway.php:496
#: includes/payment-methods/class-upe-split-payment-gateway.php:199
#: includes/payment-methods/class-upe-split-payment-gateway.php:234
#: includes/payment-methods/class-upe-split-payment-gateway.php:246
#: includes/payment-methods/class-upe-split-payment-gateway.php:299
msgid "We're not able to process this payment. Please refresh the page and try again."
msgstr "Non è stato possibile elaborare questo pagamento. Aggiorna la pagina e riprova."

#: includes/class-wc-payments-utils.php:526
msgid "There was an error while processing this request. If you continue to see this notice, please contact the admin."
msgstr "Si è verificato un errore durante l'elaborazione di questa richiesta. Se continui a visualizzare questo avviso, contatta l'amministrazione."

#. translators: link to Stripe testing page
#: includes/class-wc-payments-checkout.php:226
#: includes/class-wc-payments-upe-checkout.php:333
msgid "An error was encountered when preparing the payment form. Please try again later."
msgstr "Si è verificato un errore durante la preparazione del modulo di pagamento. Riprova più tardi."

#. translators: link to Stripe testing page
#: includes/class-wc-payments-checkout.php:176
msgid "<strong>Test mode:</strong> use the test VISA card **************** with any expiry date and CVC, or any test card numbers listed <a>here</a>."
msgstr "<strong>Modalità di prova:</strong> usa la carta VISA di prova **************** con qualsiasi data di scadenza e CVC o qualsiasi numero di carta di prova elencato <a>qui</a>."

#: includes/class-wc-payment-gateway-wcpay.php:665
msgid "Save payment information to my account for future purchases."
msgstr "Salva le informazioni di pagamento sul mio account per acquisti futuri."

#: includes/class-wc-payments-checkout.php:113
msgid "There was a problem processing the payment. Please check your email inbox and refresh the page to try again."
msgstr "Si è verificato un problema durante l'elaborazione del pagamento. Controlla la tua posta in arrivo dell'e-mail e aggiorna la pagina per riprovare."

#: includes/admin/class-wc-payments-admin-sections-overwrite.php:69
msgid "All payment methods"
msgstr "Tutti i metodi di pagamento"

#: includes/admin/class-wc-payments-admin-settings.php:52
msgid "All transactions are simulated. Customers can't make real purchases through WooCommerce Payments."
msgstr "Tutte le transazioni sono simulate. I clienti non possono effettuare acquisti reali tramite WooCommerce Payments."

#: includes/admin/class-wc-payments-admin-settings.php:51
msgid "Test mode active: "
msgstr "Modalità di prova attiva: "

#: includes/class-wc-payment-gateway-wcpay.php:360
msgid "Select pages"
msgstr "Seleziona le pagine"

#: includes/class-wc-payment-gateway-wcpay.php:357
msgid "Checkout"
msgstr "Pagamento"

#: includes/class-wc-payment-gateway-wcpay.php:356
msgid "Cart"
msgstr "Carrello"

#: includes/class-wc-payment-gateway-wcpay.php:355
msgid "Product"
msgstr "Prodotto"

#: includes/class-wc-payment-gateway-wcpay.php:346
msgid "Select where you would like to display the button."
msgstr "Seleziona il punto in cui desideri venga visualizzato il pulsante."

#: includes/class-wc-payment-gateway-wcpay.php:344
msgid "Button locations"
msgstr "Posizioni del pulsante"

#: includes/class-wc-payment-gateway-wcpay.php:340
msgid "Buy now"
msgstr "Acquista ora"

#: includes/class-wc-payment-gateway-wcpay.php:339
msgid "Enter the custom text you would like the button to have."
msgstr "Inserisci il testo personalizzato che desideri sia presente sul pulsante."

#: includes/class-wc-payment-gateway-wcpay.php:337
msgid "Custom button label"
msgstr "Etichetta del pulsante personalizzata"

#: includes/class-wc-payment-gateway-wcpay.php:332
msgid "Enter the height you would like the button to be in pixels. Width will always be 100%."
msgstr "Inserisci l'altezza che desideri abbia il pulsante in pixel. La larghezza deve essere sempre al 100%."

#: includes/class-wc-payment-gateway-wcpay.php:330
msgid "Button height"
msgstr "Altezza del pulsante"

#: includes/class-wc-payment-gateway-wcpay.php:326
msgid "Light-Outline"
msgstr "Contorno chiaro"

#: includes/class-wc-payment-gateway-wcpay.php:325
msgid "Light"
msgstr "Chiaro"

#: includes/class-wc-payment-gateway-wcpay.php:324
msgid "Dark"
msgstr "Scuro"

#: includes/class-wc-payment-gateway-wcpay.php:320
msgid "Select the button theme you would like to show."
msgstr "Seleziona il tema del pulsante che desideri mostrare."

#: includes/class-wc-payment-gateway-wcpay.php:318
msgid "Button theme"
msgstr "Tema del pulsante"

#: includes/class-wc-payment-gateway-wcpay.php:313
msgid "Donate"
msgstr "Fai una donazione"

#: includes/class-wc-payment-gateway-wcpay.php:312
msgid "Buy"
msgstr "Compra"

#: includes/class-wc-payment-gateway-wcpay.php:376
msgid "Default"
msgstr "Default"

#: includes/class-wc-payment-gateway-wcpay.php:307
msgid "Select the button type you would like to show."
msgstr "Seleziona il tipo di pulsante che desideri mostrare."

#: includes/class-wc-payment-gateway-wcpay.php:305
msgid "Button type"
msgstr "Tipo di pulsante"

#: includes/class-wc-payment-gateway-wcpay.php:300
msgid "If enabled, users will be able to pay using Apple Pay, Google Pay or the Payment Request API if supported by the browser."
msgstr "Se questa opzione è abilitata, gli utenti potranno pagare utilizzando Apple Pay, Google Pay o l'API di Richiesta di pagamento, se supportata dal browser."

#. translators: 1) br tag 2) Stripe anchor tag 3) Apple anchor tag
#: includes/class-wc-payment-gateway-wcpay.php:294
msgid "Enable payment request buttons (Apple Pay, Google Pay, and more). %1$sBy using Apple Pay, you agree to %2$s and %3$s's Terms of Service."
msgstr "Abilita i pulsanti di richiesta di pagamento (Apple Pay, Google Pay e molto altro). %1$sUsando Apple Pay, concordi con i Termini di servizio di %2$s e %3$s."

#: includes/class-wc-payment-gateway-wcpay.php:286
msgid "Payment request buttons"
msgstr "Pulsanti di richiesta di pagamento"

#: includes/class-wc-payment-gateway-wcpay.php:280
msgid "When enabled debug notes will be added to the log."
msgstr "Se il debug è abilitato, le note verranno aggiunte al log."

#: includes/class-wc-payment-gateway-wcpay.php:279
msgid "Debug log"
msgstr "Log di debug"

#: includes/class-wc-payment-gateway-wcpay.php:274
msgid "Simulate transactions using test card numbers."
msgstr "Simula le transazioni usando i numeri della carta di prova."

#: includes/class-wc-payment-gateway-wcpay.php:266
msgid "If enabled, users will be able to pay with a saved card during checkout. Card details are saved on our platform, not on your store."
msgstr "Se abilitata, gli utenti saranno in grado di pagare con una carta salvata durante il pagamento. I dettagli della carta sono salvati sulla nostra piattaforma, non nel tuo negozio."

#: includes/class-wc-payment-gateway-wcpay.php:264
msgid "Enable payment via saved cards"
msgstr "Abilita il pagamento tramite le carte salvate"

#: includes/class-wc-payment-gateway-wcpay.php:263
msgid "Saved cards"
msgstr "Carte salvate"

#: includes/class-wc-payment-gateway-wcpay.php:259
msgid "Charge must be captured within 7 days of authorization, otherwise the authorization and order will be canceled."
msgstr "L'addebito deve essere acquisito entro i 7 giorni dell'autorizzazione, altrimenti l'autorizzazione e l'ordine verranno annullati."

#: includes/class-wc-payment-gateway-wcpay.php:257
msgid "Issue an authorization on checkout, and capture later."
msgstr "Emetti un'autorizzazione al momento del pagamento e acquisiscila in un secondo momento."

#: includes/class-wc-payment-gateway-wcpay.php:256
msgid "Manual capture"
msgstr "Acquisizione manuale"

#: includes/class-wc-payment-gateway-wcpay.php:251
msgid "Edit the way your store name appears on your customers’ bank statements (read more about requirements <a>here</a>)."
msgstr "Modifica il modo in cui il nome del negozio viene visualizzato sugli estratti conto della banca del cliente (leggi di più sui requisiti <a>qui</a>)."

#: includes/class-wc-payment-gateway-wcpay.php:232
msgid "Enter your card details"
msgstr "Inserisci i dettagli della carta"

#: includes/class-payment-information.php:128
msgid "Invalid payment method. Please input a new card number."
msgstr "Metodo di pagamento non valido. Inserisci un nuovo numero di carta."

#. translators: %1: ID being fetched
#: includes/class-wc-payments-webhook-processing-service.php:668
msgid "%1$s not found in array"
msgstr "%1$s non trovato nella matrice"

#. translators: %1: the refund amount, %2: ID of the refund
#: includes/class-wc-payments-webhook-processing-service.php:275
msgid "A refund of %1$s was <strong>unsuccessful</strong> using WooCommerce Payments (<code>%2$s</code>)."
msgstr "Un rimborso di %1$s <strong>non è andato a buon fine</strong> usando WooCommerce Payments (<code>%2$s</code>)."

#. translators: %1: charge ID
#: includes/class-wc-payments-webhook-processing-service.php:265
#: includes/class-wc-payments-webhook-processing-service.php:341
#: includes/class-wc-payments-webhook-processing-service.php:533
#: includes/class-wc-payments-webhook-processing-service.php:565
#: includes/class-wc-payments-webhook-processing-service.php:596
msgid "Could not find order via charge ID: %1$s"
msgstr "Impossibile trovare l'ordine tramite l'ID di addebito: %1$s"

#: includes/admin/class-wc-rest-payments-tos-controller.php:109
msgid "ToS accept parameter is missing"
msgstr "Il parametro di accettazione ToS è mancante"

#: includes/admin/class-wc-payments-admin-settings.php:68
#: includes/admin/class-wc-payments-admin.php:413
msgid "Settings"
msgstr "Impostazioni"

#: includes/admin/class-wc-payments-admin.php:163
msgid "Disputes"
msgstr "Ricorsi"

#: client/transactions/index.tsx:82 client/transactions/list/index.tsx:383
msgid "Transactions"
msgstr "Transazioni"

#: client/onboarding-experiment/strings.ts:8
#: client/payment-methods/delete-modal.tsx:32
#: client/settings/fraud-protection/advanced-settings/index.tsx:44
msgid "WooCommerce Payments"
msgstr "WooCommerce Payments"

#: includes/admin/class-wc-payments-admin.php:239
msgid "Payments"
msgstr "Pagamenti"