{"translation-revision-date": "2025-04-10 15:54:11+0000", "generator": "GlotPress/4.0.1", "domain": "messages", "locale_data": {"messages": {"": {"domain": "messages", "plural-forms": "nplurals=2; plural=n != 1;", "lang": "it"}, "A plugin was successfully installed and activated.": ["Un plugin è stato installato e attivato correttamente."], "%1$s (%2$s) was successfully installed and activated.": ["%1$s (%2$s) è stato installato e attivato correttamente."], "Google for WooCommerce": ["Google per WooCommerce"], "WooPayments": ["WooPayments"], "Omnichannel for WooCommerce": ["Omni-canale per WooCommerce"], "TikTok for WooCommerce": ["TikTok per WooCommerce"], "Pinterest for WooCommerce": ["Pinterest per WooCommerce"], "Mercado Pago payments for WooCommerce": ["Pagamenti Mercado <PERSON> per WooCommerce"], "MailPoet": ["MailPoet"], "Could not %(actionType)s %(pluginName)s plugin, %(error)s": ["Impossibile %(actionType)s %(pluginName)s il plugin, %(error)s", "Impossibile %(actionType)s i seguenti plugin: %(pluginName)s con questi errori: %(error)s"], "Razorpay": ["Razorpay"], "Creative Mail for WooCommerce": ["Creative Mail per WooCommerce"], "WooCommerce Shipping & Tax": ["WooCommerce Shipping & Tax"], "WooCommerce Payfast": ["WooCommerce Payfast"], "WooCommerce Stripe": ["WooCommerce Stripe"], "WooCommerce PayPal": ["WooCommerce PayPal"], "WooCommerce ShipStation Gateway": ["WooCommerce ShipStation Gateway"], "There was a problem updating your settings.": ["Si è verificato un problema durante l'aggiornamento delle impostazioni."], "Plugins were successfully installed and activated.": ["I plugin sono stati installati correttamente e attivati."], "MM/DD/YYYY": ["DD/MM/YYYY"], "Facebook for WooCommerce": ["Facebook per WooCommerce"], "Mailchimp for WooCommerce": ["MailChimp for WooCommerce"], "Klarna Payments for WooCommerce": ["Klarna Payments per WooCommerce"], "Klarna Checkout for WooCommerce": ["<PERSON><PERSON><PERSON> Checkout for WooCommerce"], "Jetpack": ["Jetpack"]}}, "comment": {"reference": "assets/client/admin/data/index.js"}}