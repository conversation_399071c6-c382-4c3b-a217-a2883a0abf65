# Translation of Plugins - Duplicator &#8211; Backups &amp; Migration Plugin &#8211; Cloud Backups, Scheduled Backups, &amp; More - Stable (latest release) in Italian
# This file is distributed under the same license as the Plugins - Duplicator &#8211; Backups &amp; Migration Plugin &#8211; Cloud Backups, Scheduled Backups, &amp; More - Stable (latest release) package.
msgid ""
msgstr ""
"PO-Revision-Date: 2025-03-13 09:56:16+0000\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: GlotPress/4.0.1\n"
"Language: it\n"
"Project-Id-Version: Plugins - Duplicator &#8211; Backups &amp; Migration Plugin &#8211; Cloud Backups, Scheduled Backups, &amp; More - Stable (latest release)\n"

#: template/parts/notices/drm_multisite_msg.php:38
msgid "Upgrade Now!"
msgstr "Fai l'upgrade ora!"

#: template/parts/notices/drm_multisite_msg.php:30
msgctxt "1: name of pro plan, 2: name of elite plan"
msgid "By upgrading to the Elite or Pro plans you will unlock the ability to create backups and do advanced migrations on multi-site installations!"
msgstr "Passando ai piani Elite o Pro avrai la possibilità di creare backup e di eseguire migrazioni avanzate su installazioni multisito!"

#: src/Views/AdminNotices.php:340
msgid "Upgrade failed. Please check if you have the necessary permissions to activate plugins."
msgstr "Aggiornamento fallito. Verifica di avere le autorizzazioni necessarie per attivare i plugin."

#: src/Utils/DuplicatorPhpVersionCheck.php:105
msgid "Learn more about this change and how to upgrade"
msgstr "Approfondisci su questa modifica e come effettuare l'aggiornamento"

#: src/Utils/DuplicatorPhpVersionCheck.php:97
msgid "Please contact your hosting provider to upgrade your PHP version."
msgstr "Contatta il tuo fornitore di hosting per aggiornare la versione di PHP."

#: src/Utils/DuplicatorPhpVersionCheck.php:90
msgid ""
"While your current version of Duplicator will continue to work, \n"
"                        you'll need to upgrade your PHP version to receive future features, improvements, and security updates."
msgstr ""
"Anche se la tua versione attuale di Duplicator continuerà a funzionare, \n"
"                        dovrai aggiornare la tua versione di PHP per ricevere le funzionalità, i miglioramenti e gli aggiornamenti di sicurezza futuri."

#: src/Utils/DuplicatorPhpVersionCheck.php:77
msgid "Starting from <b>Duplicator %1$s</b>, Duplicator will require <b>PHP %2$s or higher</b> to receive new updates."
msgstr "A partire da <b>Duplicator %1$s</b>, Duplicator richiederà <b>PHP %2$s o superiore</b> per ricevere i nuovi aggiornamenti."

#: src/Utils/DuplicatorPhpVersionCheck.php:63
msgid "DUPLICATOR: Action Required - <b>PHP Version Update Needed</b>, Your site is running PHP version %s."
msgstr "DUPLICATOR: Azione necessaria - <b>Aggiornamento della versione di PHP necessario</b>, Il tuo sito utilizza la versione di PHP %s."

#: src/Ajax/ServicesEducation.php:203
msgid "Invalid license key."
msgstr "Chiave della licenza non valida."

#: views/tools/diagnostics/logging.php:215
msgid "Backup Logs"
msgstr "Log di backup"

#: views/parts/migration-almost-complete.php:31
msgid "Go to: Duplicator > Tools > General > Information > Utils and click the \"Remove Installation Files\" button"
msgstr "Vai su: Duplicator > Strumenti > Generale > Informazioni > Utilità e fai clic sul pulsante \"Rimuovi file di installazione\"."

#: views/packages/main/s2.scan3.php:565 views/settings/packages.php:256
msgid "Backup Engine"
msgstr "Backup Engine"

#: views/packages/main/s2.scan3.php:77
msgid "Skip Backup scan enabled"
msgstr "Salta la scansione di backup abilitata"

#: views/packages/main/s2.scan3.php:53
msgid "Backup Size"
msgstr "Dimensione del backup"

#: views/packages/main/s1.setup2.php:215
msgid "File Backup Encryption"
msgstr "Crittografia del backup dei file"

#: views/packages/main/s1.setup2.php:200
msgid "Backup Only the Database"
msgstr "Esegui il backup solo del database"

#: views/packages/main/packages.php:378
msgid "Delete Backups?"
msgstr "Eliminare i backup?"

#: views/packages/main/packages.php:299
msgid "Backup Details"
msgstr "Dettagli del backup"

#: views/packages/main/packages.php:260 views/packages/main/s3.build.php:154
msgid "Building Backup"
msgstr "Creazione del backup"

#: views/packages/main/packages.php:253
msgid "Backup created as daf file"
msgstr "Il backup è stato creato come file daf"

#: views/packages/main/packages.php:252
msgid "Backup created as zip file"
msgstr "Il backup è stato creato come file zip"

#: views/packages/main/packages.php:192
msgid "Select all Backups"
msgstr "Seleziona tutti i backup"

#: views/packages/main/packages.php:160 views/packages/main/packages.php:213
msgid "No Backups Found"
msgstr "Nessun backup trovato"

#: views/packages/main/packages.php:113
msgid "Backup Settings"
msgstr "Impostazioni di backup"

#: views/packages/main/controller.php:11
msgid "Backups Screen"
msgstr "Schermata dei backup"

#: views/packages/details/detail.php:553
msgid "BACKUP"
msgstr "BACKUP"

#: views/packages/details/detail.php:548
msgid "Backup File Links"
msgstr "Link ai file di backup"

#: views/packages/details/detail.php:531
msgid "View Backup Object"
msgstr "Visualizza l'oggetto di backup"

#: template/parts/filters/package_components.php:169
#: views/packages/details/detail.php:193 views/packages/details/detail.php:207
#: views/packages/details/detail.php:354 views/packages/main/packages.php:205
#: views/packages/main/packages.php:297 views/packages/main/s1.setup2.php:187
#: views/packages/main/s2.scan3.php:30 views/packages/main/s2.scan3.php:729
#: views/settings/packages.php:252
msgid "Backup"
msgstr "Backup"

#: template/parts/DashboardWidget/recently-packages.php:28
msgid "Recently Backups"
msgstr "Backup recenti"

#: template/parts/DashboardWidget/package-create-section.php:22
msgid "Backup creation"
msgstr "Creazione del backup"

#: template/mocks/settings/access/capabilities.php:88
msgid " - Backup Export "
msgstr " - Esportazione di backup "

#: template/mocks/settings/access/capabilities.php:38
msgid " - Backup Edit "
msgstr " - Modifica del backup "

#: template/mocks/settings/access/capabilities.php:28
msgid "Backup Read "
msgstr "Lettura backup "

#: template/admin_pages/settings/general/general.php:296
msgid "Reset Backups ?"
msgstr "Reimpostare i backup?"

#: template/admin_pages/settings/general/general.php:236
msgid "Backup scan"
msgstr "Scansione di backup"

#: template/admin_pages/settings/general/general.php:220
msgid "Reset Backups"
msgstr "Reimposta i backup"

#: src/Core/Bootstrap.php:547
msgid "Manage Backups"
msgstr "Gestisci i backup"

#: src/Core/Bootstrap.php:294 src/Core/Bootstrap.php:295
msgid "Schedule Backups"
msgstr "Programma i backup"

#: src/Core/Bootstrap.php:283 src/Core/Bootstrap.php:284
msgid "Import Backups"
msgstr "Importa i backup"

#: template/parts/cross_promotion/list.php:25
msgid "Enjoying Duplicator? Check out a couple of our other free plugins..."
msgstr "Ti piace Duplicator? Dai un'occhiata ad altri plugin gratuiti..."

#: src/Utils/ExtraPlugins/ExtraPluginsMng.php:523
msgid "Uncanny Automator is the easiest and most powerful way to automate your WordPress site with no code. Build automations in minutes that connect your WordPress plugins, sites and apps together using billions of recipe combinations."
msgstr "Uncanny Automator è il modo più semplice e potente per automatizzare il tuo sito WordPress senza bisogno di codice. Crea automazioni in pochi minuti che collegano tra loro i tuoi plugin, siti e applicazioni WordPress usando miliardi di combinazioni di ricette."

#: src/Utils/ExtraPlugins/ExtraPluginsMng.php:520
msgid "Uncanny Automator"
msgstr "Uncanny Automator"

#: src/Utils/ExtraPlugins/ExtraPluginsMng.php:511
msgid "Efficiently manage your website’s content directly from the WordPress admin with Search & Replace Everything by WPCode. This tool is essential for site migrations, content updates, or any situation where batch text and image replacements are needed."
msgstr "Gestisci in modo efficiente i contenuti del tuo sito web direttamente dall'amministrazione di WordPress con Search & Replace Everything di WPCode. Questo strumento è essenziale per le migrazioni del sito, per gli aggiornamenti dei contenuti o per tutte le situazioni in cui è necessario sostituire in blocco testi e immagini."

#: src/Utils/ExtraPlugins/ExtraPluginsMng.php:508
msgid "Search & Replace Everything"
msgstr "Cerca e sostituisci tutto"

#: src/Utils/ExtraPlugins/ExtraPluginsMng.php:499
msgid "Future proof your WordPress customizations with the most popular code snippet management plugin for WordPress. Trusted by over 1,500,000+ websites for easily adding code to WordPress right from the admin area."
msgstr "Proteggi le tue personalizzazioni di WordPress con il più popolare plugin di gestione degli snippet di codice per WordPress. Più di 1.500.000 siti web lo hanno già scelto per aggiungere con facilità codice a WordPress direttamente dall'area di amministrazione."

#: src/Utils/ExtraPlugins/ExtraPluginsMng.php:496
msgid "WPCode"
msgstr "WPCode"

#: views/tools/diagnostics/inc.data.php:68
msgid "The ZipArchive extensions is required to create the diagnostic data."
msgstr "L'estensione ZipArchive è necessaria per creare i dati di diagnostica."

#: views/tools/diagnostics/inc.data.php:63
msgid "Downloads a ZIP archive with all relevant diagnostic information."
msgstr "Scarica un archivio ZIP con tutte le informazioni di diagnostica rilevanti."

#: views/tools/diagnostics/inc.data.php:59
msgid "Get Diagnostic Data"
msgstr "Ottieni i dati di diagnostica"

#: views/tools/diagnostics/inc.data.php:14
msgid "Utils"
msgstr "Utilità"

#: template/parts/tools/server_settings_table.php:74
msgid "Info"
msgstr "Info"

#: template/admin_pages/welcome/intro.php:139
msgid "Names, slugs, versions, and if active or not"
msgstr "Nomi, slug, versioni e se è attivo o no"

#: template/admin_pages/welcome/intro.php:131
msgid "To ensure compatibility and avoid conflicts with your installed plugins and themes."
msgstr "Per garantire la compatibilità ed evitare conflitti con i plugin e i temi installati."

#: template/admin_pages/welcome/intro.php:129
msgid "Plugins & Themes List"
msgstr "Elenco dei plugin e dei temi"

#: template/admin_pages/welcome/intro.php:126
msgid "View Plugins & Themes List"
msgstr "Visualizza l'elenco dei plugin e dei temi"

#: template/admin_pages/welcome/intro.php:115
msgid "Current plugin & SDK versions, and if active or uninstalled"
msgstr "Versioni attuali del plugin e SDK, se sono attive o disinstallate"

#: template/admin_pages/welcome/intro.php:113
msgid "View Basic Plugin Info"
msgstr "Visualizza le informazioni di base del plugin"

#: template/admin_pages/welcome/intro.php:103
msgid "Homepage URL & title, WP & PHP versions, and site language"
msgstr "URL e titolo della homepage, versioni di WP e PHP e lingua del sito"

#: template/admin_pages/welcome/intro.php:93
msgid "To provide additional functionality that's relevant to your website, avoid WordPress or PHP version incompatibilities that can break your website, and recognize which languages & regions the plugin should be translated and tailored to."
msgstr "Per fornire funzionalità aggiuntive rilevanti per il tuo sito web, evita le incompatibilità di versione di WordPress o PHP che possono danneggiare il tuo sito web. Riconosci le lingue e le regioni in cui il plugin deve essere tradotto e adattato."

#: template/admin_pages/welcome/intro.php:91
msgid "Basic Website Info"
msgstr "Informazioni di base sul sito web"

#: template/admin_pages/welcome/intro.php:88
msgid "View Basic Website Info"
msgstr "Visualizza le informazioni di base del sito web"

#: template/admin_pages/welcome/intro.php:77
msgid "Your WordPress user's: first & last name, and email address"
msgstr "Il tuo utente WordPress: nome, cognome e indirizzo email"

#: template/admin_pages/welcome/intro.php:68
msgid "Never miss important updates, get security warnings before they become public knowledge, and receive notifications about special offers and awesome new features."
msgstr "Non perdere mai gli aggiornamenti più importanti: ricevi avvisi di sicurezza prima che diventino di dominio pubblico, notifiche sulle offerte speciali e sulle nuove fantastiche funzionalità."

#: template/admin_pages/welcome/intro.php:66
msgid "Basic Profile Info"
msgstr "Informazioni di base sul profilo"

#: template/admin_pages/welcome/intro.php:63
msgid "View Basic Profile Info"
msgstr "Visualizza le informazioni di base sul profilo"

#: template/admin_pages/welcome/intro.php:55
msgid "This will allow Duplicator to"
msgstr "Questo permetterà a Duplicator di"

#: template/admin_pages/welcome/intro.php:40
msgid "Allow & Continue"
msgstr "Consenti e continua"

#: template/admin_pages/welcome/intro.php:30
msgid "Opt in to get email notifications for security & feature updates, educational content, and occasional offers, and to share some basic WordPress environment info. This will help us make the plugin more compatible with your site and better at doing what you need it to."
msgstr "Iscriviti per ricevere notifiche via email per aggiornamenti di sicurezza e funzionalità, contenuti didattici e offerte occasionali e per condividere alcune informazioni di base sull'ambiente WordPress. Questo ci aiuterà a migliorare e a rendere il plugin più compatibile con il tuo sito."

#: template/admin_pages/welcome/intro.php:26
msgid "Never miss an important update"
msgstr "Non perdere mai un aggiornamento importante"

#: src/Utils/ZipArchiveExtended.php:357
msgid "ZipArchive PHP module is not installed/enabled. The current Backup cannot be opened."
msgstr "Il modulo PHP ZipArchive non è installato/abilitato. Il backup corrente non può essere aperto."

#: src/Utils/Support/SupportToolkit.php:60
msgid "Failed to create zip file"
msgstr "Impossibile creare un file zip"

#: classes/class.server.php:724
msgid "Archive "
msgstr "Archivio "

#: classes/class.server.php:716
msgid "Original "
msgstr "Originale "

#: classes/class.server.php:708
msgid "Target root path"
msgstr "Percorso principale di destinazione"

#: classes/class.server.php:694
msgid "Path Not Found"
msgstr "Percorso non trovato"

#: classes/class.server.php:631
msgid "Shell (exec)"
msgstr "Shell (exec)"

#: classes/class.server.php:625
msgid "open_basedir"
msgstr "open_basedir"

#: classes/class.server.php:614
msgctxt "%1$s = \"is dynamic\" or \"value is fixed\" based on settings"
msgid "(default) - %1$s"
msgstr "(predefinito) - %1$s"

#: classes/class.server.php:588
msgid "PHP SAPI"
msgstr "PHP SAPI"

#: classes/class.server.php:493
msgctxt "%1$s and %2$s are the opening and closing anchor tags"
msgid "This is a %1$sWordPress Setting%2$s"
msgstr "Questa è una %1$simpostazione di WordPress%2$s"

#: classes/class.server.php:447
msgid ""
"Note: This value is the physical servers hard-drive allocation.\n"
"                    On shared hosts check your control panel for the \"TRUE\" disk space quota value."
msgstr ""
"Nota: Questo valore corrisponde all'allocazione del disco rigido del server fisico.\n"
"                    Negli host condivisi, controlla il tuo pannello di controllo per conoscere il valore della quota di spazio su disco \"VERO\"."

#: classes/class.server.php:442
msgid "%1$s%% -- %2$s from %3$s"
msgstr "%1$s%% -- %2$s da %3$s"

#: classes/class.server.php:429
msgid "URLs Info"
msgstr "Info sugli URL"

#: classes/class.server.php:422
msgid "URL "
msgstr "URL "

#: classes/class.server.php:414
msgid "Paths Info"
msgstr "Info sui percorsi"

#: views/tools/diagnostics/support.php:97
msgctxt "1 and 2 are opening and closing anchor or link tags"
msgid "Free Users %1$sSupport Forum%2$s"
msgstr "Utenti gratuiti %1$sForum di supporto%2$s"

#: views/settings/packages.php:351
msgctxt "Leave _installer.php part as is translate only [name], [hash] and [time]"
msgid "[name]_[hash]_[time]_installer.php"
msgstr "[name]_[hash]_[time]_installer.php"

#: views/settings/packages.php:312
msgid "Network Keep Alive"
msgstr "Keep-Alive della rete"

#: views/settings/about-info.php:75
msgid "Support Duplicator <br/> with a 5 star review!"
msgstr "Supporta Duplicator <br/> con una recensione a 5 stelle!"

#: views/parts/migration-clean-installation-files.php:87
msgctxt "%1$s and %2$s are <a> tags"
msgid "Show your support with a %1$s5 star review%2$s! We would be thrilled if you could!"
msgstr "Mostra il tuo sostegno con una %1$srecensione a 5 stelle%2$s: ci piacerebbe molto se lo facessi!"

#: classes/class.server.php:615 views/packages/main/s3.build.php:490
msgid "value is fixed"
msgstr "il valore è fisso"

#: classes/class.server.php:615 views/packages/main/s3.build.php:490
msgid "is dynamic"
msgstr "è dinamico"

#: views/packages/main/s3.build.php:348
msgctxt "1: opening link tag, 2: closing link tag (<a></a>)"
msgid ""
"Note: The DupArchive engine will generate an archive.daf file. This file is very similar to a .zip except that it can \n"
"                                    only be extracted by the installer.php file or the %1$scommandline extraction tool%2$s."
msgstr ""
"Nota: Il motore DupArchive genera un file archivio.daf. Questo file è molto simile a un .zip, ma può essere \n"
"                                    estratto solo dal file installer.php o dallo %1$sstrumento di estrazione da riga di comando%2$s."

#: views/packages/main/s3.build.php:321
msgctxt "1: opening link tag, 2: closing link tag (<a></a>)"
msgid ""
"Note: DupArchive on Duplicator only supports sites up to 500MB.  If your site is over 500MB then use a file filter on \n"
"                                    step 1 to get the size below 500MB or try the other options mentioned below.  Alternatively, you may want to consider \n"
"                                    %1$sDuplicator Pro%2$s, which is capable of migrating sites much larger than 500MB."
msgstr ""
"Nota: DupArchive su Duplicator supporta solo siti fino a 500MB. Se il tuo sito supera i 500MB, usa un filtro per i file \n"
"                                    al punto 1 per ridurre le dimensioni a 500MB oppure prova le altre opzioni indicate di seguito. In alternativa, puoi prendere in considerazione \n"
"                                    %1$sDuplicator Pro%2$s, che è in grado di migrare siti di dimensioni molto superiori a 500MB."

#: views/packages/main/s2.scan3.php:1121
msgid "Size:"
msgstr "Dimensione:"

#: views/packages/main/s2.scan3.php:1117
msgid "Rows:"
msgstr "Righe:"

#: views/packages/main/s2.scan3.php:1113
msgid "Uppercase:"
msgstr "Maiuscolo:"

#: views/packages/main/s2.scan2.php:156
msgid ""
"Note: Please change the %1$s filters if you wish to include the WordPress core files \n"
"                    otherwise the data will have to be manually copied to the new location for the site to function properly."
msgstr ""
"Nota: modifica i filtri di %1$s se desideri includere i file del nucleo di WordPress \n"
"                    altrimenti i dati dovranno essere copiati manualmente nella nuova posizione affinché il sito funzioni correttamente."

#: views/packages/main/s2.scan2.php:150
msgid "files"
msgstr "file"

#: views/packages/main/s2.scan2.php:95
msgctxt "1 and 2 are <a> tags"
msgid ""
"It's possible one could get the Backup to install but it may require custom manual effort. \n"
"                To get support and the advanced installer processing required for managed host support we encourage users to %1$supgrade to Duplicator Pro%2$s.\n"
"                Pro has more sophisticated Backup and installer logic and accounts for odd configurations associated with managed hosts."
msgstr ""
"È possibile ottenere l'installazione del Backup, ma potrebbe richiedere uno sforzo manuale personalizzato. \n"
"                Per ottenere l'assistenza e l'elaborazione avanzata del programma di installazione necessaria per il supporto degli host gestiti, invitiamo gli utenti a %1$sfare l'aggiornamento a Duplicator Pro%2$s.\n"
"                Pro ha una logica più sofisticata per il backup e l'installatore e tiene conto delle strane configurazioni associate agli host gestiti."

#: views/packages/main/s1.setup1.php:122
msgctxt "1 and 2 are <a> tags"
msgid "ZipArchive extension is required or %1$sSwitch to DupArchive%2$s to by-pass this requirement."
msgstr "È necessaria l'estensione ZipArchive o %1$sPassa a DupArchive%2$s per bypassare questo requisito."

#: views/packages/details/detail.php:250
msgid "Select All"
msgstr "Seleziona tutto"

#: views/packages/details/detail.php:85
msgid "[close all]"
msgstr "[chiudi tutto]"

#: views/packages/details/detail.php:84
msgid "[open all]"
msgstr "[apri tutto]"

#: views/packages/details/controller.php:29
msgctxt "%1$s represents the Backup name"
msgid "Backup Details &raquo; %1$s"
msgstr "Dettagli del backup &raquo; %1$s"

#: template/parts/filters/package_components.php:168
msgid "media"
msgstr "media"

#: template/parts/filters/package_components.php:151
#: template/parts/filters/package_components.php:170
msgid "clear"
msgstr "cancella"

#: template/parts/filters/package_components.php:136
msgid "Enable"
msgstr "Abilita"

#: template/mocks/recovery/recovery.php:21
msgid "Need more help?"
msgstr "Hai bisogno di aiuto?"

#: template/mocks/recovery/recovery.php:20
msgid "Quickly restore this site to a specific point in time."
msgstr "Ripristina rapidamente questo sito a un momento specifico nel tempo."

#: template/mocks/import/import.php:26
msgctxt "%1$s and %2$s are opening and closing span tags"
msgid "Step %1$s1%2$s of 2: Upload Backup"
msgstr "Passo %1$s1%2$s di 2: caricare il backup"

#: template/mail/email_summary.php:73
msgid "Hi there!"
msgstr "Ciao!"

#: template/admin_pages/about_us/lite_vs_pro/main.php:27
msgid "<strong>Lite</strong> vs <strong>Pro</strong>"
msgstr "<strong>Lite</strong> vs <strong>Pro</strong>"

#: src/Lite/Requirements.php:50
msgctxt "%1$s and %2$s are <a> tags"
msgid ""
"Can't enable Duplicator LITE if the PRO version is enabled. Please deactivate Duplicator PRO, \n"
"                    then reactivate LITE version from the %1$splugins page%2$s."
msgstr ""
"Non è possibile abilitare Duplicator LITE se è abilitata la versione PRO. Disattiva Duplicator PRO\n"
"                    quindi riattiva la versione LITE dalla %1$spagina dei plugin%2$s."

#: src/Core/Bootstrap.php:416
msgid "Unable to copy"
msgstr "Impossibile copiare"

#: src/Core/Bootstrap.php:415
msgid "copied to clipboard"
msgstr "copiato negli appunti"

#: src/Core/Bootstrap.php:414
msgid "Copy to clipboard"
msgstr "Copia negli appunti"

#: src/Core/Bootstrap.php:405
msgid "Failed to load help content!"
msgstr "Non è stato possibile caricare il contenuto di supporto!"

#: src/Core/Bootstrap.php:383
msgid "Email subscription failed with message: "
msgstr "L'iscrizione all'email è fallita con il messaggio: "

#: src/Core/Bootstrap.php:382
msgid "Failed &#10007"
msgstr "Fallito &#10007"

#: src/Core/Bootstrap.php:381
msgid "Subscribing..."
msgstr "Abbonamento in corso..."

#: src/Core/Bootstrap.php:380
msgid "Subscribed &#10003"
msgstr "Abbonato &#10003"

#: src/Controllers/AboutUsController.php:55
msgid "Failure"
msgstr "Fallimento"

#: src/Controllers/AboutUsController.php:54
msgid "Loading..."
msgstr "In caricamento ..."

#: classes/utilities/class.u.php:310
msgctxt "sec. stands for seconds"
msgid "%.2f sec."
msgstr "%.2f sec."

#: template/parts/help/main.php:237
msgid "Upgrade to Duplicator Pro to access our world class customer support."
msgstr "Fai l'upgrade a Duplicator Pro per accedere al nostro supporto clienti di prim'ordine."

#: template/parts/help/main.php:231
msgid "View All Documentation"
msgstr "Visualizza tutta la documentazione"

#: template/parts/help/main.php:224
msgid "Browse documentation, reference material, and tutorials for Duplicator."
msgstr "Sfoglia la documentazione, il materiale di referer e le esercitazioni per Duplicator."

#: template/parts/help/main.php:222
msgid "View Documentation"
msgstr "Visualizza la documentazione"

#: template/parts/help/main.php:212
msgid "Related Articles"
msgstr "Articoli correlati"

#: template/parts/help/main.php:208
msgid "No results found"
msgstr "Nessun risultato trovato"

#: template/parts/help/main.php:206
msgid "Search"
msgstr "Cerca"

#: assets/js/duplicator/dup.util.php:83
msgid "RESPONSE SUCCESS"
msgstr "SUCCESSO DELLA RISPOSTA"

#: assets/js/duplicator/dup.util.php:21
msgid "wait ..."
msgstr "attendi..."

#: views/settings/packages.php:163
msgctxt "%1s and %2s represents the opening and closing HTML tags for an anchor or link"
msgid "See the %1$shost list%2$s for reliable access to mysqldump."
msgstr "Consulta %1$sl'elenco di host%2$s per avere un accesso affidabile a mysqldump."

#: views/settings/packages.php:132
msgctxt "%1s and %2s represents the opening and closing HTML tags for an anchor or link"
msgid "Please visit our recommended %1$shost list%2$s for reliable access to mysqldump."
msgstr "Visita il nostro %1$selenco di host raccomandati%2$s per avere un accesso affidabile a mysqldump."

#: views/parts/migration-clean-installation-files.php:71
msgctxt "%1$s and %2$s are <a> tags"
msgid "For more details please visit the FAQ link %1$sWhich files need to be removed after an install?%2$s"
msgstr "Per maggiori dettagli visita il link FAQ %1$sQuali file devono essere rimossi dopo un'installazione?%2$s"

#: views/packages/main/s3.build.php:423
msgctxt "1: opening link, 2: closing link"
msgid "Complete the Backup build and follow the %1$sQuick Start Two-Part Install Instructions%2$s"
msgstr "Completa la creazione del backup e segui le %1$sistruzioni per l'installazione rapida in due fasi%2$s."

#: views/packages/main/s2.scan3.php:589
msgctxt "%1$s and %2$s represent opening and closing anchor tags"
msgid "- Perform a two part install as %1$sdescribed in the documentation%2$s."
msgstr "- Esegui un'installazione in due parti come %1$sdescritto nella documentazione%2$s."

#: views/packages/main/s2.scan3.php:139
msgctxt "%1$s and %2$s are <a> tags"
msgid "Consider trying multi-threaded support in %1$sDuplicator Pro%2$s."
msgstr "Puoi provare il supporto multi-thread in %1$sDuplicator Pro%2$s."

#: views/packages/main/s2.scan3.php:126
msgctxt "%1$s and %2$s are <a> tags"
msgid "See the FAQ link to adjust this hosts timeout limits: %1$sWhat can I try for Timeout Issues?%2$s"
msgstr "Consulta il link delle FAQ per regolare i limiti di timeout di questo host: %1$sCosa posso fare per risolvere i problemi di timeout?%2$s"

#: views/packages/details/detail.php:256
msgctxt "%1$s and %2$s are opening and closing <a> tags"
msgid "A copy of the database.sql and installer.php files can both be found inside of the archive.zip/daf file.  Download and extract the Backup file to get a copy of the installer which will be named 'installer-backup.php'. For details on how to extract a archive.daf file please see: %1$sHow to work with DAF files and the DupArchive extraction tool?%2$s"
msgstr "Una copia dei file database.sql e installer.php si trova all'interno del file archivio.zip/daf. Scarica ed estrai il file di backup per ottenere una copia del programma di installazione che si chiamerà \"installer-backup.php\". Per maggiori dettagli su come estrarre un file archive.daf, consulta: %1$sCome lavorare con i file DAF e lo strumento di estrazione DupArchive?%2$s"

#: views/packages/details/controller.php:53
msgctxt "%1, %3 and %2, %4 are replaced with <a> and </a> respectively"
msgid "For help visit the %1$sFAQ%2$s and %3$sresources page%4$s."
msgstr "Per ricevere assistenza %1$svisita la pagina delle FAQ%2$s e %3$sla pagina delle risorse%4$s."

#: views/packages/details/controller.php:43
msgctxt "%1 and %2 are replaced with <a> and </a> respectively"
msgid "This Backup contains an error. Please review the %1$sBackup log%2$s for details."
msgstr "Questo backup contiene un errore. Per maggiori dettagli, consulta il %1$sregistro di backup%2$s."

#: template/admin_pages/settings/general/general.php:310
msgctxt "1 and 2 are opening and closing <a> tags"
msgid "AJAX error encountered when resetting Backups. Please see %1$sthis FAQ entry%2$s for possible resolutions."
msgstr "Errore AJAX riscontrato durante la reimpostazione dei backup. %1$sLeggi questa voce delle FAQ%2$s per le possibili soluzioni."

#: src/Views/AdminNotices.php:455
msgctxt "%1$s and %2$s are <a> tags"
msgid "<strong>RECOMMENDATION:</strong> Add export capability to your role. See FAQ: %1$sWhy is the Duplicator/Packages menu missing from my admin menu?%2$s"
msgstr "<strong>RACCOMANDAZIONE:</strong> Aggiungi la capacità di esportazione al tuo ruolo. Vedi le FAQ: %1$sPerché il menu Duplicator/Packages non compare nel mio menu di amministrazione?%2$s"

#: template/admin_pages/settings/general/email_summary.php:45
#: template/mail/email_summary.php:179
msgctxt "%1s and %2s are opening and closing link tags to the documentation."
msgid "Learn %1show to disable%2s."
msgstr "Scopri %1scome disabilitare%2s."

#: template/mail/email_summary.php:165
msgctxt "%s is an <a> tag with a link to the current website."
msgid "This email was auto-generated and sent from %s."
msgstr "Questa email è stata generata automaticamente e inviata da %s."

#: template/mail/email_summary.php:153
msgid "No backups were created in the past week."
msgstr "Non sono stati creati backup nella scorsa settimana."

#: src/Core/Bootstrap.php:273 src/Core/Bootstrap.php:274
#: template/mail/email_summary.php:131 views/packages/details/controller.php:76
#: views/packages/main/s3.build.php:125 views/settings/controller.php:36
msgid "Backups"
msgstr "Backup"

#: template/mail/email_summary.php:128
msgid "State"
msgstr "Stato"

#: template/mail/email_summary.php:123
msgid "Below are the total numbers of successful and failed backups."
msgstr "Qui sotto trovi il numero totale di backup riusciti e falliti."

#: template/mail/email_summary.php:110
msgctxt "%s and %s are opening and closing link tags to the pricing page."
msgid "To unlock scheduled backups, remote storages and many other features, %supgrade to PRO%s!"
msgstr "Per sbloccare i backup programmati, gli archivi remoti e molte altre funzioni, %saggiorna alla versione PRO%s!"

#: template/mail/email_summary.php:99
msgid "With Duplicator Pro you can store backups in Google Drive, Amazon S3, OneDrive, Dropbox, or any SFTP/FTP server for added protection."
msgstr "Con Duplicator Pro puoi archiviare i backup in Google Drive, Amazon S3, OneDrive, Dropbox o qualsiasi server SFTP/FTP per una maggiore protezione."

#: template/mail/email_summary.php:93
msgid "With Duplicator Pro you can create fully automatic backups! Schedule your preferred intervals for backups - daily, weekly, or monthly and never worry about data loss again!"
msgstr "Con Duplicator Pro puoi creare backup completamente automatici! Programma gli intervalli che preferisci per i backup - giornalieri, settimanali o mensili - e non preoccuparti mai più della perdita di dati!"

#: template/mail/email_summary.php:88
msgid "Did you know?"
msgstr "Lo sapevi?"

#: template/mail/email_summary.php:77
msgctxt "%s is the frequency of email summaries."
msgid "Here's a quick overview of your backups in the past %s."
msgstr "Ecco una rapida panoramica dei tuoi backup negli ultimi %s."

#: template/admin_pages/settings/general/usage_tracking_tooltip.php:90
msgid "<b>Backups infos:</b> Information about the Backups created and the type of components included."
msgstr "<b>Informazioni sui backup:</b> Informazioni sui backup creati e sul tipo di componenti inclusi."

#: template/admin_pages/settings/general/usage_tracking_tooltip.php:81
msgid ""
"<b>Site info:</b> General information about the site such as database, file size, number of users, and sites in case it is a multisite. \n"
"            This is useful for us to understand the critical issues of Backup creation."
msgstr ""
"<b>Informazioni sul sito:</b> Informazioni generali su chi siamo: database, dimensione dei file, numero di utenti e siti nel caso si tratti di un sito multisito. \n"
"            Questo è utile per capire le criticità della creazione del backup."

#: template/admin_pages/settings/general/usage_tracking_tooltip.php:73
msgid "<b>Plugins and Themes infos:</b> so we can figure out which ones can generate compatibility errors with Duplicator."
msgstr "<b>Informazioni su plugin e temi:</b> ci serve per capire quali possono generare errori di compatibilità con Duplicator."

#: template/admin_pages/settings/general/usage_tracking_tooltip.php:64
msgid ""
"<b>Duplicator Version:</b> so we know which versions of Duplicator are potentially responsible for issues when we get bug reports, \n"
"            allowing us to identify issues and release solutions much faster."
msgstr ""
"<b>Versione di Duplicator:</b> ci serve per sapere quali versioni di Duplicator sono potenzialmente responsabili di problemi quando riceviamo report di bug, \n"
"            ci permette di identificare i problemi e rilasciare le soluzioni molto più velocemente."

#: template/admin_pages/settings/general/usage_tracking_tooltip.php:56
msgid "<b>MySQL Version:</b> so we know which versions of MySQL to support and test against for our custom tables."
msgstr "<b>Versione di MySQL:</b> così sappiamo quali versioni di MySQL supportare e testare per le nostre tabelle personalizzate."

#: template/admin_pages/settings/general/usage_tracking_tooltip.php:48
msgid "<b>WordPress Version:</b> so we know which WordPress versions to support and test against."
msgstr "<b>Versione di WordPress:</b> così possiamo sapere quali versioni di WordPress supportare e provare."

#: template/admin_pages/settings/general/usage_tracking_tooltip.php:40
msgid "<b>PHP Version:</b> so we know which PHP versions we have to test against (no one likes whitescreens or log files full of errors)."
msgstr "<b>Versione PHP:</b> così possiamo sapere quali versioni PHP dobbiamo provare (nessuno ama le schermate bianche o i file di log pieni di errori)."

#: template/admin_pages/settings/general/usage_tracking_tooltip.php:35
msgid "Below is the list of information that Duplicator collects as part of the usage tracking:"
msgstr "Di seguito è riportato l'elenco delle informazioni che Duplicator raccoglie come parte del monitoraggio dell'utilizzo:"

#: template/admin_pages/settings/general/usage_tracking_tooltip.php:33
msgid "This allows us to continuously improve our product as well as our Q&A / testing process."
msgstr "Questo ci permette di migliorare continuamente il nostro prodotto e il nostro processo di Q&A e di test."

#: template/admin_pages/settings/general/usage_tracking_tooltip.php:26
msgid ""
"Usage tracking for Duplicator helps us better understand our users and their website needs by looking \n"
"            at a range of server and website environments."
msgstr ""
"Il tracciamento dell'utilizzo di Duplicator ci aiuta a capire meglio i nostri utenti e le loro esigenze sul sito web, esaminando \n"
"            una serie di ambienti di server e siti web."

#: template/admin_pages/settings/general/usage_tracking_tooltip.php:20
msgid "No information about storage or Backup's content are sent."
msgstr "Non vengono inviate informazioni sull'archiviazione o sul contenuto del backup."

#: template/admin_pages/settings/general/usage_tracking_tooltip.php:19
msgid "All information sent to the server is anonymous."
msgstr "Tutte le informazioni inviate al server sono anonime."

#: template/admin_pages/settings/general/general.php:170
msgid "Check this option to hide plugin announcements and update details."
msgstr "Seleziona questa opzione per nascondere gli annunci e i dettagli di aggiornamento del plugin."

#: template/admin_pages/settings/general/general.php:161
msgid "Hide Announcements"
msgstr "Nascondi annunci"

#: template/admin_pages/settings/general/general.php:152
msgid "Usage Tracking"
msgstr "Tracciamento dell'utilizzo"

#: template/admin_pages/settings/general/general.php:149
msgid "Enable usage tracking"
msgstr "Abilita il tracciamento dell'utilizzo"

#: template/admin_pages/settings/general/general.php:139
msgid "Usage statistics are hardcoded disallowed."
msgstr "Le statistiche di utilizzo sono disabilitate in codifica fissa."

#: template/admin_pages/settings/general/general.php:135
msgid "Usage statistics"
msgstr "Statistiche di utilizzo"

#: template/admin_pages/settings/general/email_summary.php:37
msgctxt "%1s and %2s are the opening and close <a> tags to the summary preview link"
msgid "You can view the email summary example %1shere%2s."
msgstr "Puoi visualizzare un esempio di resoconto tramite email %1squi%2s."

#: template/admin_pages/settings/general/email_summary.php:24
msgid "Frequency"
msgstr "Frequenza"

#: template/admin_pages/settings/general/email_summary.php:20
msgid "Email Summary"
msgstr "Resoconto tramite email"

#: src/Views/AdminNotices.php:410
msgid ""
"You created over %d backups with Duplicator. Great job! If you can spare a minute, \n"
"                                please help us by leaving a five star review on WordPress.org."
msgstr ""
"Hai creato oltre %d backup con Duplicator. Ottimo lavoro! Se puoi, dedicaci un minuto \n"
"                                e lasciaci una recensione a cinque stelle su WordPress.org."

#: src/Utils/Email/EmailSummary.php:145
msgid "week"
msgstr "settimana"

#: src/Utils/Email/EmailSummary.php:142
msgid "month"
msgstr "mese"

#: src/Utils/Email/EmailSummary.php:140
msgid "day"
msgstr "giorno"

#: src/Utils/Email/EmailSummary.php:126
msgid "Monthly"
msgstr "Mensile"

#: src/Utils/Email/EmailSummary.php:125
msgid "Weekly"
msgstr "Settimanale"

#: src/Utils/Email/EmailSummary.php:124
msgid "Daily"
msgstr "Giornaliera"

#: src/Utils/Email/EmailSummary.php:123
msgid "Never"
msgstr "Mai"

#: src/Utils/Email/EmailSummaryBootstrap.php:71
msgctxt "%s is the site domain"
msgid "Your Weekly Duplicator Summary for %s"
msgstr "Il tuo riepilogo settimanale di Duplicator per %s"

#: src/Utils/Email/EmailSummary.php:108
msgid "Failed"
msgstr "Falliti"

#: src/Utils/Email/EmailSummary.php:103
msgid "Successful"
msgstr "Riusciti"

#: src/Utils/CronUtils.php:42
msgid "Once a Month"
msgstr "Una volta al mese"

#: src/Utils/CronUtils.php:37
msgid "Once a Week"
msgstr "Una volta alla settimana"

#: src/Utils/CronUtils.php:32
msgid "Once a Day"
msgstr "Una volta al giorno"

#: src/Controllers/StorageController.php:117
msgid "S3-Compatible (Generic) Cloudian, Cloudn, Connectria, Constant, Exoscal, Eucalyptus, Nifty, Nimbula, Minio, etc..."
msgstr "S3-Compatible (Generic) Cloudian, Cloudn, Connectria, Constant, Exoscal, Eucalyptus, Nifty, Nimbula, Minio, ecc..."

#: src/Controllers/StorageController.php:111
#: src/Controllers/StorageController.php:112
msgid "Wasabi"
msgstr "Wasabi"

#: src/Controllers/StorageController.php:106
#: src/Controllers/StorageController.php:107
msgid "Dream Objects"
msgstr "Dream Objects"

#: src/Controllers/StorageController.php:101
#: src/Controllers/StorageController.php:102
msgid "Vultr Object Storage"
msgstr "Archiviazione oggetti Vultr"

#: src/Controllers/StorageController.php:96
#: src/Controllers/StorageController.php:97
msgid "DigitalOcean Spaces"
msgstr "DigitalOcean Spaces"

#: src/Controllers/StorageController.php:91
#: src/Controllers/StorageController.php:92
msgid "Cloudflare R2"
msgstr "Cloudflare R2"

#: src/Controllers/StorageController.php:86
#: src/Controllers/StorageController.php:87
msgid "Back Blaze"
msgstr "Back Blaze"

#: src/Controllers/StorageController.php:81
#: src/Controllers/StorageController.php:82
msgid "Google Cloud Storage"
msgstr "Google Cloud Storage"

#: views/settings/license.php:75
msgid "One click Upgrade to Pro"
msgstr "Passa a Pro con un clic"

#: views/settings/license.php:74
msgid "Paste license key here"
msgstr "Incolla qui la chiave della licenza"

#: views/settings/license.php:72
msgid "Already purchased? Simply enter your license key below to enable <b>Duplicator PRO!</b>"
msgstr "Già acquistato? Inserisci semplicemente la tua chiave della licenza qui sotto per abilitare <b>Duplicator PRO!</b>"

#: views/packages/main/s2.scan3.php:679
msgctxt "%1$s represents the memory limit value (e.g. 256MB)"
msgid "If you want to build the Backup with mysqldump, increase the PHP <b>memory_limit</b> value in your php.ini file to at least %1$s."
msgstr "Se vuoi creare il backup con mysqldump, aumenta il valore di PHP <b>memory_limit</b> nel tuo file php.ini ad almeno %1$s."

#: views/packages/main/s2.scan3.php:665
msgctxt "%1$s and %2$s represent opening and closing anchor tags"
msgid "Please change the setting SQL Mode to PHP Code. You can do that by opening %1$sDuplicator Pro > Settings > Backups.%2$s"
msgstr "Cambia l'impostazione da Modalità SQL a Codice PHP. Puoi farlo aprendo %1$sDuplicator Pro > Impostazioni > Backup.%2$s"

#: views/packages/main/s2.scan3.php:650
msgid "The database size is larger than the PHP memory_limit value. This can lead into issues when building a Backup, during which the system can run out of memory. To fix this issue please consider doing one of the below mentioned recommendations."
msgstr "La dimensione del database è superiore al valore di PHP memory_limit. Questo può causare problemi durante la creazione di un backup, durante il quale il sistema può esaurire la memoria. Per correggere questo problema, prendi in considerazione una delle raccomandazioni riportate di seguito."

#: views/packages/main/s2.scan3.php:647
msgid "The database size exceeds the allowed mysqldump size limit."
msgstr "La dimensione del database supera il limite di dimensione mysqldump consentito."

#: views/packages/main/s2.scan3.php:633
msgctxt "1$s and 2$s represent opening and closing anchor tags"
msgid "If you encounter any issues with mysqldump please change the setting SQL Mode to PHP Code. You can do that by opening %1$sDuplicator Pro > Settings > Backups.%2$s"
msgstr "Se riscontri dei problemi con mysqldump, modifica l'impostazione Modalità SQL in Codice PHP. Puoi farlo aprendo %1$sDuplicator Pro > Impostazioni > Backup.%2$s"

#: views/packages/main/s2.scan3.php:629
msgid "The database size is within the allowed mysqldump size limit."
msgstr "La dimensione del database rientra nel limite di dimensione consentito per mysqldump."

#: views/packages/main/s2.scan3.php:616
msgid "Mysqldump memory check"
msgstr "Controllo della memoria Mysqldump"

#: template/parts/Notifications/single-message.php:21
msgid "Watch video"
msgstr "Guarda il video"

#: template/parts/Notifications/main.php:33
msgid "Next message"
msgstr "Messaggio successivo"

#: template/parts/Notifications/main.php:29
msgid "Previous message"
msgstr "Messaggio precedente"

#: template/parts/Notifications/main.php:20
msgid "Notifications"
msgstr "Notifiche"

#: template/parts/filters/package_components.php:236
msgctxt "%1$s and %2$s represents the opening and closing HTML tags for an anchor or link."
msgid "The <b>Media Only</b> and <b>Custom</b> options are not included in Duplicator Lite. To enable advanced options please %1$supgrade to Pro%2$s."
msgstr "Le opzioni <b>Solo media</b> e <b>Personalizzato</b> non sono incluse in Duplicator Lite. Per abilitare le opzioni avanzate, %1$saggiorna a Pro%2$s."

#: template/parts/filters/package_components.php:227
msgid "database only quick start"
msgstr "avvio rapido solo database"

#: template/parts/filters/package_components.php:216
msgid "<b>Install Time:</b><br> When installing a database only Backup please visit the "
msgstr "<b>Tempo di installazione:</b><br> Quando si installa un backup solo database, visita la pagina "

#: template/parts/filters/package_components.php:204
msgid "For example, if you have WordPress 5.6 on this site and you copy this site's database to a host that has WordPress 5.8 files then the source code of the files  will not be in sync with the database causing possible errors. This can also be true of plugins and themes.  When moving only the database be sure to know the database will be compatible with ALL source code files. Please use this advanced feature with caution!"
msgstr "Ad esempio, se su questo sito hai WordPress 5.6 e copi il database del sito su un host che ha i file di WordPress 5.8, il codice sorgente dei file non sarà sincronizzato con il database causando possibili errori. Questo può valere anche per i plugin e i temi. Se sposti solo il database, assicurati che il database sia compatibile con TUTTI i file del codice sorgente. Usa questa caratteristica avanzata con cautela!"

#: template/parts/filters/package_components.php:191
msgid "<b><i class='fa fa-exclamation-circle'></i> Notice:</b><br/> Installing only the database over an existing site may have unintended consequences.  Be sure to know the state of your system before installing the database without the associated files.  "
msgstr "<b><i class='fa fa-exclamation-circle'></i> Avviso:</b><br/> L'installazione del solo database su un sito esistente può avere conseguenze indesiderate. Assicurati di conoscere lo stato del tuo sistema prima di installare il database senza i file associati.  "

#: template/parts/filters/package_components.php:138
msgid "Path Filters"
msgstr "Filtri di percorso"

#: template/parts/filters/package_components.php:126
msgid "Other"
msgstr "Altro"

#: template/parts/filters/package_components.php:112
msgid "Only Active Themes"
msgstr "Solo i temi attivi"

#: template/parts/filters/package_components.php:106
msgid "Themes"
msgstr "Temi"

#: template/parts/filters/package_components.php:99
msgid "Only Active Plugins"
msgstr "Solo i plugin attivi"

#: template/parts/filters/package_components.php:93
msgid "Plugins"
msgstr "Plugin"

#: template/parts/filters/package_components.php:51
msgid "Backup Components (Pro feature)"
msgstr "Componenti di backup (funzionalità Pro)"

#: template/parts/filters/package_components.php:49
msgid "Components"
msgstr "Componenti"

#: template/parts/filters/package_components.php:41
msgid "File extension filters allow you to exclude files with certain file extensions from the Backup e.g. zip;rar;pdf etc. Enter the file extensions you want to exclude from the Backup as a semicolon (;) seperated list."
msgstr "I filtri per l'estensione dei file ti permettono di escludere dal backup i file con determinate estensioni, per esempio zip, rar, pdf, ecc. Inserisci le estensioni dei file che vuoi escludere dal backup in un elenco separato da punto e virgola (;)."

#: template/parts/filters/package_components.php:39
msgid "File filters allow you to exclude files and folders from the Backup. To enable path and extension filters check the checkbox. Enter the full path of the files and folders you want to exclude from the Backup as a semicolon (;) seperated list."
msgstr "I filtri sui file ti permettono di escludere file e cartelle dal backup. Per abilitare i filtri per percorso ed estensione, seleziona il checkbox. Inserisci il percorso completo dei file e delle cartelle che vuoi escludere dal backup come elenco separato da punto e virgola (;)."

#: template/parts/filters/package_components.php:28
msgid "Backup components allow you to include/exclude differents part of your WordPress installation in the Backup.</br></br><b>Database</b>: Include the database in the Backup.</br><b>Plugins</b>: Include the plugins in the Backup. With the 'active only' option enabled, only active plugins will be included in the Backup.</br><b>Themes</b>: Include the themes in the Backup. With the 'active only' option enabled, only active themes will be included in the Backup.</br><b>Media</b>: Include the 'uploads' folder.</br><b>Other</b>: Include non-WordPress files and folders in the root directory.</br>"
msgstr "I componenti di backup ti permettono di includere/escludere diverse parti della tua installazione di WordPress nel backup.</br></br><b>Database</b>: Include il database nel backup.</br><b>Plugin</b>: Include i plugin nel backup. Con l'opzione \"solo attivi\" abilitata, solo i plugin attivi saranno inclusi nel backup.</br><b>Temi</b>: Include i temi nel backup. Con l'opzione \"solo attivo\" abilitata, solo i temi attivi saranno inclusi nel backup.</br><b>Media</b>: Includi la cartella \"uploads\".</br><b>Altro</b>: Include i file e le cartelle non WordPress nella directory principale.</br>"

#: src/Libs/OneClickUpgrade/ConnectSkin.php:35
msgid "There was an error installing Duplicator Pro. Please try again."
msgstr "Si è verificato un errore durante l'installazione di Duplicator Pro. Riprova."

#: src/Core/Bootstrap.php:396
msgid "Check the license key and try again, if the error persists proceed with manual activation."
msgstr "Controlla la chiave della licenza e prova di nuovo, se l'errore persiste procedi con l'attivazione manuale."

#: src/Core/Bootstrap.php:395
msgid "Message: "
msgstr "Messaggio: "

#: src/Core/Bootstrap.php:394
msgid "Failed to activate license for this website."
msgstr "Attivazione della licenza per questo sito fallita."

#: src/Ajax/ServicesEducation.php:280
msgid "Invalid license JSON data."
msgstr "Dati JSON della licenza non validi."

#: views/settings/controller.php:48
msgid "Access"
msgstr "Accesso"

#: template/mocks/settings/access/content-popup.php:15
msgid "Elevate your backup capabilities with advanced permissions, allowing for precise control over the creation, exportation, restoration, and management of control settings. Enjoy granular access control to ensure only authorized users can perform these critical functions."
msgstr "Migliora le tue capacità di backup con autorizzazioni avanzate che consentono un controllo preciso sulla creazione, l'esportazione, il ripristino e la gestione delle impostazioni di controllo. Il controllo granulare degli accessi garantisce che solo gli utenti autorevoli possano eseguire queste funzioni critiche."

#: template/mocks/settings/access/capabilities.php:124
msgid "Advanced Backup Permissions are not available in Duplicator Lite!"
msgstr "Le autorizzazioni avanzate per il backup non sono disponibili in Duplicator Lite!"

#: template/mocks/settings/access/capabilities.php:108
msgid " -  - Manage License Settings "
msgstr " - Gestisci le impostazioni della licenza "

#: template/mocks/settings/access/capabilities.php:98
msgid " - Manage Settings "
msgstr " - Gestisci le impostazioni "

#: template/mocks/settings/access/capabilities.php:78
msgid " - - Backup Import "
msgstr " - Importazione di backup "

#: template/mocks/settings/access/capabilities.php:68
msgid " - Restore Backup "
msgstr " - Ripristino Backup "

#: template/mocks/settings/access/capabilities.php:58
msgid " - - Manage Storages "
msgstr " -- Gestisci gli archivi "

#: template/mocks/settings/access/capabilities.php:48
msgid " - - Manage Schedules "
msgstr " -- Gestire i programmi "

#: template/mocks/settings/access/capabilities.php:32
#: template/mocks/settings/access/capabilities.php:42
#: template/mocks/settings/access/capabilities.php:52
#: template/mocks/settings/access/capabilities.php:62
#: template/mocks/settings/access/capabilities.php:72
#: template/mocks/settings/access/capabilities.php:82
#: template/mocks/settings/access/capabilities.php:92
#: template/mocks/settings/access/capabilities.php:102
#: template/mocks/settings/access/capabilities.php:112
msgid "Administrator"
msgstr "Amministratore"

#: template/mocks/settings/access/capabilities.php:23
msgid "It is not possible to self remove the manage settings capabilities."
msgstr "Non è possibile rimuovere autonomamente le capacità di gestione delle impostazioni."

#: template/mocks/settings/access/capabilities.php:21
msgid "Some capabilities depend on others so If you select for example storage capability automatically the Backup read and Backup edit capabilities are assigned"
msgstr "Alcune capacità dipendono da altre. Per esempio, la capacità di archiviazione, le capacità di lettura e modifica del backup vengono assegnate automaticamente."

#: template/mocks/settings/access/capabilities.php:20
msgid "By default, all permissions are provided only to administrator users."
msgstr "Di default, tutte le autorizzazioni sono fornite solo agli utenti amministratori."

#: template/mocks/settings/access/capabilities.php:19
msgid "Select the user roles and/or users that are allowed to manage different aspects of Duplicator."
msgstr "Seleziona i ruoli utente e/o gli utenti che possono gestire diversi aspetti di Duplicator."

#: template/mocks/settings/access/capabilities.php:15
msgid "Roles and Permissions"
msgstr "Ruoli e permessi"

#: src/Controllers/AboutUsController.php:217
msgid "Enjoy granular access control to ensure only authorized users can perform these critical functions."
msgstr "Il controllo degli accessi è granulare e garantisce che solo gli utenti autorevoli possano eseguire queste funzioni critiche."

#: src/Controllers/AboutUsController.php:215 src/Utils/Upsell.php:52
#: src/Utils/Upsell.php:82 template/mocks/settings/access/capabilities.php:123
msgid "Advanced Backup Permissions"
msgstr "Autorizzazioni avanzate per il backup"

#. Plugin URI of the plugin
#. Author URI of the plugin
#: duplicator.php
msgid "https://duplicator.com/"
msgstr "https://duplicator.com/"

#: views/tools/diagnostics/support.php:87
msgid "Having a problem with your back up or migrations? Upgrade to get our Premium Support."
msgstr "Hai un problema con il backup o le migrazioni? Fai l'upgrade per ottenere il nostro Supporto Premium."

#: views/tools/diagnostics/support.php:84
msgid "Premium Support"
msgstr "Supporto premium"

#: views/settings/license.php:62
msgid "As a valued Duplicator Lite user you receive <strong>%1$d%% off</strong>, automatically applied at checkout!"
msgstr "Come utente di Duplicator Lite riceverai <strong>%1$d%% di sconto su</strong>, applicato automaticamente al momento del pagamento!"

#: views/settings/license.php:45
msgid "To unlock more features consider <strong><a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">upgrading to PRO</a></strong>."
msgstr "Per sbloccare più caratteristiche considera <strong><a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">l'aggiornamento a PRO</a></strong>."

#: views/settings/license.php:41
msgid "You're using Duplicator Lite - no license needed. Enjoy!"
msgstr "Stai usando Duplicator Lite - non è necessaria alcuna licenza. Buon divertimento!"

#: views/packages/main/s1.setup2.php:407
msgid "Available with Duplicator Pro!"
msgstr "Disponibile con Duplicator Pro!"

#: template/parts/Education/subscribe-form.php:29
msgid "Get tips and product updates straight to your inbox."
msgstr "Ricevi consigli e aggiornamenti sui prodotti direttamente nella tua casella di posta."

#: src/Core/Bootstrap.php:379 template/parts/Education/subscribe-form.php:26
msgid "Subscribe"
msgstr "Iscriviti"

#: template/parts/Education/subscribe-form.php:24
msgid "Email Address"
msgstr "Indirizzo email"

#: template/parts/Education/static-popup.php:31
msgid "Upgrade to Duplicator Pro Now"
msgstr "Aggiorna a Duplicator Pro ora"

#: template/parts/Education/packages-bottom-bar.php:36
msgid "Upgrade Now & Save!"
msgstr "Fai ora l'upgrade e salva!"

#: template/parts/Education/packages-bottom-bar.php:27
msgid "Upgrade to Pro to Unlock..."
msgstr "Passa a Pro per sbloccare..."

#: template/parts/Education/did-you-know-blurb.php:24
#: views/packages/main/s1.setup2.php:359
msgid "Upgrade To Pro"
msgstr "Passa a Pro"

#: template/parts/Education/did-you-know-blurb.php:22
msgid "Did you know Duplicator Pro has: %s?"
msgstr "Sapevi che Duplicator Pro ha: %s?"

#: template/parts/Education/callout-cta.php:72
msgid "<strong>Bonus:</strong> Duplicator Lite users get <span class=\"green\">%1$d%% off regular price</span>,automatically applied at checkout."
msgstr "<strong>Bonus:</strong> gli utenti di Duplicator Lite ricevono <span class=\"green\">%1$d%% di sconto sul prezzo normale</span>, applicato automaticamente al momento del pagamento."

#: template/parts/Education/callout-cta.php:66
msgid "Get Duplicator Pro Today and Unlock all the Powerful Features »"
msgstr "Ottieni Duplicator Pro oggi stesso e sblocca tutte le sue potenti caratteristiche\"."

#: template/parts/Education/callout-cta.php:50
msgid "Pro Features:"
msgstr "Funzionalità Pro:"

#. translators: %s - star icons.
#: template/parts/Education/callout-cta.php:34
msgid "We know that you will truly love Duplicator. It has over 4000+ five star ratings (%s) and is active on over 1 million websites."
msgstr "Sappiamo che Duplicator ti piacerà moltissimo. Ha più di 4000 valutazioni a cinque stelle (%s) ed è attivo su oltre 1 milione di siti web."

#: template/parts/Education/callout-cta.php:24
msgid "Thanks for being a loyal Duplicator Lite user. Upgrade to Duplicator Pro to unlock all the awesome features and experience why Duplicator is consistently rated the best WordPress migration plugin."
msgstr "Grazie per aver scelto di usare Duplicator Lite. Passa a Duplicator Pro per sbloccare tutte le fantastiche caratteristiche e scoprire perché Duplicator è sempre considerato il miglior plugin per la migrazione di WordPress."

#: template/parts/Education/callout-cta.php:21
#: template/parts/Notifications/main.php:24
msgid "Dismiss this message"
msgstr "Chiudi questo messaggio"

#: template/parts/DashboardWidget/sections-section.php:103
msgid "Not set"
msgstr "Non impostato"

#: template/mocks/recovery/recovery.php:16
#: template/parts/DashboardWidget/sections-section.php:98
msgid "Recovery Point"
msgstr "Punto di recupero"

#: template/parts/DashboardWidget/sections-section.php:82
msgid "%s Template"
msgid_plural "%s Templates"
msgstr[0] "%s Modello"
msgstr[1] "%s Template"

#: template/parts/DashboardWidget/sections-section.php:65
msgid "%s Storage"
msgid_plural "%s Storages"
msgstr[0] "%s Immagazzinamento"
msgstr[1] "%s Archivi"

#: template/parts/DashboardWidget/sections-section.php:40
msgid "%s Schedule"
msgid_plural "%s Schedules"
msgstr[0] "%s Programma"
msgstr[1] "%s Programmazioni"

#: template/parts/DashboardWidget/recommended-section.php:50
msgid "Dismiss recommended plugin"
msgstr "Ignora il plugin raccomandato"

#: template/parts/cross_promotion/item.php:23
#: template/parts/DashboardWidget/recommended-section.php:42
msgid "Install"
msgstr "Installa"

#: template/parts/DashboardWidget/recommended-section.php:36
msgid "Recommended Plugin:"
msgstr "Plugin suggeriti:"

#: template/parts/DashboardWidget/recently-packages.php:47
msgid "Backups: %1$d, Failures: %2$d"
msgstr "Backup: %1$d, Non riusciti: %2$d"

#: template/parts/DashboardWidget/package-create-section.php:31
msgid "Last backup:"
msgstr "Ultimo backup:"

#: template/parts/DashboardWidget/package-create-section.php:23
msgid "This will create a new Backup. If a Backup is currently running then this button will be disabled."
msgstr "Questo creerà un nuovo backup. Se è in corso un backup, questo pulsante sarà disabilitato."

#: template/mocks/transfer/transfer.php:231
msgid "Remote storages are not available in Duplicator Lite!"
msgstr "Gli archivi remoti non sono disponibili in Duplicator Lite!"

#: template/mocks/transfer/transfer.php:230
msgid "Manually transfer backups to remote storages!"
msgstr "Trasferisci manualmente i backup su archivi remoti!"

#: template/mocks/transfer/content-popup.php:14
msgid "With manual transfers you can upload your backup to remote storages even after you have created them."
msgstr "Con i trasferimenti manuali puoi caricare i tuoi backup su archivi remoti anche dopo averli creati."

#: template/mocks/templates/templates.php:194
msgid "Easily customize your backups with templates!"
msgstr "Personalizza facilmente i tuoi backup con i template!"

#: template/mocks/templates/content-popup.php:22
msgid "Instead of manually configuring the same themes and plugins over and over, just configure one site and bundle it into a Duplicator Backup. Install the Backup to create a pre-configured site on as many locations as you want!"
msgstr "Invece di configurare manualmente sempre gli stessi temi e gli stessi plugin, configura un sito e raggruppalo in un bundle di Duplicator Backup. Installa il backup per creare un sito preconfigurato su tutte le posizioni che vuoi!"

#: template/mocks/templates/content-popup.php:14
msgid "If you install the same theme, plugins or content on all your WordPress sites then Duplicator can save you a lot of time."
msgstr "Se installi lo stesso tema, gli stessi plugin o gli stessi contenuti su tutti i tuoi siti WordPress, Duplicator può farti risparmiare molto tempo."

#: template/mocks/storage/storage.php:146
msgid "Total: %s"
msgstr "Totale: %s"

#: template/mocks/storage/storage.php:93
msgid "Add New"
msgstr "Aggiungi"

#: template/mocks/storage/storage.php:62
msgid "Back up to Dropbox, FTP, Google Drive, OneDrive, Amazon S3 or Amazon S3 compatible for safe off-site storage."
msgstr "Esegui il backup su Dropbox, FTP, Google Drive, OneDrive, Amazon S3 o Amazon S3 compatibile per un'archiviazione off-site sicura."

#: template/mocks/storage/storage.php:61
msgid "Remote Cloud Backups is a PRO feature"
msgstr "I backup remoti in cloud sono una caratteristica PRO"

#: template/mocks/storage/popup.php:17
msgid "Store to Multiple Endpoints with Duplicator Pro"
msgstr "Archivia su più endpoint con Duplicator Pro"

#: template/mocks/schedule/schedules.php:280
msgid "Duplicator Lite does not support scheduled backups!"
msgstr "Duplicator Lite non supporta i backup programmati!"

#: template/mocks/schedule/schedules.php:279
msgid "Automate your workflow with scheduled backups!"
msgstr "Automatizza il flusso di lavoro con i backup programmati!"

#: template/mocks/schedule/content-popup.php:23
msgid "Supported Cloud Storage: Google Drive, Dropbox, Microsoft One Drive, Amazon S3 (or any compatible S3 service), and FTP/SFTP Storage."
msgstr "Archiviazione cloud supportata: Google Drive, Dropbox, Microsoft One Drive, Amazon S3 (o qualsiasi altro servizio S3 compatibile) e archiviazione FTP/SFTP."

#: template/mocks/schedule/content-popup.php:14
msgid "Scheduled Backups provide peace of mind and ensure that critical data can be quickly and easily restored in the event of a disaster or loss. Duplicator Pro supports Hourly, Daily, Weekly and Monthly scheduled backups."
msgstr "I backup programmati garantiscono la massima tranquillità e assicurano che i dati critici possano essere ripristinati rapidamente e facilmente in caso di incidente o perdita. Duplicator Pro supporta backup programmati orari, quotidiani, settimanali e mensili."

#: template/mocks/recovery/recovery.php:118
msgid "Recovery Points are not supported in Duplicator Lite!"
msgstr "I Punti di ripristino non sono supportati in Duplicator Lite!"

#: template/mocks/recovery/recovery.php:117
msgid "Rollback your sites with Recovery Points!"
msgstr "Ripristina i tuoi siti con Punti di ripristino!"

#: template/mocks/import/import.php:211
msgid "Overwrite a WordPress site with Drag and Drop Import!"
msgstr "Sovrascrivi un sito WordPress con l'importazione \"trascina e rilascia\"!"

#: template/mocks/import/content-popup.php:18
msgid "In addition to the <a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">classic installer method</a> on an empty site, Duplicator Pro now supports Drag and Drop migrations and site restores! Simply drag the bundled site Backup to the site you wish to overwrite."
msgstr "Oltre al <a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">classico metodo di installazione</a> su un sito vuoto, Duplicator Pro ora supporta le migrazioni e i ripristini del sito tramite funzionalità drag&amp;drop! Basta trascinare il Backup del sito in bundle sul sito che vuoi sovrascrivere."

#: template/admin_pages/welcome/upgrade-cta.php:36
#: template/mocks/storage/popup.php:37 template/mocks/storage/storage.php:68
#: views/tools/diagnostics/support.php:91
msgid "Upgrade Now"
msgstr "Aggiorna ora"

#: template/admin_pages/welcome/upgrade-cta.php:21
msgid "Upgrade to PRO"
msgstr "Aggiorna a PRO"

#: template/admin_pages/welcome/testimonials.php:44
msgid "Duplicator Pro is the best <b>WordPress migration & backup</b> plugin I have ever used. I will be recommending this plugin to everyone I can."
msgstr "Duplicator Pro è il miglior plugin <b>per la migrazione e il backup di WordPress</b> che abbia mai utilizzato. Raccomanderò questo plugin a tutti."

#: template/admin_pages/welcome/testimonials.php:27
msgid "It walked me step-by-step through the process of migrating a WordPress website. If you want to save a ton of time with <b>WP migration</b>, I very much recommend this plugin!"
msgstr "Mi ha guidato passo dopo passo attraverso il processo di migrazione di un sito WordPress. Se vuoi risparmiare un sacco di tempo con <b>WP migration</b>, ti consiglio vivamente questo plugin!"

#: template/admin_pages/welcome/testimonials.php:20
msgid "Testimonials"
msgstr "Testimonianze"

#: template/admin_pages/welcome/intro.php:23
msgid "Willie the Duplicator mascot"
msgstr "Willie, la mascotte di Duplicator"

#: template/admin_pages/welcome/footer.php:34 template/parts/help/main.php:243
msgid "Upgrade to Duplicator Pro"
msgstr "Passa a Duplicator Pro"

#: template/admin_pages/welcome/footer.php:25
msgid "Create Your First Backup"
msgstr "Crea il tuo primo backup"

#: template/admin_pages/welcome/features.php:101
msgid "See All Features"
msgstr "Guarda tutte le caratteristiche"

#: template/admin_pages/welcome/features.php:91
msgid "Duplicator Pro supports multisite network backup & migration. You can even install  a subsite as a standalone site."
msgstr "Duplicator Pro supporta il backup e la migrazione in rete di multisiti. Puoi anche installare un sottosito come sito indipendente."

#: template/admin_pages/welcome/features.php:89
msgid "Multisite Support"
msgstr "Supporto multisito"

#: template/admin_pages/welcome/features.php:82
msgid "Duplicator Pro has developed a new way to package backups especially tailored for larger site. No server timeouts or other restrictions."
msgstr "Duplicator Pro ha sviluppato un nuovo modo di confezionare i backup, appositamente studiato per i siti di grandi dimensioni. Non ci sono timeout del server o altre restrizioni."

#: template/admin_pages/welcome/features.php:73
msgid "Use file and database filters to pick and choose exactly what you want to backup or transfer. No bloat!"
msgstr "Usa i filtri per i file e i database per scegliere esattamente ciò di cui vuoi fare il backup o il trasferimento. Niente gonfiore!"

#: template/admin_pages/welcome/features.php:64
msgid "Direct Backup import from source server or cloud storage using URL. No need to download the Backup to your desktop machine first."
msgstr "Importa direttamente il backup dal server di origine o dal cloud storage tramite URL. Non è necessario scaricare prima il backup sul computer desktop."

#: template/admin_pages/welcome/features.php:56
#: views/packages/main/s1.setup2.php:354
msgid "Protect and secure the Backup file with industry-standard AES-256 encryption."
msgstr "Proteggi e metti in sicurezza il file di backup con la crittografia AES-256, standard del settore."

#: template/admin_pages/welcome/features.php:47
#: template/mocks/recovery/content-popup.php:14
msgid "Recovery Points provides protection against mistakes and bad updates by letting you quickly rollback your system to a known, good state."
msgstr "Punti di ripristino fornisce protezione contro errori e aggiornamenti sbagliati, consentendo di ripristinare rapidamente il sistema a uno stato noto e valido."

#: template/admin_pages/welcome/features.php:39
msgid "Back up to Dropbox, FTP, Google Drive, OneDrive, or Amazon S3 and more for safe storage."
msgstr "Esegui il backup su Dropbox, FTP, Google Drive, OneDrive o Amazon S3 e altro ancora per un'archiviazione sicura."

#: template/admin_pages/welcome/features.php:37
msgid "Cloud Backups"
msgstr "Backup in cloud"

#: template/admin_pages/welcome/features.php:30
msgid "Ensure that important data is regularly and consistently backed up, allowing for quick and efficient recovery in case of data loss."
msgstr "Assicurati che il backup dei dati importanti venga eseguito in modo normale e costante, consentendo un recupero rapido ed efficiente in caso di perdita di dati."

#: template/admin_pages/welcome/features.php:22
msgid "Duplicator is both easy to use and extremely powerful. We have tons of helpful features that allow us to give you everything you need from a backup & migration plugin."
msgstr "Duplicator è facile da usare ed estremamente potente. Abbiamo un sacco di caratteristiche utili che ci permettono di offrirti tutto ciò che ti serve da un plugin per il backup e la migrazione."

#: template/admin_pages/welcome/features.php:21
msgid "Duplicator Features"
msgstr "Caratteristiche di Duplicator"

#: template/admin_pages/about_us/tabs.php:25
msgid "Lite vs Pro"
msgstr "Lite vs Pro"

#: template/admin_pages/about_us/tabs.php:21
msgid "Getting Started"
msgstr "Guida introduttiva"

#: template/admin_pages/about_us/lite_vs_pro/main.php:78
msgid "Not Available"
msgstr "Non disponibile"

#: template/admin_pages/about_us/lite_vs_pro/main.php:77
#: template/admin_pages/about_us/lite_vs_pro/main.php:87
msgid "Included"
msgstr "Incluso"

#: template/admin_pages/about_us/lite_vs_pro/main.php:58
msgid "Pro"
msgstr "Pro"

#: template/admin_pages/about_us/lite_vs_pro/main.php:53
msgid "Lite"
msgstr "Lite"

#: template/admin_pages/about_us/lite_vs_pro/main.php:39
msgid "Get the most out of Duplicator by upgrading to Pro and unlocking all of the powerful features."
msgstr "Ottieni il massimo da Duplicator passando a Pro e sbloccando tutte le sue potenti caratteristiche."

#: template/admin_pages/about_us/getting_started/get_pro.php:101
#: template/admin_pages/about_us/lite_vs_pro/main.php:112
msgid "Bonus: Duplicator Lite users get <span class=\"price-20-off\">%1$d%% off regular price</span>, automatically applied at checkout."
msgstr "Bonus: gli utenti di Duplicator Lite ottengono <span class=\"price-20-off\">%1$d%% di sconto sul prezzo normale</span>, applicato automaticamente al momento del pagamento."

#: template/admin_pages/about_us/getting_started/get_pro.php:93
#: template/admin_pages/about_us/lite_vs_pro/main.php:104
msgid "Get Duplicator Pro Today and Unlock all the Powerful Features"
msgstr "Ottieni Duplicator Pro oggi stesso e sblocca tutte le sue potenti caratteristiche"

#. translators: %s - stars.
#: template/admin_pages/about_us/getting_started/get_pro.php:48
msgid "We know that you will truly love Duplicator. It has over <strong>4000+ five star ratings</strong> (%s) and is active on over 1 million websites."
msgstr "Sappiamo che Duplicator ti piacerà moltissimo. Ha oltre <strong>4000 valutazioni a cinque stelle</strong> (%s) ed è attivo su più di 1 milione di siti web."

#: template/admin_pages/about_us/getting_started/get_pro.php:31
msgid "Thanks for being a loyal Duplicator Lite user. <strong>Upgrade to Duplicator Pro</strong> to unlock all the awesome features and experience<br>why Duplicator is consistently rated the best WordPress migration plugin."
msgstr "Grazie per aver scelto di usare Duplicator Lite. <strong>Fai l'upgrade a Duplicator Pro</strong> per sbloccare tutte le fantastiche caratteristiche e scopri<br>perché Duplicator è sempre considerato il miglior plugin per la migrazione di WordPress."

#: template/admin_pages/about_us/getting_started/get_pro.php:25
#: template/parts/Education/callout-cta.php:22
msgid "Get Duplicator Pro and Unlock all the Powerful Features"
msgstr "Ottieni Duplicator Pro e sblocca tutte le sue potenti caratteristiche"

#: template/admin_pages/about_us/getting_started/first_package.php:65
msgid "How to Migrate to a New Site"
msgstr "Come migrare a un nuovo sito"

#: template/admin_pages/about_us/getting_started/first_package.php:56
msgid "How to Create a Backup"
msgstr "Come creare un backup"

#: template/admin_pages/about_us/getting_started/first_package.php:36
msgid "In the Backups page, the Backups list will be empty because there are no Backups yet. To create a new Backup, click on the Create New button, and this will launch the Backup Creation Wizard."
msgstr "Nella pagina Backup, l'elenco dei backup sarà vuoto perché non ci sono ancora backup. Per creare un nuovo backup, fai clic sul pulsante Crea nuovo per avviare la procedura guidata di creazione del backup."

#: template/admin_pages/about_us/getting_started/first_package.php:32
msgid "To begin, you’ll need to be logged into the WordPress admin area. Once there, click on Duplicator in the admin sidebar to go the Backups page."
msgstr "Per iniziare, devi connetterti all'area di amministrazione di WordPress. Una volta lì, fai clic su Duplicator nella barra laterale dell'amministrazione per accedere alla pagina dei backup."

#: template/admin_pages/about_us/getting_started/first_package.php:28
msgid "Want to get started creating your first Backup with Duplicator? By following the step by step instructions in this walkthrough, you can easily create a backup or migration."
msgstr "Vuoi iniziare a creare il tuo primo backup con Duplicator? Seguendo le istruzioni passo passo di questa guida, potrai creare facilmente un backup o una migrazione."

#: template/admin_pages/about_us/getting_started/first_package.php:25
msgid "Creating Your First Backup"
msgstr "Creazione del tuo primo backup"

#: template/admin_pages/about_us/about_us/info.php:51
msgid "The Awesome Motive Team"
msgstr "Il team di Awesome Motive"

#: template/admin_pages/about_us/about_us/info.php:49
msgid "The Awesome Motive Team photo"
msgstr "La foto del team Awesome Motive"

#: template/admin_pages/about_us/about_us/info.php:43
msgid "Yup, we know a thing or two about building awesome products that customers love."
msgstr "Sì, effettivamente sappiamo come costruire prodotti eccezionali che i nostri clienti amano."

#. translators: %1$s - WPBeginner URL; %2$s - OptinMonster URL; %3$s -
#. MonsterInsights URL.
#: template/admin_pages/about_us/about_us/info.php:21
msgid "Duplicator is brought to you by the same team that’s behind the largest WordPress resource site, <a href=\"%1$s\" target=\"_blank\" rel=\"noopener noreferrer\">WPBeginner</a>, the most popular lead-generation software, <a href=\"%2$s\" target=\"_blank\" rel=\"noopener noreferrer\">OptinMonster</a>, the best WordPress analytics plugin, <a href=\"%3$s\" target=\"_blank\" rel=\"noopener noreferrer\">MonsterInsights</a>, and more!"
msgstr "Duplicator è stato creato dal team che ha creato il più grande sito di risorse per WordPress, <a href=\"%1$s\" target=\"_blank\" rel=\"noopener noreferrer\">WPBeginner</a>, il più popolare software di lead-generation, <a href=\"%2$s\" target=\"_blank\" rel=\"noopener noreferrer\">OptinMonster</a>, il miglior plugin di analisi per WordPress, <a href=\"%3$s\" target=\"_blank\" rel=\"noopener noreferrer\">MonsterInsights</a>, e molto altro ancora!"

#: template/admin_pages/about_us/about_us/info.php:14
msgid "Our goal is to take the pain out of creating backups, migrations, and make it easy."
msgstr "Il nostro obiettivo è quello di eliminare il problema della creazione di backup e migrazioni e renderlo più semplice."

#: template/admin_pages/about_us/about_us/info.php:9
msgid "Over the years, we found that most WordPress backup and migration plugins were unreliable, buggy, slow, and very hard to use. So we started with a simple goal: build a WordPress backup and migration plugin that’s both easy and powerful."
msgstr "Nel corso degli anni, abbiamo scoperto che la maggior parte dei plugin per il backup e la migrazione di WordPress erano inaffidabili, pieni di bug, lenti e molto difficili da usare. Siamo partiti guidati da un semplice obiettivo: creare un plugin per il backup e la migrazione di WordPress che fosse facile e potente."

#: template/admin_pages/about_us/about_us/info.php:4
msgid "Hello and welcome to Duplicator, the most reliable WordPress backup and migration plugin. At Duplicator, we build software that helps protect your website with our reliable secure backups and migrate your website without any manual effort."
msgstr "Ciao! Ti diamo il benvenuto su Duplicator, il plugin più affidabile per il backup e la migrazione di WordPress. Noi di Duplicator creiamo un software che aiuta a proteggere il tuo sito web con i nostri backup sicuri e affidabili e a migrare il tuo sito web senza alcuno sforzo manuale."

#: template/admin_pages/about_us/about_us/extra_plugin_item.php:46
msgid "Status:"
msgstr "Stato:"

#: template/admin_pages/about_us/about_us/extra_plugin_item.php:28
msgid "Install Plugin"
msgstr "Installa il plugin"

#: template/admin_pages/about_us/about_us/extra_plugin_item.php:22
msgid "Activate"
msgstr "Attiva"

#: src/Controllers/AboutUsController.php:57
#: template/admin_pages/about_us/about_us/extra_plugin_item.php:17
msgid "Activated"
msgstr "Attivato"

#: src/Views/EducationElements.php:120
msgid "Multisite Support - Duplicator Pro supports multisite network backup & migration. You can even install a subsite as a standalone site."
msgstr "Supporto multisito - Duplicator Pro supporta il backup e la migrazione in rete di più siti. Puoi anche installare un sottosito come sito autonomo."

#: src/Views/EducationElements.php:118
msgid "Large Site Support - Duplicator Pro has developed a new way to package backups especially tailored for larger site. No server timeouts or other restrictions."
msgstr "Supporto per siti di grandi dimensioni - Duplicator Pro ha sviluppato un nuovo modo di confezionare i backup appositamente per i siti di grandi dimensioni. Non ci sono timeout del server o altre restrizioni."

#: src/Views/EducationElements.php:116
msgid "File & Database Table Filters - Use file and database filters to pick and choose exactly what you want to backup or transfer. No bloat!"
msgstr "Filtri per file e tabelle di database - Utilizza filtri per file e database per scegliere esattamente ciò di cui vuoi fare il backup o il trasferimento. Nessun appesantimento!"

#: src/Views/EducationElements.php:114
msgid "Server to Server Import - Direct Backup import from source server or cloud storage using URL. No need to download the Backup to your desktop machine first."
msgstr "Importazione da server a server - Importazione diretta del backup dal server di origine o dal cloud storage tramite URL. Non è necessario scaricare prima il backup sul computer desktop."

#: src/Views/EducationElements.php:113
msgid "Secure File Encryption - Protect and secure the archive file with industry-standard AES-256 encryption"
msgstr "Crittografia sicura dei file - Proteggi il file di archivio con la crittografia AES-256 standard del settore."

#: src/Views/EducationElements.php:111
msgid "Recovery Points - Recovery Points provides protection against mistakes and bad updates by letting you quickly rollback your system to a known, good state."
msgstr "Punti di ripristino - Punti di ripristino fornisce protezione contro errori e aggiornamenti sbagliati, consentendo di ripristinare rapidamente il sistema a uno stato noto e valido."

#: src/Views/EducationElements.php:110
msgid "Cloud Backups - Back up to Dropbox, FTP, Google Drive, OneDrive, or Amazon S3 and more for safe storage."
msgstr "Backup cloud - Esegui il backup su Dropbox, FTP, Google Drive, OneDrive, Amazon S3 e altro ancora per un'archiviazione sicura."

#: src/Views/EducationElements.php:108
msgid "Scheduled Backups - Ensure that important data is regularly and consistently backed up, allowing for quick and efficient recovery in case of data loss."
msgstr "Backup programmati - Assicurati che i dati importanti siano sottoposti a backup regolari e costanti, per consentire un recupero rapido ed efficiente in caso di perdita di dati."

#: src/Views/DashboardWidget.php:195
msgctxt "%s represents the time diff, eg. 2 days"
msgid "%s ago"
msgstr "%s fa"

#: src/Views/DashboardWidget.php:183
msgid "No Backups have been created yet."
msgstr "Non è stato ancora creato alcun backup."

#: src/Views/DashboardWidget.php:166
msgid "A Backup is currently running."
msgstr "È in corso un backup."

#: src/Utils/ExtraPlugins/ExtraPluginsMng.php:485
msgid "Sugar Calendar Pro"
msgstr "Sugar Calendar Pro"

#: src/Utils/ExtraPlugins/ExtraPluginsMng.php:479
#: src/Utils/ExtraPlugins/ExtraPluginsMng.php:488
msgid "A simple & powerful event calendar plugin for WordPress that comes with all the event management features including payments, scheduling, timezones, ticketing, recurring events, and more."
msgstr "Un semplice e potente plugin WordPress per il calendario degli eventi: è dotato di tutte le funzionalità di gestione degli eventi, tra cui pagamenti, programmazione, fasce orarie, biglietteria, eventi ricorrenti e molto altro."

#: src/Utils/ExtraPlugins/ExtraPluginsMng.php:476
msgid "Sugar Calendar"
msgstr "Sugar Calendar"

#: src/Utils/ExtraPlugins/ExtraPluginsMng.php:467
msgid "The best WordPress eCommerce plugin for selling digital downloads. Start selling eBooks, software, music, digital art, and more within minutes. Accept payments, manage subscriptions, advanced access control, and more."
msgstr "Il miglior plugin WordPress di eCommerce per la vendita di prodotti digitali. Vendi eBook, software, musica, arte digitale e molto altro in pochi minuti. Accetta i pagamenti, gestisci le iscrizioni, controlla in modo avanzato gli accessi e molto altro."

#: src/Utils/ExtraPlugins/ExtraPluginsMng.php:464
msgid "Easy Digital Downloads"
msgstr "Easy Digital Downloads"

#: src/Utils/ExtraPlugins/ExtraPluginsMng.php:451
msgid "WP Simple Pay Pro"
msgstr "WP Simple Pay Pro"

#: src/Utils/ExtraPlugins/ExtraPluginsMng.php:442
#: src/Utils/ExtraPlugins/ExtraPluginsMng.php:454
msgid "The #1 Stripe payments plugin for WordPress. Start accepting one-time and recurring payments on your WordPress site without setting up a shopping cart. No code required."
msgstr "Il plugin WordPress numero 1 per i pagamenti Stripe. Accetta pagamenti sia una tantum che ricorrenti sul tuo sito WordPress senza dover configurare un carrello della spesa. E tutto senza mai toccare il codice."

#: src/Utils/ExtraPlugins/ExtraPluginsMng.php:439
msgid "WP Simple Pay"
msgstr "WP Simple Pay"

#: src/Utils/ExtraPlugins/ExtraPluginsMng.php:427
msgid "The #1 affiliate management plugin for WordPress. Easily create an affiliate program for your eCommerce store or membership site within minutes and start growing your sales with the power of referral marketing."
msgstr "Il plugin WordPress numero 1 per la gestione delle affiliazioni. Crea in pochi minuti un programma di affiliazione per il tuo negozio eCommerce o sito di membership e fai crescere le tue vendite con la potenza del referral marketing."

#: src/Utils/ExtraPlugins/ExtraPluginsMng.php:424
msgid "AffiliateWP"
msgstr "AffiliateWP"

#: src/Utils/ExtraPlugins/ExtraPluginsMng.php:413
msgid "The most advanced WordPress search plugin. Customize your WordPress search algorithm, reorder search results, track search metrics, and everything you need to leverage search to grow your business."
msgstr "Il più avanzato plugin di ricerca per WordPress. Personalizza l'algoritmo di ricerca di WordPress, riordina i risultati di ricerca, traccia le metriche e tutto ciò che serve per sfruttare la ricerca e far crescere il tuo business."

#: src/Utils/ExtraPlugins/ExtraPluginsMng.php:410
msgid "SearchWP"
msgstr "SearchWP"

#: src/Utils/ExtraPlugins/ExtraPluginsMng.php:397
msgid "Boost your sales and conversions by up to 15% with real-time social proof notifications. TrustPulse helps you show live user activity and purchases to help convince other users to purchase."
msgstr "Aumenta vendite e conversioni fino al 15% con le notifiche di riprova sociale in tempo reale. TrustPulse ti permette di mostrare in diretta l'attività degli utenti e i loro acquisti per convincere altre persone ad acquistare."

#: src/Utils/ExtraPlugins/ExtraPluginsMng.php:394
msgid "TrustPulse"
msgstr "TrustPulse"

#: src/Utils/ExtraPlugins/ExtraPluginsMng.php:381
msgid "Smash Balloon YouTube Feeds Pro"
msgstr "YouTube Feeds Pro di Smash Balloon"

#: src/Utils/ExtraPlugins/ExtraPluginsMng.php:372
#: src/Utils/ExtraPlugins/ExtraPluginsMng.php:384
msgid "Easily display YouTube videos on your WordPress site without writing any code. Comes with multiple layouts, ability to embed live streams, video filtering, ability to combine multiple channel videos, and more."
msgstr "Visualizza con facilità i video di YouTube sul tuo sito WordPress senza scrivere alcun codice. Include numerosi layout, capacità di incorporare i live stream, filtrare i video, combinare più canali video e molto altro."

#: src/Utils/ExtraPlugins/ExtraPluginsMng.php:369
msgid "Smash Balloon YouTube Feeds"
msgstr "YouTube Feeds di Smash Balloon"

#: src/Utils/ExtraPlugins/ExtraPluginsMng.php:356
msgid "Smash Balloon Twitter Feeds Pro"
msgstr "Twitter Feeds Pro di Smash Balloon"

#: src/Utils/ExtraPlugins/ExtraPluginsMng.php:347
#: src/Utils/ExtraPlugins/ExtraPluginsMng.php:359
msgid "Easily display Twitter content in WordPress without writing any code. Comes with multiple layouts, ability to combine multiple Twitter feeds, Twitter card support, tweet moderation, and more."
msgstr "Visualizza con facilità i contenuti di Twitter in WordPress senza scrivere alcun codice. Include numerosi layout, capacità di combinare più feed di Twitter, supporto per le schede di Twitter, moderazione dei tweet e altro ancora."

#: src/Utils/ExtraPlugins/ExtraPluginsMng.php:344
msgid "Smash Balloon Twitter Feeds"
msgstr "Twitter Feeds di Smash Balloon"

#: src/Utils/ExtraPlugins/ExtraPluginsMng.php:331
msgid "Smash Balloon Facebook Feeds Pro"
msgstr "Facebook Feeds Pro di Smash Balloon"

#: src/Utils/ExtraPlugins/ExtraPluginsMng.php:322
#: src/Utils/ExtraPlugins/ExtraPluginsMng.php:334
msgid "Easily display Facebook content on your WordPress site without writing any code. Comes with multiple templates, ability to embed albums, group content, reviews, live videos, comments, and reactions."
msgstr "Visualizza con facilità i contenuti di Facebook sul tuo sito WordPress senza scrivere alcun codice. Include numerosi template, capacità di incorporare album, contenuti di gruppo, recensioni, video live, commenti e reazioni."

#: src/Utils/ExtraPlugins/ExtraPluginsMng.php:319
msgid "Smash Balloon Facebook Feeds"
msgstr "Facebook Feeds di Smash Balloon"

#: src/Utils/ExtraPlugins/ExtraPluginsMng.php:306
msgid "Smash Balloon Instagram Feeds Pro"
msgstr "Instagram Feeds Pro di Smash Balloon"

#: src/Utils/ExtraPlugins/ExtraPluginsMng.php:297
#: src/Utils/ExtraPlugins/ExtraPluginsMng.php:309
msgid "Easily display Instagram content on your WordPress site without writing any code. Comes with multiple templates, ability to show content from multiple accounts, hashtags, and more. Trusted by 1 million websites."
msgstr "Visualizza con facilità i contenuti di Instagram sul tuo sito WordPress senza scrivere alcun codice. Include numerosi template, capacità di mostrare contenuti da più account, hashtag e altro ancora. 1 milione di siti web gli hanno dato fiducia."

#: src/Utils/ExtraPlugins/ExtraPluginsMng.php:294
msgid "Smash Balloon Instagram Feeds"
msgstr "Instagram Feeds di Smash Balloon"

#: src/Utils/ExtraPlugins/ExtraPluginsMng.php:282
msgid "Connect with your visitors after they leave your website with the leading web push notification software. Over 10,000+ businesses worldwide use PushEngage to send 9 billion notifications each month."
msgstr "Mettiti in contatto con le persone dopo che visitano il tuo sito web grazie al principale software di notifiche push. Più di 10.000 aziende in tutto il mondo usano PushEngage per inviare 9 miliardi di notifiche ogni mese."

#: src/Utils/ExtraPlugins/ExtraPluginsMng.php:279
msgid "PushEngage"
msgstr "PushEngage"

#: src/Utils/ExtraPlugins/ExtraPluginsMng.php:266
msgid "RafflePress Pro"
msgstr "RafflePress Pro"

#: src/Utils/ExtraPlugins/ExtraPluginsMng.php:257
#: src/Utils/ExtraPlugins/ExtraPluginsMng.php:269
msgid "Turn your website visitors into brand ambassadors! Easily grow your email list, website traffic, and social media followers with the most powerful giveaways & contests plugin for WordPress."
msgstr "Trasforma le persone che visitano il tuo sito in brand ambassador! Fai crescere in fretta la tua lista di email, il traffico del sito web e i follower dei social media grazie al più potente plugin WordPress per giveaway e concorsi."

#: src/Utils/ExtraPlugins/ExtraPluginsMng.php:254
msgid "RafflePress"
msgstr "RafflePress"

#: src/Utils/ExtraPlugins/ExtraPluginsMng.php:244
msgid "SeedProd Pro"
msgstr "SeedProd Pro"

#: src/Utils/ExtraPlugins/ExtraPluginsMng.php:238
#: src/Utils/ExtraPlugins/ExtraPluginsMng.php:247
msgid "The best WordPress coming soon page plugin to create a beautiful coming soon page, maintenance mode page, or landing page. No coding skills required."
msgstr "Il miglior plugin per WordPress per creare una bellissima pagina coming soon, una pagina in modalità manutenzione o una landing page. Non serve saper programmare per usarlo."

#: src/Utils/ExtraPlugins/ExtraPluginsMng.php:235
#: src/Views/DashboardWidget.php:230
msgid "SeedProd"
msgstr "SeedProd"

#: src/Utils/ExtraPlugins/ExtraPluginsMng.php:222
msgid "AIOSEO Pro"
msgstr "AIOSEO Pro"

#: src/Utils/ExtraPlugins/ExtraPluginsMng.php:213
#: src/Utils/ExtraPlugins/ExtraPluginsMng.php:225
msgid "The original WordPress SEO plugin and toolkit that improves your website's search rankings. Comes with all the SEO features like Local SEO, WooCommerce SEO, sitemaps, SEO optimizer, schema, and more."
msgstr "L'originale plugin e toolkit SEO di WordPress che migliora il posizionamento del tuo sito web. Include tutte le caratteristiche SEO come Local SEO, WooCommerce SEO, sitemap, SEO optimizer, schema e molto altro."

#: src/Utils/ExtraPlugins/ExtraPluginsMng.php:210
#: src/Views/DashboardWidget.php:222
msgid "AIOSEO"
msgstr "AIOSEO"

#: src/Utils/ExtraPlugins/ExtraPluginsMng.php:197
msgid "WP Mail SMTP Pro"
msgstr "WP Mail SMTP Pro"

#: src/Utils/ExtraPlugins/ExtraPluginsMng.php:188
#: src/Utils/ExtraPlugins/ExtraPluginsMng.php:200
msgid "Improve your WordPress email deliverability and make sure that your website emails reach user's inbox with the #1 SMTP plugin for WordPress. Over 3 million websites use it to fix WordPress email issues."
msgstr "Migliora la deliverability delle tue email in WordPress e assicurati che le email del tuo sito raggiungano la casella di posta dell'utente con il plugin SMTP n. 1 per WordPress. Oltre 2 milioni di siti web lo usano per risolvere i problemi di email di WordPress."

#: src/Utils/ExtraPlugins/ExtraPluginsMng.php:185
#: src/Views/DashboardWidget.php:238
msgid "WP Mail SMTP"
msgstr "WP Mail SMTP"

#: src/Utils/ExtraPlugins/ExtraPluginsMng.php:175
msgid "The easiest drag & drop WordPress form builder plugin to create beautiful contact forms, subscription forms, payment forms, and more in minutes. No coding skills required."
msgstr "Il più semplice plugin \"trascina e rilascia\" per WordPress: crea bellissimi moduli di contatto, moduli di abbonamento, moduli di pagamento e molto altro ancora in pochi minuti. Non serve saper programmare per usarlo."

#: src/Utils/ExtraPlugins/ExtraPluginsMng.php:172
msgid "WPForms Pro"
msgstr "WPForms Pro"

#: src/Utils/ExtraPlugins/ExtraPluginsMng.php:163
msgid "The best drag & drop WordPress form builder. Easily create beautiful contact forms, surveys, payment forms, and more with our 100+ form templates. Trusted by over 4 million websites as the best forms plugin."
msgstr "Il miglior builder di moduli WordPress drag & drop. Crea con facilità bellissimi moduli di contatto, sondaggi, moduli di pagamento e molto altro ancora con i nostri 100 e più template di moduli. Sono più di 4 milioni i siti web che lo considerano il miglior plugin per i moduli."

#: src/Utils/ExtraPlugins/ExtraPluginsMng.php:160
msgid "WPForms"
msgstr "WPForms"

#: src/Utils/ExtraPlugins/ExtraPluginsMng.php:147
msgid "MonsterInsights Pro"
msgstr "MonsterInsights Pro"

#: src/Utils/ExtraPlugins/ExtraPluginsMng.php:138
#: src/Utils/ExtraPlugins/ExtraPluginsMng.php:150
msgid "The leading WordPress analytics plugin that shows you how people find and use your website, so you can make data driven decisions to grow your business. Properly set up Google Analytics without writing code."
msgstr "Il principale plugin di analisi per WordPress che ti mostra come le persone trovano e usano il tuo sito: così potrai prendere decisioni guidate dai dati per far crescere il tuo business. Imposta correttamente Google Analytics senza bisogno di scrivere codice."

#: src/Utils/ExtraPlugins/ExtraPluginsMng.php:135
#: src/Views/DashboardWidget.php:214
msgid "MonsterInsights"
msgstr "MonsterInsights"

#: src/Utils/ExtraPlugins/ExtraPluginsMng.php:126
msgid "Instantly get more subscribers, leads, and sales with the #1 conversion optimization toolkit. Create high converting popups, announcement bars, spin a wheel, and more with smart targeting and personalization."
msgstr "Ottieni subito più abbonati, contatti e vendite con il toolkit n. 1 per l'ottimizzazione delle conversioni. Crea pop-up ad alta conversione, banner di annunci e molto altro con la personalizzazione e l'ottimizzazione intelligenti."

#: src/Utils/ExtraPlugins/ExtraPluginsMng.php:98
msgid "Plugin not found"
msgstr "Plugin non trovato"

#: src/Utils/ExtraPlugins/ExtraPluginsMng.php:93
msgid "Plugin slug is empty"
msgstr "Lo slug del plugin è vuoto"

#: src/Utils/ExtraPlugins/ExtraItem.php:297
msgid "Not Installed"
msgstr "Non installato"

#: src/Utils/ExtraPlugins/ExtraItem.php:295
msgid "Inactive"
msgstr "Inattivo"

#: src/Controllers/AboutUsController.php:56
#: src/Utils/ExtraPlugins/ExtraItem.php:293
msgid "Active"
msgstr "Attivo"

#: src/Utils/Upsell.php:79
msgid "Multisite Network"
msgstr "Rete multisito"

#: src/Utils/Upsell.php:78
msgid "Regenerate SALTS"
msgstr "Rigenera SALTS"

#: src/Utils/Upsell.php:73
msgid "Developer Hooks"
msgstr "Hook per lo sviluppo"

#: src/Utils/Upsell.php:70
msgid "Smart Migration Wizard"
msgstr "Migrazione guidata intelligente"

#: src/Utils/Upsell.php:50
msgid "Multisite Network Support"
msgstr "Supporto per reti multisito"

#: src/Utils/Upsell.php:47
msgid "Cloud Storage - FTP/SFTP"
msgstr "Archiviazione cloud - FTP/SFTP"

#: src/Utils/Upsell.php:46
msgid "Cloud Storage - OneDrive"
msgstr "Archiviazione cloud - OneDrive"

#: src/Utils/Upsell.php:45
msgid "Cloud Storage - DropBox"
msgstr "Archiviazione cloud - DropBox"

#: src/Utils/Upsell.php:44
msgid "Cloud Storage - Amazon S3"
msgstr "Archiviazione cloud - Amazon S3"

#: src/Utils/Upsell.php:43
msgid "Cloud Storage - Google Drive"
msgstr "Archiviazione cloud - Google Drive"

#: src/Utils/Upsell.php:41 src/Utils/Upsell.php:67
#: template/admin_pages/welcome/features.php:62
msgid "Server to Server Import"
msgstr "Importazione da server a server"

#: src/Utils/Upsell.php:40 src/Utils/Upsell.php:66
#: template/admin_pages/welcome/features.php:54
msgid "Secure File Encryption"
msgstr "Crittografia sicura dei file"

#: src/Core/Bootstrap.php:336 template/admin_pages/about_us/tabs.php:17
msgid "About Us"
msgstr "Informazioni"

#: src/Core/Bootstrap.php:335
msgid "About Duplicator"
msgstr "Informazioni su Duplicator"

#: src/Core/Bootstrap.php:269
msgid "NEW!"
msgstr "NUOVO!"

#: src/Controllers/WelcomeController.php:65
#: src/Controllers/WelcomeController.php:66
msgid "Welcome to Duplicator"
msgstr "Benvenuto in Duplicator"

#: src/Controllers/StorageController.php:76
#: src/Controllers/StorageController.php:77
msgid "FTP/SFTP"
msgstr "FTP/SFTP"

#: src/Controllers/StorageController.php:116
msgid "S3-Compatible Provider"
msgstr "Provider compatibile con S3"

#: src/Controllers/AboutUsController.php:242
msgid "Priority Support"
msgstr "Supporto prioritario"

#: src/Controllers/AboutUsController.php:241
msgid "Limited Support"
msgstr "Supporto limitato"

#: src/Controllers/AboutUsController.php:233
msgid "Advanced features included: Hourly Schedules, Custom Search & Replace, Migrate Duplicator Settings, Regenerate Salts and Developer Hooks"
msgstr "Caratteristiche avanzate incluse: programmi orari, ricerca e sostituzione personalizzata, migrazione delle impostazioni di Duplicator, rigenerazione dei salts e hook per lo sviluppo."

#: src/Controllers/AboutUsController.php:231
msgid "Advanced Features"
msgstr "Caratteristiche avanzate"

#: src/Controllers/AboutUsController.php:225
msgid "Enhanced features include: Managed Hosting Support, Shared Database Support, Streamlined Installer, Email Alerts and more..."
msgstr "Le caratteristiche migliorate includono: supporto per l'hosting gestito, supporto per i database condivisi, installazione semplificata, avvisi via email e molto altro..."

#: src/Controllers/AboutUsController.php:223
msgid "Enhanced Features"
msgstr "Caratteristiche migliorate"

#: src/Controllers/AboutUsController.php:212
msgid "Protect and secure the archive file with industry-standard AES-256 encryption!"
msgstr "Proteggi e metti al sicuro il file di archivio con la crittografia standard del settore AES-256!"

#: src/Controllers/AboutUsController.php:210
msgid "Archive Encryption"
msgstr "Crittografia degli archivi"

#: src/Controllers/AboutUsController.php:207
msgid "Create your own custom-configured WordPress site and \"Brand\" the installer file with your look and feel."
msgstr "Crea il tuo sito WordPress personalizzato e adatta l'aspetto del file di installazione con la tua identità di marca."

#: src/Controllers/AboutUsController.php:198
msgid "Supports multisite network backup & migration. Subsite As Standalone Install, Standalone Import Into Multisite and Import Subsite Into Multisite"
msgstr "Supporta il backup o la migrazione in rete di multisiti. Installazione di un sottosito come indipendente, importazione indipendente in un multisito e importazione di un sottosito in un multisito."

#: src/Controllers/AboutUsController.php:196
msgid "Multisite support"
msgstr "Supporto multisito"

#: src/Controllers/AboutUsController.php:189
msgid "Direct Server Transfers allow you to build an archive, then directly transfer it from the source server to the destination server for a lightning fast migration!"
msgstr "Direct Server Transfers ti permette di compilare un archivio e trasferirlo direttamente dal server sorgente al server di destinazione per una migrazione fulminea!"

#: src/Controllers/AboutUsController.php:187
msgid "Server-to-Server Import"
msgstr "Importazione da server a server"

#: src/Controllers/AboutUsController.php:181
msgid "We've developed a new way to package backups especially tailored for larger site. No server timeouts or other restrictions!"
msgstr "Abbiamo sviluppato un nuovo modo di preparare i backup, appositamente studiato per i siti di grandi dimensioni. Nessun timeout del server o altre restrizioni!"

#: src/Controllers/AboutUsController.php:179 src/Utils/Upsell.php:49
#: src/Utils/Upsell.php:75
msgid "Larger Site Support"
msgstr "Supporto per siti di grandi dimensioni"

#: src/Controllers/AboutUsController.php:173
msgid "Back up to Dropbox, FTP, Google Drive, OneDrive, Amazon S3 or any S3-compatible storage service for safe storage."
msgstr "Esegui il backup su Dropbox, FTP, Google Drive, OneDrive, Amazon S3 o qualsiasi altro servizio di archiviazione compatibile con S3 per un'archiviazione sicura."

#: src/Controllers/AboutUsController.php:165
msgid "Recovery Points provide protection against mistakes and bad updates by letting you quickly rollback your system to a known, good state."
msgstr "I Punti di ripristino forniscono protezione contro errori e aggiornamenti sbagliati, consentendo di ripristinare rapidamente il sistema a uno stato noto e valido."

#: src/Controllers/AboutUsController.php:157
msgid "Ensure that your important data is regularly and consistently backed up, allowing for quick and efficient recovery in case of data loss."
msgstr "Assicurati che il backup dei tuoi dati importanti venga eseguito in modo normale e costante, così in caso di perdita puoi recuperare i tuoi dati in modo rapido ed efficiente."

#: src/Controllers/AboutUsController.php:149
msgid "Drag and Drop migrations and site restores! Simply drag the bundled site archive to the site you wish to overwrite."
msgstr "Migra e ripristina i siti con un semplice \"trascina e rilascia\"! Trascina l'archivio del sito in bundle sul sito che desideri sovrascrivere."

#: src/Controllers/AboutUsController.php:148
msgid "Classic WordPress-less Installs Only"
msgstr "Solo installazioni classiche senza WordPress"

#: template/parts/notice-bar.php:65
msgid "Dismiss this message."
msgstr "Ignora questo messaggio."

#. translators: %s - duplicator.com Upgrade page URL.
#: template/parts/notice-bar.php:44
msgid "<strong>You're using Duplicator Lite.</strong> To unlock more features consider <a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">upgrading to Pro</a>"
msgstr "<strong>Stai utilizzando Duplicator Lite.</strong> Per sbloccare maggiori funzionalità valuta di <a href=\"%s\" target=\"_blank\" rel=\"noopener noreferrer\">passare alla versione Pro</a>"

#. translators: $1$s - WPForms plugin name; $2$s - WP.org review link; $3$s -
#. WP.org review link.
#: src/Core/Notifications/Review.php:220
msgid "Please rate <strong>Duplicator</strong> <a href=\"%1$s\" target=\"_blank\" rel=\"noopener noreferrer\">&#9733;&#9733;&#9733;&#9733;&#9733;</a> on <a href=\"%1$s\" target=\"_blank\" rel=\"noopener\">WordPress.org</a> to help us spread the word. Thank you from the Duplicator team!"
msgstr "Dai un voto a <strong>Duplicator</strong> <a href=\"%1$s\" target=\"_blank\" rel=\"noopener noreferrer\">&#9733;&#9733;&#9733;&#9733;&#9733;</a> su <a href=\"%1$s\" target=\"_blank\" rel=\"noopener\">WordPress.org</a> per aiutarci a diffondere la parola. Grazie dal team di Duplicator!"

#: src/Core/Notifications/Review.php:145
msgid "No thanks"
msgstr "No grazie"

#: src/Core/Notifications/Review.php:141
msgid "Give Feedback"
msgstr "Lascia un feedback"

#: src/Core/Notifications/Review.php:133
msgid "We're sorry to hear you aren't enjoying Duplicator. We would love a chance to improve. Could you take a minute and let us know what we can do better?"
msgstr "Ci dispiace che non ti stia piacendo Duplicator. Vorremmo poter migliorare. Potresti dedicarci un minuto e farci sapere cosa possiamo migliorare?"

#: src/Core/Notifications/Review.php:126
msgid "I already did"
msgstr "L'ho già fatto"

#: src/Core/Notifications/Review.php:122
msgid "Nope, maybe later"
msgstr "No, forse più tardi"

#: src/Core/Notifications/Review.php:118
msgid "Ok, you deserve it"
msgstr "Ok, te lo meriti"

#: src/Core/Notifications/Review.php:114
msgid "~ John Turner<br>President of Duplicator"
msgstr "~ John Turner<br>Presidente di Duplicator"

#: src/Core/Notifications/Review.php:109
msgid "That’s awesome! Could you please do me a BIG favor and give it a 5-star rating on WordPress to help us spread the word and boost our motivation?"
msgstr "È fantastico! Potresti farci un ENORME favore lasciando una recensione a 5 stelle su WordPress, aiutandoci a far conoscere SeedProd a più persone e incoraggiarci?"

#: src/Core/Notifications/Review.php:102
msgid "Not really"
msgstr "Preferisco di no"

#: src/Core/Notifications/Review.php:95
msgid "Are you enjoying %s?"
msgstr "Ti sta piacendo %s?"

#: src/Core/Bootstrap.php:268 src/Core/Bootstrap.php:527
msgid "Upgrade to Pro"
msgstr "Aggiorna alla versione Pro"

#: views/packages/details/detail.php:486
msgid "Password Protection Disabled"
msgstr "Protezione con password disabilitata"

#: views/packages/details/detail.php:484
msgid "Password Protection Enabled"
msgstr "Protezione della password abilitata"

#: views/settings/packages.php:389
msgid "Tip: Each row on the Backups screen includes a copy button to copy the installer name to the clipboard.  Paste the installer name from the clipboard into the URL being used to install the destination site.  This feature is handy when using the secure installer name."
msgstr "Suggerimento: Ogni riga nella schermata dei backup include un pulsante di copia per copiare il nome del programma di installazione negli appunti. Incolla il nome dell'installatore dagli appunti nell'URL utilizzato per installare il sito di destinazione.  Questa caratteristica è utile quando si usa il nome del programma di installazione sicuro."

#: views/settings/packages.php:381
msgid "Do not to leave any installer files on the destination server, after installing the migrated/restored site.  Logon as a WordPress administrator and follow the prompts to remove the installer files or remove them manually."
msgstr "Non lasciare alcun file del programma di installazione sul server di destinazione, dopo aver installato il sito migrato/ripristinato. Accedi come amministratore di WordPress e segui le istruzioni per rimuovere i file del programma di installazione o rimuovili manualmente."

#: views/settings/packages.php:370
msgid "This setting specifies the name of the installer used at download-time.  Independent of the value of this setting, you can change the name of the installer in the \"Save as\" file dialog at download-time.  If you choose to use a custom name, use a file name that is known only to you. Installer filenames must end in \"php\".  Changes to the Backup file should not be made."
msgstr "Questa impostazione specifica il nome del programma di installazione utilizzato al momento del download. Indipendentemente dal valore di questa impostazione, puoi modificare il nome del programma di installazione nella finestra di dialogo \"Salva con nome\" al momento del download. Se scegli di usare un nome personalizzato, usa un nome di file noto solo a te. I nomi dei file del programma di installazione devono terminare con \"php\". Non si devono apportare modifiche al file di backup."

#: views/settings/packages.php:366
msgid "Example"
msgstr "Esempio"

#: views/settings/packages.php:365
msgid "Using a 'Secure' file helps prevent unauthorized access to the installer file."
msgstr "L'utilizzo di un file \"sicuro\" aiuta a prevenire l'accesso non autorizzato al file di installazione."

#: views/settings/packages.php:349
msgid "Secure"
msgstr "Davvero vuoi farlo"

#: views/settings/packages.php:332
msgid "File Name"
msgstr "Nome del file"

#: views/settings/packages.php:297
msgctxt "%1$s and %2$s represents the opening and closing HTML tags for an anchor or link"
msgid "Consider upgrading to %1$sDuplicator Pro%2$s for unlimited large site support with DupArchive."
msgstr "Considera l'aggiornamento a %1$sDuplicator Pro%2$s per avere un supporto illimitato per siti di grandi dimensioni con DupArchive."

#: views/settings/packages.php:294
msgid "Duplicator Lite has a fixed constraint of 500MB for daf formats."
msgstr "Duplicator Lite ha un vincolo fisso di 500 MB per i formati daf."

#: views/settings/packages.php:280
msgid "Duplicator Lite has no fixed size constraints for zip formats.  The only constraints are timeouts on the server."
msgstr "Duplicator Lite non ha vincoli di dimensioni corrette per i formati zip. Gli unici vincoli sono i timeout del server."

#: views/settings/packages.php:216
msgid "Multi-Threaded (Pro)"
msgstr "Multi-Threaded (Pro)"

#: views/parts/migration-message.php:63
msgid "Note: This message will be removed after all installer files are removed. Installer files must be removed to maintain a secure site. Click the link above to remove all installer files and complete the migration."
msgstr "Nota: Questo messaggio verrà rimosso dopo che tutti i file del programma di installazione saranno stati rimossi. I file di installazione devono essere rimossi per mantenere un sito sicuro. Fai clic sul link qui sopra per rimuovere tutti i file di installazione e completare la migrazione."

#: views/parts/migration-message.php:50
msgid "Final step:"
msgstr "Fase finale:"

#: views/parts/migration-message.php:38
msgid "Security actions:"
msgstr "Azioni di sicurezza:"

#: views/parts/migration-message.php:22
msgid "The following installation files are stored in the folder <b>%s</b>"
msgstr "I seguenti file di installazione sono archiviati nella cartella <b>%s</b>"

#: views/parts/migration-message.php:15
msgid "This site has been successfully restored!"
msgstr "Questo sito è stato ripristinato con successo!"

#: views/parts/migration-clean-installation-files.php:84
msgid "The Duplicator team has worked many years to make moving a WordPress site a much easier process. "
msgstr "Il team di Duplicator ha lavorato per molti anni per rendere il trasferimento di un sito WordPress un processo molto più semplice. "

#: views/parts/migration-clean-installation-files.php:62
msgid " If the installer files do not successfully get removed with this action, then they WILL need to be removed manually through your hosts control panel or FTP.  Please remove all installer files to avoid any security issues on this site."
msgstr " Se i file del programma di installazione non vengono rimossi con questa azione, dovranno essere rimossi manualmente attraverso il pannello di controllo dell'host o l'FTP.  Rimuovi tutti i file di installazione per evitare problemi di sicurezza su questo sito."

#: views/parts/migration-clean-installation-files.php:13
msgid "Installation cleanup ran!"
msgstr "La pulizia dell'installazione è stata eseguita!"

#: views/parts/migration-almost-complete.php:43
#: views/parts/migration-message.php:72
msgid "If an archive.zip/daf file was intentially added to the root directory to perform an overwrite install of this site then you can ignore this message."
msgstr "Se un file archive.zip/daf è stato aggiunto alla directory principale per eseguire un'installazione sovrascritta di questo sito, puoi ignorare questo messaggio."

#: views/parts/migration-almost-complete.php:15
msgid "Restore Backup Almost Complete!"
msgstr "Ripristino del backup quasi completato!"

#: views/packages/main/s2.scan3.php:604
msgid "- Consider upgrading to %s for unlimited large site support."
msgstr "- Considera l'aggiornamento a %s per avere un supporto illimitato per i siti di grandi dimensioni."

#: views/packages/main/packages.php:280
msgid "Click to configure installer name."
msgstr "Fai clic per configurare il nome dell'installer."

#: views/packages/details/detail.php:288
msgid "Locations"
msgstr "Sedi"

#: views/packages/details/detail.php:228
msgid "The installer is also available inside the Backup file."
msgstr "Il programma di installazione è disponibile anche all'interno del file di backup."

#: views/packages/details/detail.php:184
msgid "Click buttons or links to download."
msgstr "Fai clic sui pulsanti o sui link per scaricare."

#: views/packages/details/detail.php:178
msgid "Download basic installer (installer.php)"
msgstr "Scarica l'installer di base (installer.php)"

#: views/packages/details/detail.php:175
msgid "Download hashed installer ([name]_[hash]_[time]_installer.php)"
msgstr "Scarica il programma di installazione con hash ([nome]_[hash]_[ora]_installer.php)"

#: src/Utils/CachesPurge/CacheItem.php:100
#: src/Utils/CachesPurge/CacheItem.php:104
msgid "Error on caches purge of <b>%s</b>."
msgstr "Errore sullo svuotamento della cache di <b>%s</b>."

#: src/Utils/CachesPurge/CacheItem.php:61
msgid "All caches on <b>%s</b> have been purged."
msgstr "Tutte le cache su <b>%s</b> sono state svuotate."

#: src/Core/MigrationMng.php:402
msgid "Original files folder"
msgstr "Cartella dei file originali"

#: src/Core/MigrationMng.php:401
msgid "Installer boot log"
msgstr "Registro di avvio dell'installer"

#: src/Core/MigrationMng.php:400
msgid "Installer log"
msgstr "Registro dell'installer"

#: src/Core/MigrationMng.php:383
msgid "Can't move %s to %s"
msgstr "Impossibile spostare %s su %s"

#: src/Core/MigrationMng.php:378
msgid "Original files folder moved in installer backup directory"
msgstr "La cartella dei file originali è stata spostata nella directory di backup del programma d'installazione."

#: classes/package/class.pack.installer.php:863
msgid "ARCHIVE CONSISTENCY TEST: PASS"
msgstr "TEST DI COERENZA DELL'ARCHIVIO: SUPERATO"

#: classes/package/class.pack.installer.php:860
msgid "ARCHIVE CONSISTENCY TEST: FAIL"
msgstr "TEST DI COERENZA DELL'ARCHIVIO: FALLITO"

#: classes/package/class.pack.installer.php:761
msgid "Zip archive %1s not present."
msgstr "L'archivio Zip %1s non è presente."

#: ctrls/class.web.services.php:116
msgid "INVALID REQUEST: File not found, please check the backup folder for file."
msgstr "RICHIESTA NON VALIDA: File non trovato, cerca il file nella cartella di backup."

#: views/packages/main/s3.build.php:284
msgid "Drag and drop or use a URL for super-fast installs (requires Pro*)"
msgstr "Trascina e rilascia o usa un URL per installazioni superveloci (richiede Pro*)"

#: views/packages/main/s3.build.php:278
msgid "Import Backup and Overwrite Site"
msgstr "Importa il backup e la sovrascrittura del sito"

#: views/packages/main/s3.build.php:264
msgid "Overwrite Site"
msgstr "Sovrascrivi il sito"

#: views/packages/main/s3.build.php:248
msgid "Install to Empty Directory "
msgstr "Installa in una directory vuota "

#: views/packages/main/packages.php:165 views/packages/main/packages.php:218
msgid "Visit the 'Quick Start' guide!"
msgstr "Visita la guida 'Avvio rapido'!"

#: views/packages/main/packages.php:161 views/packages/main/packages.php:214
msgid "Click 'Create New' to Backup Site"
msgstr "Fai clic su \"Crea nuovo\" per eseguire il backup del sito."

#: template/mocks/templates/templates.php:195
msgid "Templates are not available in Duplicator Lite!"
msgstr "I template non sono disponibili in Duplicator Lite!"

#: views/packages/main/s3.build.php:531
msgid "See Backup Log For Complete Details"
msgstr "Per i dettagli completi, consulta il registro di backup."

#: views/packages/main/s3.build.php:523
msgid "Error status unavailable."
msgstr "Stato di errore non disponibile."

#: views/packages/main/s3.build.php:516
msgid "System Details"
msgstr "Dettagli di sistema"

#: views/packages/main/s3.build.php:504
msgid "unavailable"
msgstr "non disponibile"

#: views/packages/main/s3.build.php:303
msgid "If the error details are not specific consider the options below by clicking each section."
msgstr "Se i dettagli dell'errore non sono specifici, considera le opzioni sottostanti facendo clic su ciascuna sezione."

#: views/packages/main/s3.build.php:302
msgid "This server cannot complete the build due to host setup constraints, see the error message for more details."
msgstr "Questo server non può completare la compilazione a causa di vincoli di configurazione dell'host; per maggiori dettagli, consulta il messaggio di errore."

#: views/packages/main/s3.build.php:271
msgid "Quickly overwrite an existing WordPress site in a few clicks."
msgstr "Sovrascrivi rapidamente un sito WordPress esistente in pochi clic."

#: views/packages/main/s3.build.php:257
msgid "Install to an empty directory like a new WordPress install does."
msgstr "Installa in una directory vuota come fa una nuova installazione di WordPress."

#: views/packages/main/s3.build.php:229
msgid "Notice:Duplicator Lite does not officially support WordPress multisite."
msgstr "Avviso: Duplicator Lite non supporta ufficialmente WordPress multisito."

#: views/packages/main/s3.build.php:213
msgid "[Show Installer Name]"
msgstr "[Mostra il nome dell'installer]"

#: views/packages/main/s3.build.php:198
msgid "Clicking this button will open the installer and Backup download prompts one after the other with one click verses downloading each file separately with two clicks.  On some browsers you may have to disable pop-up warnings on this domain for this to work correctly."
msgstr "Facendo clic su questo pulsante si apriranno le richieste di download del programma di installazione e del backup una dopo l'altra con un solo clic, invece di scaricare ogni file separatamente con due clic. Su alcuni browser potrebbe essere necessario disabilitare gli avvisi di pop-up su questo dominio affinché l'operazione funzioni correttamente."

#: views/packages/main/s3.build.php:197
msgid "Download Both Files:"
msgstr "Scarica entrambi i file:"

#: views/packages/main/s3.build.php:193
msgid "Download Both Files"
msgstr "Scarica entrambi i file"

#: views/packages/main/s3.build.php:181
msgid "Download Backup Files"
msgstr "Scarica i file di backup"

#: views/packages/main/s3.build.php:175
msgid "Build Time"
msgstr "Tempo di compilazione"

#: views/packages/main/s3.build.php:171
msgid "Backup Build Completed"
msgstr "Backup completato"

#: views/packages/main/s3.build.php:118
msgid "Step 3: Build and download the Backup files."
msgstr "Fase 3: Crea e scarica i file di backup."

#: views/packages/main/s3.build.php:25
msgid "When clicking the Installer download button, the 'Save as' dialog will save the name as '[name]_[hash]_[time]_installer.php'. This is the secure and recommended option.  For more information goto: Settings ❯ Backups Tab ❯ Installer Name Option.  To quickly copy the hashed installer name, to your clipboard use the copy icon link."
msgstr "Facendo clic sul pulsante di download dell'Installatore, la finestra di dialogo \"Salva con nome\" salverà il nome come \"[name]_[hash]_[time]_installer.php\". Questa è l'opzione sicura e consigliata. Per maggiori informazioni vai su: Impostazioni ❯ Scheda Backup ❯ Opzione Nome Installatore. Per copiare rapidamente il nome con hash del programma di installazione negli appunti, usa il link dell'icona di copia."

#: views/packages/main/s3.build.php:22
msgid "When clicking the Installer download button, the 'Save as' dialog will default the name to 'installer.php'. To improve the security and get more information, goto: Settings ❯ Backups Tab ❯ Installer Name option."
msgstr "Facendo clic sul pulsante di download dell'Installatore, la finestra di dialogo \"Salva con nome\" predefinisce il nome \"installer.php\". Per migliorare la sicurezza e ottenere maggiori informazioni, vai su: Impostazioni ❯ Scheda Backup ❯ Opzione Nome Installatore."

#: views/packages/main/s2.scan3.php:69
msgid "Only the database and a copy of the installer will be included in the Backup file.  This notice simply indicates that the Backup will not be capable of restoring a full WordPress site, but only the database.  If this is the desired intention then this notice can be ignored."
msgstr "Nel file di backup saranno inclusi solo il database e una copia del programma di installazione. Questo avviso indica semplicemente che il backup non sarà in grado di ripristinare un sito WordPress completo, ma solo il database. Se questo è quello che vuoi, puoi ignorare questo avviso."

#: views/packages/main/s2.scan2.php:192
msgid "While it is not recommended you can still continue with the build of this Backup.  At install time additional manual custom configurations will need to be made to finalize this multisite migration.  Please note that any support requests for mulitsite with Duplicator Lite will not be supported."
msgstr "Anche se non è consigliato, puoi comunque proseguire con la creazione di questo backup. Al momento dell'installazione sarà necessario effettuare ulteriori configurazioni personalizzate manuali per finalizzare questa migrazione multisito. Qualsiasi richiesta di assistenza per i multisiti con Duplicator Lite non sarà supportata."

#: views/packages/main/s2.scan2.php:13
msgid "Check Site Health"
msgstr "Verifica lo stato di salute del sito"

#: views/packages/main/s2.scan1.php:245
msgid "Scan Time:"
msgstr "Tempo di scansione:"

#: views/packages/main/s2.scan1.php:200
msgid "Step 2: Scan site for configuration &amp; system notices."
msgstr "Fase 2: Esegui una scansione del sito per verificare la presenza di avvisi di configurazione e di sistema."

#: views/packages/main/s1.setup2.php:388
msgid "The installer file is used to redeploy/install the Backup contents."
msgstr "Il file di installazione viene utilizzato per distribuire/installare i contenuti del backup."

#: views/packages/main/s1.setup2.php:297
msgid "Configuration"
msgstr "Configurazione"

#: views/packages/main/s1.setup2.php:110
msgid "This is the storage location on this server where the Backup and installer files will be saved."
msgstr "Questa è la posizione di archiviazione sul server in cui verranno salvati i file del backup e del programma di installazione."

#: views/packages/main/s1.setup1.php:72
msgid "Step 1: Choose the WordPress contents to backup."
msgstr "Passo 1: Scegli i contenuti di WordPress di cui fare il backup."

#: views/packages/main/packages.php:173 views/packages/main/s3.build.php:231
msgid "We strongly recommend upgrading to "
msgstr "Consigliamo caldamente di passare a "

#: views/packages/main/packages.php:171
msgid "Duplicator Lite does not officially support WordPress multisite."
msgstr "Duplicator Lite non supporta ufficialmente WordPress multisito."

#: views/packages/details/detail.php:430 views/packages/main/s1.setup2.php:302
#: views/settings/packages.php:97
msgid "SQL Mode"
msgstr "Modalità SQL"

#: views/packages/details/detail.php:322 views/packages/main/s1.setup2.php:154
msgid "Back up this site to %1$s, %2$s, %3$s, %4$s, %5$s and other locations with "
msgstr "Fai un backup di questo sito in %1$s, %2$s, %3$s, %4$s, %5$s e in altre posizioni con "

#: views/packages/details/detail.php:215
msgid "Build Log"
msgstr "Compila il log"

#: views/packages/details/detail.php:196
msgid "Share File Links"
msgstr "Condividi i link ai file"

#: views/packages/main/packages.php:332
msgid "Trace Logging Enabled.  Please disable when trace capture is complete."
msgstr "Trace Logging abilitato. Disabilita quando l'acquisizione del tracciamento è completo."

#: src/Core/MigrationMng.php:345
msgid "Can't rename installer file <b>%s</b> with HASH, please remove it for security reasons"
msgstr "Impossibile rinominare il file di installazione <b>%s</b> con HASH, rimuovilo per motivi di sicurezza"

#: src/Core/MigrationMng.php:339
msgid "Installer file <b>%s</b> renamed with HASH"
msgstr "File di installazione <b>%s</b> rinominato con HASH"

#: src/Core/MigrationMng.php:328
msgid "Can't remove installer file <b>%s</b>, please remove it for security reasons"
msgstr "Impossibile rimuovere il file di installazione <b>%s</b>, rimuovilo per motivi di sicurezza"

#: src/Core/MigrationMng.php:323
msgid "Installer file <b>%s</b> removed for security reasons"
msgstr "File di installazione <b>%s</b> rimosso per motivi di sicurezza"

#: src/Lite/Requirements.php:143
msgid "To use \"Duplicator LITE\" please deactivate \"Duplicator PRO\" from the "
msgstr "Per usare \"Duplicator LITE\" disattiva \"Duplicator PRO\" dalla "

#: src/Lite/Requirements.php:140
msgid "The \"Duplicator Lite\" and \"Duplicator Pro\" plugins cannot both be active at the same time.  "
msgstr "I plugin \"Duplicator Lite\" e \"Duplicator Pro\" non possono essere attivi contemporaneamente. "

#: src/Lite/Requirements.php:139
msgid "Duplicator Notice:"
msgstr "Avviso di Duplicator"

#: src/Lite/Requirements.php:145
msgid "plugins page"
msgstr "pagina dei plugin"

#: template/admin_pages/about_us/getting_started/first_package.php:47
msgid "Quick Start Guide"
msgstr "Guida rapida"

#: src/Utils/Upsell.php:77
msgid "Migrate Duplicator Settings"
msgstr "Impostazioni di migrazione di Duplicator"

#: src/Controllers/AboutUsController.php:205 src/Utils/Upsell.php:76
msgid "Installer Branding"
msgstr "Brand dell'installer"

#: src/Controllers/AboutUsController.php:134 src/Utils/Upsell.php:42
#: src/Utils/Upsell.php:68 template/admin_pages/welcome/features.php:71
msgid "File & Database Table Filters"
msgstr "Filtri per file e tabella di database"

#: template/admin_pages/settings/general/general.php:263
msgid "Foreign CSS"
msgstr "Css esterni"

#: template/admin_pages/settings/general/general.php:252
#: template/admin_pages/settings/general/general.php:266
msgid "Disable"
msgstr "Disabilito"

#: template/admin_pages/settings/general/general.php:249
msgid "Foreign JavaScript"
msgstr "Javascript Esterni"

#: views/packages/main/s2.scan2.php:90
msgid "Duplicator Lite allows users to build a Backup on managed hosts, however, the installer may not properly install Backups created on managed hosts due to the non-standard configurations of managed hosts. It is also possible the Backup engine of Duplicator Lite won’t be able to capture all of the necessary data of a site running on a managed host."
msgstr "Duplicator Lite permette agli utenti di creare un backup su host gestiti. Tuttavia il programma di installazione potrebbe non installare correttamente i backup creati su host gestiti a causa delle configurazioni non standard di questi ultimi. È anche possibile che il motore di backup di Duplicator Lite non sia in grado di catturare tutti i dati necessari di un sito in esecuzione su un host gestito."

#: views/packages/main/s2.scan2.php:87
msgid "A managed host is a WordPress host that tightly controls the server environment so that the software running on it can be closely ‘managed’ by the hosting company. Managed hosts typically have constraints imposed to facilitate this management, including the locking down of certain files and directories as well as non-standard configurations."
msgstr "Un host gestito è un host WordPress che controlla strettamente l'ambiente del server in modo che il software in esecuzione su di esso possa essere strettamente \"gestito\" dalla società di hosting. Gli host gestiti hanno tipicamente dei vincoli imposti per facilitare questa gestione, tra cui il blocco di alcuni file e directory e configurazioni non standard."

#: views/packages/main/s2.scan2.php:86
msgid "Managed Host"
msgstr "Host gestito"

#: views/packages/main/s2.scan2.php:44
msgid "System"
msgstr "Sistema"

#: views/packages/main/s2.scan3.php:510
msgid "Object Access"
msgstr "Accesso agli oggetti"

#: views/packages/main/s2.scan3.php:491
msgid "This database makes use of %1$s which can manually be imported at install time.  Instructions and SQL statement queries will be provided at install time for users to execute. No actions need to be performed at this time, this message is simply a notice."
msgstr "Questo database utilizza il sito %1$s che può essere importato manualmente al momento dell'installazione. Al momento dell'installazione verranno fornite istruzioni e query SQL che gli utenti potranno eseguire. Non è necessario eseguire alcuna azione al momento, questo messaggio è semplicemente un avviso."

#: views/packages/main/s2.scan3.php:490
msgid "triggers"
msgstr "trigger"

#: views/packages/main/s2.scan3.php:481
msgid "Triggers"
msgstr "Trigger"

#: src/Views/AdminNotices.php:448
msgid ""
"<strong>Duplicator</strong><hr> Your logged-in user role does not have export \n"
"                capability so you don't have access to Duplicator functionality."
msgstr ""
"<strong>Duplicator</strong><hr> Il ruolo utente con cui hai eseguito l'accesso non ha \n"
"               capacità di esportazione, quindi non hai accesso alle funzionalità di Duplicator."

#: views/packages/main/s1.setup2.php:533
msgid "example: utf8_general_ci (value is optional)"
msgstr "esempio: utf8_general_ci (il valore è facoltativo)"

#: views/packages/main/s1.setup2.php:525
msgid "Collation"
msgstr "Collazione"

#: views/packages/main/s1.setup2.php:520
msgid "example: utf8 (value is optional)"
msgstr "esempio: utf8 (il valore è facoltativo)"

#: src/Utils/Upsell.php:74
msgid "Managed Hosting Support"
msgstr "Supporto per l'hosting gestito"

#: src/Controllers/AboutUsController.php:163 src/Utils/Upsell.php:39
#: src/Utils/Upsell.php:65 template/admin_pages/welcome/features.php:45
msgid "Recovery Points"
msgstr "Punti di recupero"

#: src/Utils/Upsell.php:72
msgid "Streamlined Installer"
msgstr "Installazione semplificata"

#: src/Controllers/AboutUsController.php:146 src/Utils/Upsell.php:48
#: src/Utils/Upsell.php:71
msgid "Drag & Drop Installs"
msgstr "Installazioni drag & drop"

#: template/admin_pages/settings/general/general.php:308
msgid "AJAX Call Error!"
msgstr "Errore nella chiamata AJAX!"

#: template/mocks/import/import.php:17 views/packages/main/packages.php:128
msgid "Import"
msgstr "Importa"

#: views/packages/main/packages.php:132 views/tools/controller.php:26
msgid "Recovery"
msgstr "Recupero"

#: template/mocks/import/import.php:212
msgid "Drag and Drop Import is not available in Duplicator Lite!"
msgstr "L'importazione \"trascina e rilascia\" non è disponibile in Duplicator Lite!"

#: classes/package/class.pack.database.php:602
msgid "Shell mysql dump error. Change SQL Mode to the \"PHP Code\" in the Duplicator > Settings > Backups."
msgstr "Errore nel dump della shell mysql. Modifica la modalità SQL in \"Codice PHP\" in Duplicator > Impostazioni > Backup."

#: ctrls/class.web.services.php:177
msgid "Notice with that ID doesn't exist."
msgstr "Non esiste un avviso con questo ID."

#: ctrls/class.web.services.php:168
msgid "Invalid Request"
msgstr "Richiesta non valida"

#: ctrls/class.web.services.php:161 deactivation.php:410
#: src/Views/AdminNotices.php:139
msgid "Security issue"
msgstr "Problema di sicurezza"

#: ctrls/ctrl.tools.php:114 ctrls/ctrl.ui.php:91 deactivation.php:415
msgid "Invalid Request."
msgstr "Richiesta non valida."

#: views/packages/main/packages.php:200 views/packages/main/s3.build.php:218
msgid "Installer Name:"
msgstr "Nome installer:"

#: views/packages/main/packages.php:198
msgid "Installer Name"
msgstr "Nome installer"

#: views/packages/main/packages.php:29
msgid "When clicking the Installer download button, the 'Save as' dialog is defaulting the name to '[name]_[hash]_[time]_installer.php'. This is the secure and recommended option.  For more information, go to: Settings > Backups Tab > Installer > Name or click on the gear icon at the top of this page.<br/><br/>To quickly copy the hashed installer name, to your clipboard use the copy icon link or click the installer name and manually copy the selected text."
msgstr "Facendo clic sul pulsante di download dell'Installatore, la finestra di dialogo \"Salva con nome\" predefinisce il nome a \"[nome]_[hash]_[ora]_installer.php\". Questa è l'opzione sicura e consigliata. Per maggiori informazioni, vai su: Impostazioni > Scheda Backup > Installatore > Nome oppure fai clic sull'icona a forma di ingranaggio in alto in questa pagina.<br/><br/>Per copiare rapidamente il nome dell'installatore con hash negli appunti, usa il link dell'icona di copia oppure fai clic sul nome dell'installatore e copia manualmente il testo selezionato."

#: views/packages/main/packages.php:26
msgid "When clicking the Installer download button, the 'Save as' dialog is currently defaulting the name to 'installer.php'. To improve the security and get more information, go to: Settings > Backups Tab > Installer > Name option or click on the gear icon at the top of this page."
msgstr "Facendo clic sul pulsante di download dell'Installatore, la finestra di dialogo \"Salva con nome\" attualmente predefinisce il nome \"installer.php\". Per migliorare la sicurezza e ottenere maggiori informazioni, vai su: Impostazioni > Scheda Backup > Installatore > Opzione Nome o fai clic sull'icona dell'ingranaggio in alto in questa pagina."

#: views/packages/details/detail.php:300 views/packages/main/s1.setup2.php:138
msgid "(Contents Path)"
msgstr "(Percorso contenuti)"

#: views/packages/details/detail.php:298 views/packages/main/s1.setup2.php:136
msgid "(Legacy Path)"
msgstr "(Percorso legacy)"

#: views/packages/main/s1.setup2.php:113
msgid "[Storage Options]"
msgstr "[Opzioni di archiviazione]"

#: views/packages/main/s3.build.php:208
msgid "[Copy Installer Name to Clipboard]"
msgstr "[Copia il nome dell'installer negli appunti]"

#: views/packages/main/s2.scan3.php:566
msgid "The %s is set to create Backups in the 'DupArchive' format.  This custom format is used to overcome budget host constraints. With DupArchive, Duplicator is restricted to processing sites up to %s.  To process larger sites, consider these recommendations. "
msgstr "Il sito %s è impostato per creare backup nel formato \"DupArchive\". Questo formato personalizzato si usa per superare i vincoli di budget degli host. Con DupArchive, Duplicator si limita a elaborare siti fino a %s. Per elaborare siti più grandi, prendi in considerazione le seguenti raccomandazioni. "

#: views/packages/main/s2.scan3.php:560
msgid "Click for recommendations."
msgstr "Fai clic per i consigli."

#: views/packages/main/s2.scan3.php:522
msgid "The database user for this WordPress site does NOT sufficient permissions to write stored procedures or functions to the sql file of the archive.  Stored procedures will not be added to the sql file."
msgstr "L'utente del database per questo sito WordPress NON dispone di autorizzazioni sufficienti per scrivere procedure o funzioni memorizzate nel file sql dell'archivio. Le procedure memorizzate non verranno aggiunte al file sql."

#: views/packages/main/s2.scan3.php:518
msgid "The database user for this WordPress site has sufficient permissions to write stored procedures and functions to the sql file of the archive. [The command SHOW CREATE FUNCTION will work.]"
msgstr "L'utente del database per questo sito WordPress dispone di autorizzazioni sufficienti per scrivere procedure o funzioni memorizzate nel file sql dell'archivio. [Il comando SHOW CREATE FUNCTION funzionerà correttamente.]"

#: views/packages/main/s1.setup1.php:230
msgid "The function mysqli_real_escape_string is not working properly. Please consult host support and ask them to switch to a different PHP version or configuration."
msgstr "La funzione mysqli_real_escape_string non funziona correttamente. Consulta il supporto dell'host e chiedi loro di passare ad una versione o configurazione PHP diversa."

#: views/settings/packages.php:361
msgid "read this section"
msgstr "leggi questa sezione"

#: views/settings/packages.php:360
msgid "To understand the importance and usage of the installer name, please"
msgstr "Per comprendere l'importanza e l'utilizzo del nome dell'installer,"

#: views/settings/packages.php:334
msgid "Default 'Save as' name:"
msgstr "Nome predefinito 'Salva con nome':"

#: src/Controllers/StorageController.php:34
msgid "Advanced Storage"
msgstr "Archiviazione avanzata"

#: views/settings/storage.php:122
msgid "Save Storage Settings"
msgstr "Salva le impostazioni di archiviazione"

#: views/settings/storage.php:114
msgid "Only disable this option if issues occur when downloading either the installer/archive files."
msgstr "Disabilitare questa opzione solo se si verificano problemi durante il download dei file installer/archivio."

#: views/settings/storage.php:113
msgid "When checked this setting will prevent Duplicator from laying down an .htaccess file in the storage location above."
msgstr "Se selezionata, questa impostazione impedirà a Duplicator di impostare un file .htaccess nella posizione di archiviazione."

#: views/settings/storage.php:107
msgid "Apache .htaccess"
msgstr "Apache .htaccess"

#: views/settings/storage.php:102
msgid "More Advanced Storage Options..."
msgstr "Altre opzioni di archiviazione avanzate..."

#: views/settings/storage.php:97
msgid "The storage location is where all Backup files are stored to disk. If your host has troubles writing content to the 'Legacy Path' then use the 'Contents Path'.  Upon clicking the save button all files are moved to the new location and the previous path is removed."
msgstr "Il percorso di archiviazione è il luogo in cui tutti i file di backup vengono salvati su disco. Se il tuo host ha problemi a scrivere il contenuto nel \"Percorso legacy\", usa il \"Percorso del contenuto\". Facendo clic sul pulsante Salva, tutti i file vengono spostati nella nuova posizione e il percorso precedente viene rimosso."

#: views/settings/storage.php:91
msgid "Contents Path:"
msgstr "Percorso dei contenuti:"

#: views/settings/storage.php:82
msgid "Legacy Path:"
msgstr "Percorso legacy:"

#: views/settings/storage.php:18
msgid "Storage Settings Saved"
msgstr "Impostazioni di archiviazione salvate"

#: views/settings/storage.php:49
msgid "Check the parent folder permissions. ( <i>%s</i> )"
msgstr "Controlla le autorizzazioni della cartella principale. ( <i>%s</i> )"

#: views/settings/storage.php:48
msgid "Duplicator can't change the storage folder to <i>%s</i>"
msgstr "Duplicator non può modificare la cartella di archiviazione in <i>%s</i>"

#: views/settings/storage.php:45
msgid "Storage folder move problem"
msgstr "Problema nello spostamento della cartella di archiviazione"

#: assets/js/javascript.php:278 assets/js/javascript.php:282
msgid "Copy to Clipboard!"
msgstr "Copia negli appunti!"

#: assets/js/javascript.php:272 assets/js/javascript.php:274
msgid "unable to copy"
msgstr "impossibile copiare"

#: assets/js/javascript.php:272
msgid "Copied: "
msgstr "Copiato: "

#: ctrls/class.web.services.php:94 ctrls/class.web.services.php:98
#: ctrls/class.web.services.php:102
msgid "Invalid request."
msgstr "Richiesta non valida."

#: src/Views/AdminNotices.php:318
msgid "Plugin(s) listed here have been deactivated during installation to help prevent issues. Please activate them to finish this migration: "
msgstr "Il o i plugin elencati qui sono stati disattivati durante l'installazione per prevenire eventuali problemi. Attivalo/i per completare la migrazione:"

#: src/Views/AdminNotices.php:317
msgid "Warning!"
msgstr "Attenzione!"

#: views/packages/main/s2.scan2.php:221
msgid ""
"The Backup created here cannot be migrated to a new server.\n"
"                                The Backup created here can be restored on the same server."
msgstr ""
"Il backup creato qui non può essere migrato su un nuovo server.\n"
"                                Il backup creato in questo punto può essere ripristinato sullo stesso server."

#: views/packages/main/s2.scan2.php:217
msgid "The Backup created here can be migrated to a new server."
msgstr "Il backup creato qui può essere migrato su un nuovo server."

#: views/packages/main/s2.scan2.php:209
msgid "Migration Status"
msgstr "Stato della migrazione"

#: src/Views/AdminNotices.php:426
msgid "Hide Notification"
msgstr "Nascondi notifica"

#: src/Views/AdminNotices.php:423
msgid "Sure! I'd love to help"
msgstr "Sicuro! Mi piacerebbe aiutare"

#: src/Views/AdminNotices.php:406
msgid "Congrats!"
msgstr "Congratulazioni!"

#: views/packages/main/s1.setup2.php:550
msgid "This feature works only with hosts that support cPanel."
msgstr "Questa funzionalit&agrave; funziona solo con gli host che supportano cPanel."

#: views/packages/main/s1.setup2.php:548
msgid "Duplicator Pro!"
msgstr "Duplicatore Pro!"

#: views/packages/main/s1.setup2.php:546
msgid "This feature is only availble in "
msgstr "Questa funzionalità è disponibile solo in "

#: views/packages/main/s1.setup2.php:545
msgid "Create the database and database user at install time without leaving the installer!"
msgstr "Crea il database e l'utente del database al momento dell'installazione senza uscire dal programma di installazione!"

#: views/packages/main/s1.setup2.php:448 views/settings/packages.php:340
msgid "Basic"
msgstr "Base"

#: views/packages/main/s1.setup2.php:440
msgid "Prefills"
msgstr "Precompilate"

#: views/packages/main/s1.setup2.php:425
msgid "Enabling this option will allow for basic password protection on the installer. Before running the installer the password below must be entered before proceeding with an install.  This password is a general deterrent and should not be substituted for properly keeping your files secure.  Be sure to remove all installer files when the install process is completed."
msgstr "L'attivazione di questa opzione consentirà la protezione con password di base dell'installer. Prima di eseguire l'installer, è necessario immettere la password riportata di seguito prima di procedere con un'installazione.  Questa password è un deterrente generale e non deve essere sostituita per mantenere i file al sicuro.  Assicurarsi di rimuovere tutti i file dell'installer al termine del processo di installazione."

#: views/packages/main/s1.setup2.php:424
msgid "Security:"
msgstr "Sicurezza:"

#: views/packages/main/s1.setup2.php:422
msgid "Enable Password Protection"
msgstr "Abilita la protezione delle password"

#: views/packages/details/detail.php:479 views/packages/main/s1.setup2.php:415
msgid "Security"
msgstr "Sicurezza"

#: views/packages/main/s1.setup2.php:410
msgid "Branding is a way to customize the installer look and feel.  With branding you can create multiple brands of installers."
msgstr "Il branding è un modo per personalizzare l'aspetto dell'installer.  Con il branding è possibile creare più brand dell'istaller."

#: views/packages/main/s1.setup2.php:404 views/packages/main/s1.setup2.php:409
msgid "Branding"
msgstr "Brand"

#: views/packages/main/s1.setup2.php:393
msgid "All values in this section are OPTIONAL! If you know ahead of time the database input fields the installer will use, then you can optionally enter them here and they will be prefilled at install time.  Otherwise you can just enter them in at install time and ignore all these options in the Installer section."
msgstr "Tutti i valori in questa sezione sono FACOLTATIVI! Se conosci in anticipo i campi di input del database che verrà utilizzato dall'installer, puoi inserirli facoltativamente qui e saranno precompilati al momento dell'installazione. Altrimenti puoi inserirli al momento dell'installazione e ignorare tutte le opzioni nella sezione dell'installer."

#: views/packages/main/s1.setup2.php:392
msgid "Setup/Prefills"
msgstr "Configurazione/Precompilazioni"

#: views/packages/main/s1.setup2.php:377
msgid "Installer password protection is off"
msgstr "La password di protezione dell'installer &egrave; disattivata"

#: views/packages/main/s1.setup2.php:374
msgid "Installer password protection is on"
msgstr "La password di protezione dell'installer &egrave; attivata"

#: views/packages/main/s1.setup2.php:309
msgid "This is an advanced database backwards compatibility feature that should ONLY be used if having problems installing Backups. If the database server version is lower than the version where the Backup was built then these options may help generate a script that is more compliant with the older database server. It is recommended to try each option separately starting with mysql40."
msgstr "Si tratta di una caratteristica avanzata di retrocompatibilità del database che dovrebbe essere utilizzata SOLO in caso di problemi nell'installazione dei backup. Se la versione del server del database è inferiore a quella in cui è stato creato il backup, queste opzioni possono aiutare a generare uno script più compatibile con il server del database più vecchio. Ti consigliamo di provare ogni opzione separatamente partendo da mysql40."

#: template/parts/filters/package_components.php:178
msgid "<b>Overview:</b><br> This advanced option excludes all files from the Backup.  Only the database and a copy of the installer.php will be included in the archive.zip file. The option can be used for backing up and moving only the database."
msgstr "<b>Panoramica:</b><br> Questa opzione avanzata esclude tutti i file dal backup. Solo il database e una copia del file installer.php saranno inclusi nel file archive.zip. Questa opzione può essere utilizzata per il backup e lo spostamento del solo database."

#: views/packages/main/s2.scan1.php:455
msgid "Fail"
msgstr "Fallito"

#: views/packages/main/s2.scan1.php:274
msgid "Yes. Continue without applying any file filters."
msgstr "Sì, continua senza applicare filtri ai file."

#: views/packages/main/s2.scan1.php:272
msgid "To apply a \"Quick Filter\" click the \"Add Filters & Rescan\" button"
msgstr "Per applicare un \"Filtro rapido\" fai clic sul pulsante \"Aggiungi filtri e riesegui scansione\""

#: views/packages/main/s2.scan1.php:271
msgid "At least one or more checkboxes were checked in \"Quick Filters\"."
msgstr "Uno o più checkbox sono stati selezionati in \"Filtri rapidi\"."

#: views/packages/main/s2.scan1.php:269
msgid "Do you want to continue?"
msgstr "Vuoi continuare?"

#: views/packages/main/s2.scan1.php:167
msgid "Input fields not valid"
msgstr "Campi di input non validi"

#: views/packages/main/controller.php:10
msgid "Please retry by going to the"
msgstr "Riprova accedendo a"

#: views/packages/main/controller.php:9
msgid "An invalid request was made to this page."
msgstr "È stata effettuata una richiesta non valida a questa pagina."

#: views/packages/main/s2.scan2.php:194 views/packages/main/s2.scan2.php:199
msgid "upgrade to pro"
msgstr "passa a pro"

#: views/packages/main/s2.scan2.php:188
msgid "Duplicator does not support WordPress multisite migrations.  We strongly recommend using Duplicator Pro which currently supports full multisite migrations and various other subsite scenarios."
msgstr "Duplicator non supporta le migrazioni di WordPress multisito. Si consiglia di utilizzare Duplicator Pro che attualmente supporta migrazioni complete multisito e molte altre possibilità per i sottositi."

#: views/packages/main/s2.scan2.php:168
msgid "If the scanner is unable to locate the wp-config.php file in the root directory, then you will need to manually copy it to its new location. This check will also look for core WordPress paths that should be included in the Backup for WordPress to work correctly."
msgstr "Se la scansione non riesce a individuare il file wp-config.php nella directory principale, dovrai copiarlo manualmente nella nuova posizione. Questo controllo cercherà anche i percorsi fondamentali di WordPress che dovrebbero essere inclusi nel backup affinché WordPress funzioni correttamente."

#: views/packages/main/s2.scan2.php:123
msgid "It is recommended to have a version of WordPress that is greater than %1$s.  Older version of WordPress can lead to migration issues and are a security risk. If possible please update your WordPress site to the latest version."
msgstr "Si consiglia di disporre di una versione di WordPress maggiore di %1$s.  Le versioni precedenti di WordPress possono causare problemi di migrazione e rappresentano un rischio per la sicurezza. Se possibile, aggiorna il tuo sito WordPress all'ultima versione."

#: views/packages/main/s2.scan2.php:69
msgid "Timeouts may occur for larger Backups when [max_execution_time] time in the php.ini is too low.  A value of 0 (recommended) indicates that PHP has no time limits. An attempt is made to override this value if the server allows it."
msgstr "I timeout possono verificarsi per i backup più grandi quando il tempo [max_execution_time] nel php.ini è troppo basso. Un valore di 0 (consigliato) indica che PHP non ha limiti di tempo. Verrà fatto un tentativo di sovrascrivere questo valore se il server lo consente."

#: views/packages/main/s3.build.php:496
msgid "If the value is [dynamic] then its possible for PHP to run longer than the default.  If the value is [fixed] then PHP will not be allowed to run longer than the default. <br/><br/> If this value is larger than the [Allowed Runtime] above then the web server has been enabled with a timeout cap and is overriding the PHP max time setting."
msgstr "Se il valore è [dinamico], è possibile che PHP esegua elaborazioni più a lungo del valore predefinito. Se il valore è [fisso], PHP non potrà essere eseguito più a lungo del valore predefinito.<br/><br/> Se questo valore è maggiore del [Tempo di esecuzione consentito], allora il server web è stato abilitato con un limite di timeout e sovrascrive l'impostazione del tempo massimo PHP."

#: views/packages/main/s3.build.php:454
msgid "This option is available on some hosts that allow for users to adjust server configurations.  With this option you will be directed to an FAQ page that will show various recommendations you can take to improve/unlock constraints set up on this server."
msgstr "Questa opzione è disponibile su alcuni host che consentono agli utenti di regolare le configurazioni del server. Con questa opzione verrai indirizzato a una pagina delle domande frequenti che mostrerà vari consigli che puoi seguire per migliorare/sbloccare i vincoli impostati su questo server."

#: views/packages/main/s3.build.php:453
msgid "OPTION 4:"
msgstr "OPZIONE 4:"

#: views/packages/main/s3.build.php:449
msgid "Option 4: Configure Server"
msgstr "Opzione 4: configurazione del server"

#: views/packages/main/s3.build.php:415
msgid " Overview"
msgstr "Panoramica"

#: views/packages/main/s3.build.php:411
msgid "A two-part install minimizes server load and can avoid I/O and CPU issues encountered on some budget hosts. With this procedure you simply build a 'database-only' Backup, manually move the website files, and then run the installer to complete the process."
msgstr "Un'installazione in due parti riduce al minimo il carico del server e può evitare i problemi di I/O e CPU ospitati su alcuni host economici. Con questa procedura devi semplicemente creare un backup \"solo database\", spostare manualmente i file del sito web e poi eseguire il programma di installazione per completare il processo."

#: views/packages/main/s3.build.php:406
msgid "Option 3: Two-Part Install"
msgstr "Opzione 3: installazione in due parti"

#: views/packages/main/s3.build.php:393
msgid "On some servers the build will continue to run in the background. To validate if a build is still running; open the 'tmp' folder above and see if the Backup file is growing in size or check the main Backups screen to see if the Backup completed. If it is not then your server has strict timeout constraints."
msgstr "Su alcuni server la build continuerà a essere eseguita in background. Per verificare se una build è ancora in esecuzione, apri la cartella \"tmp\" in alto e vedi se il file di backup sta aumentando di dimensioni oppure controlla la schermata principale dei backup per vedere se il backup è stato completato. Se così non fosse, il tuo server ha dei vincoli di timeout molto rigidi."

#: views/packages/main/s3.build.php:383
msgid "Retry Build With Filters"
msgstr "Riprova a compilare con filtri"

#: views/packages/main/s3.build.php:378
msgid "For example, you could  filter out the  \"/wp-content/uploads/\" folder to create the Backup then move the files from that directory over manually.  If this work-flow is not desired or does not work please check-out the other options below."
msgstr "Per esempio, puoi filtrare la cartella \"/wp-content/uploads/\" per creare il backup e spostare manualmente i file da quella directory. Se questo flusso di lavoro non è desiderato o non funziona, controlla le altre opzioni riportate di seguito."

#: views/packages/main/s3.build.php:373
msgid "The first pass for reading files on some budget hosts maybe slow and have conflicts with strict timeout settings setup by the hosting provider.  In these cases, it is recommended to retry the build by adding file filters to larger files/directories."
msgstr "Il primo passaggio per la lettura di file su alcuni host economici potrebbe essere lento e avere conflitti con le rigide impostazioni di timeout impostate dal provider di hosting. In questi casi, si consiglia di riprovare la compilazione aggiungendo filtri di file a file/directory più grandi."

#: views/packages/main/s3.build.php:368
msgid "Option 2: File Filters"
msgstr "Opzione 2: filtri dei file"

#: views/packages/main/s3.build.php:342
msgid "Build a new Backup using the new engine format."
msgstr "Crea un nuovo backup usando il nuovo formato del motore."

#: views/packages/main/s3.build.php:340
msgid "Enable DupArchive"
msgstr "Abilita DupArchive"

#: views/packages/main/s3.build.php:339
msgid "Go to Duplicator &gt; Settings &gt; Backups Tab &gt; Backup Engine &gt;"
msgstr "Vai su Duplicator &gt; Impostazioni &gt; Scheda Backup &gt; Backup Engine &gt;"

#: views/packages/main/s3.build.php:337
msgid "On the scanner step check to make sure your Backup is under 500MB. If not see additional options below."
msgstr "Nella fase di scansione verifica che il tuo backup sia inferiore a 500 MB. In caso contrario, consulta le opzioni aggiuntive riportate di seguito."

#: views/packages/main/s3.build.php:315
msgid "Enable the DupArchive format which is specific to Duplicator and designed to perform better on constrained budget hosts."
msgstr "Abilita il formato DupArchive che è specifico per Duplicator e progettato per funzionare meglio su host economici."

#: views/packages/main/s3.build.php:310
msgid "Option 1: DupArchive"
msgstr "Opzione 1: DupArchive"

#: views/packages/main/s3.build.php:158
msgid "This may take several minutes to complete."
msgstr "Il completamento dell'operazione potrebbe richiedere diversi minuti."

#: views/packages/main/s3.build.php:157
msgid "Keep this window open and do not close during the build process."
msgstr "Tieni aperta questa finestra e non chiuderla durante il processo di creazione."

#: views/packages/main/s2.scan3.php:804
msgid "Auto File Filters"
msgstr "Filtri dei file automatici"

#: views/packages/main/s2.scan3.php:798
msgid "Auto Directory Filters"
msgstr "Filtri delle directory automatici"

#: views/packages/main/s2.scan3.php:600
msgid "- Switch to the %s which requires a capable hosting provider (VPS recommended)."
msgstr "- Passa a %s che richiede un hosting provider performante (consigliato VPS)."

#: views/packages/main/s2.scan3.php:599
msgid "ZipArchive Engine"
msgstr "Motore ZipArchive"

#: views/packages/main/s2.scan3.php:584
msgid "- In %s consider adding file/directory or database table filters."
msgstr "- In %s considera l'aggiunta di filtri di file/directory o tabelle di database."

#: views/packages/main/s2.scan3.php:582
msgid "- In the 'Size Checks' section above consider adding filters (if notice is shown)."
msgstr "- Nella sezione 'Controlli delle dimensioni', considera l'aggiunta di filtri (se viene visualizzato un avviso)."

#: views/packages/main/s2.scan3.php:580
msgid "- Add data filters to get the Backup size under %s: "
msgstr "- Aggiungi filtri ai dati per ottenere le dimensioni del backup sotto %s: "

#: views/packages/main/s2.scan3.php:579
msgid "Step 1"
msgstr "Passo 1"

#: views/packages/main/s2.scan3.php:558
msgid "The build can't continue because the total size of files and the database exceeds the %s limit that can be processed when creating a DupArchive Backup. "
msgstr "L'elaborazione non può continuare perché la dimensione totale dei file e del database supera il limite di %s che può essere elaborato durante la creazione di un backup DupArchive. "

#: views/packages/main/s2.scan3.php:548
msgid "The total size of the site (files plus  database)."
msgstr "La dimensione totale del sito (file e database)."

#: views/packages/main/s2.scan3.php:547
msgid "Total Size:"
msgstr "Dimensione totale:"

#: views/packages/main/s2.scan3.php:451
msgid "The notices for tables are %1$s records or names with upper-case characters.  Individual tables will not trigger a notice message, but can help narrow down issues if they occur later on."
msgstr "Gli avvisi per le tabelle sono %1$s record o nomi con caratteri maiuscoli. Le singole tabelle non genereranno un messaggio di avviso, ma possono aiutare a limitare i problemi se si verificano in seguito."

#: views/packages/main/s2.scan3.php:444
msgid "Total size and row counts are approximate values.  The thresholds that trigger notices are %1$s records total for the entire database.  Larger databases take more time to process.  On some budget hosts that have cpu/memory/timeout limits this may cause issues."
msgstr "Le dimensioni totali e i conteggi delle righe sono valori approssimativi.  Le soglie che attivano gli avvisi sono %1$s record totali per l'intero database. I database più grandi richiedono più tempo per l'elaborazione. Su alcuni host economici che hanno limiti di cpu/memoria/timeout potrebbero sorgere problemi."

#: views/packages/main/s2.scan3.php:368
msgid "PHP is unable to read the following items and they will NOT be included in the Backup.  Please work with your host to adjust the permissions or resolve the symbolic-link(s) shown in the lists below.  If these items are not needed then this notice can be ignored."
msgstr "PHP non è in grado di leggere i seguenti elementi e NON saranno inclusi nel backup. Ti invitiamo a collaborare con il tuo host per modificare le autorizzazioni o correggere i link simbolici indicati negli elenchi sottostanti. Se questi elementi non sono necessari, puoi ignorare questo avviso."

#: views/packages/main/s2.scan3.php:295
msgid "If this environment/system and the system where it will be installed are set up to support Unicode and long paths then these filters can be ignored.  If you run into issues with creating or installing a Backup, then is recommended to filter these paths."
msgstr "Se questo ambiente/sistema e il sistema in cui verrà installato sono impostati per supportare Unicode e percorsi lunghi, questi filtri possono essere ignorati.  Se hai problemi con la creazione o l'installazione di un backup, è consigliabile filtrare questi percorsi."

#: views/packages/main/s2.scan3.php:239
msgid ""
"An \"Addon Site\" is a separate WordPress site(s) residing in subdirectories within this site. If you confirm these to be separate sites, \n"
"                        then it is recommended that you exclude them by checking the corresponding boxes below and clicking the 'Add Filters & Rescan' button.  To backup the other sites \n"
"                        install the plugin on the sites needing to be backed-up."
msgstr ""
"Un \"Sito Add-on\" è un sito o più siti WordPress separati che risiedono in sottodirectory all'interno di questo sito. Se confermi che si tratta di siti separati, \n"
"                        ti consigliamo di escluderli selezionando le caselle corrispondenti qui sotto e facendo clic sul pulsante \"Aggiungi filtri e scansiona di nuovo\".  Per eseguire il backup degli altri siti \n"
"                        installa il plugin sui siti di cui è necessario eseguire il backup."

#: views/packages/main/s2.scan3.php:198
msgid "No large files found during this scan.  If you're having issues building a Backup click the back button and try adding a file filter to non-essential files paths like wp-content/uploads.   These excluded files can then be manually moved to the new location after you have ran the migration installer."
msgstr "Non sono stati trovati file di grandi dimensioni durante questa scansione. Se hai problemi a creare un backup, fai clic sul pulsante indietro e prova ad aggiungere un filtro ai percorsi dei file non essenziali come wp-content/uploads. Questi file esclusi possono essere spostati manualmente nella nuova posizione dopo aver eseguito il programma di installazione della migrazione."

#: views/packages/main/s2.scan3.php:152
msgid "Files over %1$s are listed below. Larger files such as movies or zipped content can cause timeout issues on some budget hosts.  If you are having issues creating a Backup try excluding the directory paths below or go back to Step 1 and add them."
msgstr "I file superiori a %1$s sono elencati di seguito. I file più grandi, come i film o i contenuti zippati, possono causare problemi di timeout su alcuni host economici.  Se hai problemi a creare un backup, prova a escludere i percorsi delle directory qui sotto o torna al punto 1 e aggiungili."

#: views/packages/main/s2.scan3.php:108
msgctxt "%s size in bytes"
msgid "This notice is triggered at [%s] and can be ignored on most hosts.  If during the build process you see a \"Host Build Interrupt\" message then this host has strict processing limits.  Below are some options you can take to overcome constraints set up on this host."
msgstr "Questo avviso è generato a [%s] byte e può essere ignorato sulla maggior parte degli host.  Se durante la compilazione del pacchetto vedi un messaggio di \"Compilazione interrotta dall'host\" significa che questo host ha limiti di elaborazione ristretti.  Qui sotto ci sono alcune opzioni che puoi tentare per superare i vincoli impostati su questo host."

#: views/packages/main/s2.scan3.php:83
msgid " Disable the advanced option to re-enable file controls."
msgstr "Disabilita l'opzione avanzata per riattivare i controlli dei file."

#: views/packages/main/s2.scan3.php:81
msgid "All file checks are skipped. This could cause problems during extraction if problematic files are included."
msgstr "Tutti i controlli dei file vengono saltati. Ciò potrebbe causare problemi durante l'estrazione se vengono inclusi file problematici."

#: views/packages/main/s2.scan3.php:54
msgid "This size includes only files BEFORE compression is applied. It does not include the size of the database script or any applied filters.  Once complete the Backup size will be smaller than this number."
msgstr "Questa dimensione include solo i file PRIMA dell'applicazione della compressione. Non include le dimensioni dello script del database o di eventuali filtri applicati. Una volta completato, le dimensioni del backup saranno inferiori a questo numero."

#: views/packages/main/packages.php:393
msgid "A Backup is being processed. Retry later."
msgstr "È in corso l'elaborazione di un backup. Riprova più tardi."

#: views/packages/main/packages.php:392
msgid "Alert!"
msgstr "Avviso!"

#: views/packages/main/packages.php:374
msgid "No selections made! Please select at least one Backup to delete."
msgstr "Nessuna selezione effettuata! Seleziona almeno un backup da eliminare."

#: views/packages/main/packages.php:368
msgid "No selections made! Please select an action from the \"Bulk Actions\" drop down menu."
msgstr "Nessuna selezione effettuata! Seleziona un'azione dal menu a discesa \"Azioni di massa\"."

#: views/packages/main/packages.php:355
msgid "Items"
msgstr "Elementi"

#: views/packages/main/packages.php:337
msgid "Current Server Time"
msgstr "Orario del Server"

#: views/packages/main/packages.php:263
msgid "To stop or reset this Backup build goto Settings > Advanced > Reset Backups"
msgstr "Per interrompere o reimpostare la creazione di questo backup vai in Impostazioni > Avanzate > Reimposta backup."

#: views/packages/main/packages.php:262
msgid "Backup Build Running"
msgstr "Creazione del backup in corso"

#: views/packages/main/s1.setup1.php:156
msgid "For any issues in this section please contact your hosting provider or server administrator.  For additional information see our online documentation."
msgstr "Per qualsiasi problema in questa sezione, contatta il tuo host provider o amministratore del server. Per ulteriori informazioni consulta la nostra documentazione online."

#: views/packages/main/s1.setup1.php:137
msgid "Safe Mode should be set to Off in you php.ini file and is deprecated as of PHP 5.3.0."
msgstr "La modalità provvisoria dovrebbe essere impostata su Off nel file php.ini ed è deprecata a partire da PHP 5.3.0."

#: views/packages/main/s1.setup1.php:114
msgid "PHP versions 5.2.9+ or higher is required."
msgstr "È richiesta la versione PHP 5.2.9+ o successive."

#: template/mocks/storage/popup.php:30
msgid "Set up one-time storage locations and automatically push the Backup to your destination."
msgstr "Imposta le posizioni di archiviazione una tantum e invia automaticamente il backup a destinazione."

#: views/packages/details/detail.php:497 views/packages/main/s1.setup2.php:431
msgid "Show/Hide Password"
msgstr "Mostra/nascondi password"

#: views/packages/details/detail.php:337 views/packages/main/s1.setup2.php:167
msgid "Duplicator Pro allows you to create a Backup and store it at a custom location on this server or to a remote cloud location such as Google Drive, Amazon, Dropbox and many more."
msgstr "Duplicator Pro ti permette di creare un backup e di archiviarlo in una posizione personalizzata su questo server o in una posizione cloud remota come Google Drive, Amazon, Dropbox e molti altri."

#: views/settings/license.php:25
msgid "Duplicator Lite"
msgstr "Duplicator Lite"

#: src/Controllers/AboutUsController.php:142
msgid "Overwrite Live Site"
msgstr "Sovrascrivi sito live"

#: views/settings/about-info.php:105
msgid "LinkedIn"
msgstr "LinkedIn"

#: views/settings/about-info.php:102
msgid "Twitter"
msgstr "Twitter"

#: views/settings/about-info.php:99
msgid "Facebook"
msgstr "Facebook"

#: views/settings/about-info.php:49
msgid ""
"Duplicator can streamline your workflow and quickly clone/migrate a WordPress site. The plugin helps admins, designers and \n"
"                        developers speed up the migration process of moving a WordPress site. Please help us continue development by giving the plugin a \n"
"                        5 star and consider purchasing our Pro product."
msgstr ""
"Duplicator è in grado di snellire il tuo flusso di lavoro e di clonare/migrare rapidamente un sito WordPress. Il plugin aiuta chi amministra, sviluppa e disegna siti\n"
"                        a velocizzare il processo di migrazione di un sito WordPress. Aiutaci a continuare lo sviluppo dando al plugin \n"
"                        5 stelle e considera l'acquisto del nostro prodotto Pro."

#: views/settings/packages.php:321
msgid " Valid only when Backup Engine for ZipArchive is enabled."
msgstr " Valido solo se Backup Engine for ZipArchive è abilitato."

#: views/settings/packages.php:293
msgid "This option is recommended for large sites or sites on constrained servers."
msgstr "Questa opzione è consigliata per siti di grandi dimensioni o siti su server vincolati."

#: views/settings/packages.php:290
msgid "Creates a custom Backup format (archive.daf)."
msgstr "Crea un formato di backup personalizzato (archivio.daf)."

#: views/settings/packages.php:278
msgid "This option uses the internal PHP ZipArchive classes to create a zip file."
msgstr "Questa opzione utilizza le classi PHP ZipArchive interne per creare un file Zip."

#: views/settings/packages.php:275
msgid "Creates a Backup format (archive.zip)."
msgstr "Crea un formato di backup (archivio.zip)."

#: views/settings/packages.php:267
msgid "DupArchive"
msgstr "DupArchive"

#: views/packages/main/s2.scan3.php:732
msgid "Archive Engine"
msgstr "Motore dell'archivio"

#: views/settings/packages.php:224
msgid "<br><br><i>Multi-Threaded mode is only available in Duplicator Pro.</i>"
msgstr "<br><br><i>La modalità multi-thread è disponibile solo in Duplicator Pro.</i>"

#: views/settings/packages.php:222
msgid "Single-Threaded mode attempts to create the entire database script in one request.  Multi-Threaded mode allows the database script to be chunked over multiple requests.  Multi-Threaded mode is typically slower but much more reliable especially for larger databases."
msgstr "La modalità a thread singolo tenta di creare l'intero script del database in una richiesta. La modalità multi-thread consente di suddividere lo script del database su più richieste. La modalità multi-thread è in genere più lenta ma molto più affidabile, soprattutto per i database più grandi."

#: views/settings/packages.php:220
msgid "PHP Code Mode:"
msgstr "Modalità di codifica PHP:"

#: views/settings/packages.php:213
msgid "Single-Threaded"
msgstr "Single-Threaded"

#: views/settings/packages.php:181
msgid "Add a custom path if the path to mysqldump is not properly detected.   For all paths use a forward slash as the path seperator.  On Linux systems use mysqldump for Windows systems use mysqldump.exe.  If the path tried does not work please contact your hosting provider for details on the correct path."
msgstr "Aggiungi un percorso personalizzato se il percorso di mysqldump non viene rilevato correttamente. Per tutti i percorsi utilizzare una barra in avanti come separatore di percorso. Sui sistemi Linux usa mysqldump, per i sistemi Windows usa mysqldump.exe. Se il percorso provato non funziona, contatta il tuo fornitore di hosting per i dettagli sul percorso corretto."

#: template/admin_pages/settings/general/general.php:368
msgid "Backups successfully reset"
msgstr "I backup sono stati ripristinati con successo"

#: assets/js/duplicator/dup.util.php:97
#: template/admin_pages/settings/general/general.php:325
#: template/admin_pages/settings/general/general.php:378
msgid "RESPONSE ERROR!"
msgstr "ERRORE NELLA RISPOSTA!"

#: assets/js/duplicator/dup.util.php:106
msgid "Ajax request error"
msgstr "Errore di richiesta Ajax"

#: assets/js/duplicator/dup.util.php:106
msgid "AJAX ERROR! "
msgstr "ERRORE AJAX! "

#: template/admin_pages/settings/general/general.php:302
msgid "No"
msgstr "No"

#: src/Core/Notifications/Review.php:98
#: template/admin_pages/settings/general/general.php:301
msgid "Yes"
msgstr "Sì"

#: template/admin_pages/settings/general/general.php:298
msgid "Resetting settings, Please Wait..."
msgstr "Ripristino delle impostazioni, attendere..."

#: template/admin_pages/settings/general/general.php:297
msgid "This will clear and reset all of the current temporary Backups.  Would you like to continue?"
msgstr "Questo cancellerà e reimposterà tutti i backup temporanei correnti.  Vuoi continuare?"

#: template/admin_pages/settings/general/general.php:269
msgid "Check this option if other plugins/themes CSS files are conflicting with Duplicator."
msgstr "Seleziona questa opzione se altri file CSS di plugin/temi sono in conflitto con Duplicator."

#: template/admin_pages/settings/general/general.php:257
#: template/admin_pages/settings/general/general.php:271
msgid "Do not modify this setting unless you know the expected result or have talked to support."
msgstr "Non modificare questa impostazione a meno che tu non conosca il risultato atteso o non abbia parlato con l'assistenza."

#: template/admin_pages/settings/general/general.php:255
msgid "Check this option if other plugins/themes JavaScript files are conflicting with Duplicator."
msgstr "Seleziona questa opzione se altri file JavaScript di plugin/temi sono in conflitto con Duplicator."

#: template/admin_pages/settings/general/general.php:242
msgid "If enabled all files check on scan will be skipped before Backup creation.  In some cases, this option can be beneficial if the scan process is having issues running or returning errors."
msgstr "Se abilitato, tutti i file selezionati durante la scansione verranno saltati prima della creazione del backup. In alcuni casi, questa opzione può essere utile se il processo di scansione ha problemi di esecuzione o restituisce errori."

#: template/admin_pages/settings/general/general.php:239
#: template/admin_pages/welcome/intro.php:48
msgid "Skip"
msgstr "Salta"

#: template/admin_pages/settings/general/general.php:230
msgid "This action should only be used if the Backups screen is having issues or a build is stuck."
msgstr "Questa azione deve essere utilizzata solo se la schermata dei backup ha dei problemi o se una build è bloccata."

#: template/admin_pages/settings/general/general.php:228
msgid "Reset Settings"
msgstr "Reimposta le impostazioni"

#: template/admin_pages/settings/general/general.php:224
msgid "This process will reset all Backups by deleting those without a completed status, reset the active Backup id and perform a cleanup of the build tmp file."
msgstr "Questo processo resetta tutti i backup eliminando quelli che non hanno uno stato completato, reimposta l'ID del backup attivo ed esegue una pulizia del file build tmp."

#: template/admin_pages/settings/general/general.php:213
msgid "Advanced"
msgstr "Avanzate"

#: template/admin_pages/settings/general/general.php:205
msgid "Download Trace Log"
msgstr "Scarica il tracciamento del log"

#: template/admin_pages/settings/general/general.php:197
msgid "WARNING: Only turn on this setting when asked to by support as tracing will impact performance."
msgstr "ATTENZIONE: attiva questa impostazione solo quando richiesto dall'assistenza poiché il tracciamento influirà sulle prestazioni."

#: template/admin_pages/settings/general/general.php:195
msgid "Turns on detailed operation logging. Logging will occur in both PHP error and local trace logs."
msgstr "Attiva la registrazione dettagliata delle operazioni. La registrazione si verificherà sia negli errori PHP che nei registri locali."

#: template/admin_pages/settings/general/general.php:189
msgid "Trace Log"
msgstr "Tracciamento del log"

#: views/parts/migration-clean-installation-files.php:51
msgid " If this process continues please see the previous FAQ link."
msgstr " Se questo processo continua, consulta il link FAQ precedente."

#: views/parts/migration-clean-installation-files.php:49
msgid "please retry the installer cleanup process"
msgstr "riprova il processo di pulizia del programma di installazione"

#: views/parts/migration-clean-installation-files.php:47
msgid "Some of the installer files did not get removed, "
msgstr "Alcuni dei file di installazione non sono stati rimossi, "

#: views/parts/migration-clean-installation-files.php:21
msgid "No Duplicator files were found on this WordPress Site."
msgstr "Non è stato trovato nessun file di Duplicator in questo sito WordPress."

#: views/parts/migration-clean-installation-files.php:27
#: views/tools/diagnostics/information.php:31
msgid "Removed"
msgstr "Rimosso"

#: views/tools/diagnostics/information.php:30
msgid "File Found: Unable to remove"
msgstr "File trovato: impossibile rimuovere"

#: classes/class.server.php:648
msgid "Architecture"
msgstr "Architettura"

#: classes/class.server.php:618
msgid ""
"If the value shows dynamic then this means its possible for PHP to run longer than the default. \n"
"                    If the value is fixed then PHP will not be allowed to run longer than the default."
msgstr ""
"Se il valore è dinamico, significa che PHP può funzionare più a lungo di quello predefinito. \n"
"                    Se il valore è fisso, allora il PHP non potrà funzionare più a lungo di quello predefinito."

#: classes/class.server.php:470
msgid "Can't detect"
msgstr "Non rilevato"

#: views/tools/diagnostics/inc.data.php:31
msgid "Clicking on the 'Remove Installation Files' button will attempt to remove the installer files used by Duplicator.  These files should not be left on production systems for security reasons. Below are the files that should be removed."
msgstr "Facendo clic sul pulsante \"Rimuovi file di installazione\" si tenterà di rimuovere i file di installazione utilizzati da Duplicator. Questi file non devono essere lasciati sui sistemi di produzione per motivi di sicurezza. Di seguito sono riportati i file che dovrebbero essere rimossi."

#: classes/class.server.php:327
msgid "(directory)"
msgstr "(directory)"

#: classes/class.logging.php:161
msgid "No Log"
msgstr "Nessun log"

#: classes/class.server.php:491 classes/utilities/class.u.php:70
msgid "Unknown"
msgstr "Sconosciuta"

#: classes/utilities/class.u.php:67
msgid "64-bit"
msgstr "64 bit"

#: classes/utilities/class.u.php:64
msgid "32-bit"
msgstr "32-bit"

#: src/Views/AdminNotices.php:307
msgid "Activate %s"
msgstr "Attiva %s"

#: src/Views/AdminNotices.php:226
msgid "Reserved Duplicator installation files have been detected in the root directory.  Please delete these installation files to avoid security issues. <br/> Go to: Duplicator > Tools > General > Information > Utils and click the \"Remove Installation Files\" button"
msgstr "Sono stati rilevati dei file di installazione di Duplicator riservati nella directory principale. Elimina questi file di installazione per evitare problemi di sicurezza. <br/> Vai a: Duplicator > Strumenti > Generale > Informazioni > Utilità e fai clic sul pulsante \"Rimuovi i file di installazione\"."

#: src/Views/AdminNotices.php:208
msgid "This message will be removed after all installer files are removed.  Installer files must be removed to maintain a secure site.  Click the link above or button below to remove all installer files and complete the migration."
msgstr "Questo messaggio verrà rimosso dopo che tutti i file dell'installer saranno stati rimossi. I file dell'installer devono essere rimossi per mantenere un sito sicuro.  Fare clic sul collegamento qui sopra o sul pulsante qui sotto per rimuovere tutti i file dell'installer e completare la migrazione."

#: classes/package/duparchive/class.pack.archive.duparchive.php:232
msgid "Critical failure present in validation"
msgstr "Errore critico presente nella validazione"

#: classes/package/duparchive/class.pack.archive.duparchive.php:158
msgid "Problems adding items to archive."
msgstr "Problemi nell'aggiungere elementi all'archivio."

#: classes/package/duparchive/class.pack.archive.duparchive.php:157
msgid "Problem adding items to archive."
msgstr "Problema nell'aggiungere elementi all'archivio."

#: classes/package/duparchive/class.pack.archive.duparchive.php:71
msgid "ERROR: Can't find Scanfile %s. Please ensure there no non-English characters in the Backup or schedule name."
msgstr "ERRORE: Impossibile trovare Scanfile %s. Verifica che non ci siano caratteri non inglesi nel nome del backup o del programma."

#: classes/package/duparchive/class.pack.archive.duparchive.php:61
msgid "Click on \"Resolve This\" button to fix the JSON settings."
msgstr "Fai clic sul pulsante \"Risolvi\" per correggere le impostazioni JSON."

#: classes/package/duparchive/class.pack.archive.duparchive.php:36
msgid "Build Failure"
msgstr "Compilazione fallita"

#: classes/package/duparchive/class.pack.archive.duparchive.php:35
msgid "Backup build appears stuck so marking Backup as failed. Is the Max Worker Time set too high?."
msgstr "La creazione del backup sembra bloccata e quindi il backup viene contrassegnato come non riuscito. Il tempo massimo del worker è impostato su un valore troppo alto?"

#: classes/package/class.pack.database.php:757
msgid "Please contact your DataBase administrator to fix the error."
msgstr "Contatta l'amministratore del database per correggere l'errore."

#: classes/package/class.pack.php:1039
msgid "ARCHIVE CONSISTENCY TEST: Pass"
msgstr "Test d consistenza dell'archivio: Superato"

#: classes/package/class.pack.installer.php:844
#: classes/package/class.pack.php:1029
msgid "ERROR: Archive checksum is bad."
msgstr "ERRORE: il checksum dell'archivio non è valido."

#: classes/package/class.pack.installer.php:841
#: classes/package/class.pack.php:1024
msgid "ERROR: Archive doesn't pass consistency check."
msgstr "ERRORE: l'archivio non ha superato il controllo di coerenza."

#: classes/package/class.pack.installer.php:838
#: classes/package/class.pack.php:1020
msgid "ERROR: Archive is not valid zip archive."
msgstr "ERRORE: l'archivio non è un archivio zip valido."

#: classes/package/class.pack.installer.php:834
#: classes/package/class.pack.php:1015
msgid "ERROR: Cannot open created archive. Error code = %1$s"
msgstr "ERRORE: impossibile aprire l'archivio creato. Codice di errore = %1$s"

#: classes/package/class.pack.php:967
msgid "ACTUAL FILE/DIRECTORY COUNT: %1$s"
msgstr "CONTEGGIO FILE/DIRECTORY EFFETTIVO: %1$s"

#: classes/package/class.pack.php:966
msgid "EXPECTED FILE/DIRECTORY COUNT: %1$s"
msgstr "CONTEGGIO FILE/DIRECTORY PREVISTO: %1$s"

#: classes/package/class.pack.php:945
msgid "Can't find Scanfile %s. Please ensure there no non-English characters in the Backup or schedule name."
msgstr "Impossibile trovare Scanfile %s. Verifica che non ci siano caratteri non inglesi nel nome del backup o del programma."

#: classes/package/class.pack.php:401
msgid "MySQL Server Port: <b>%1$s</b> isn't a valid port"
msgstr "Porta del Serve MySQL: <b>%1$s</b> non è una porta valida"

#: classes/package/class.pack.php:391
msgid "MySQL Server Host: <b>%1$s</b> isn't a valid host"
msgstr "MySQL Server Host: <b>%1$s</b> non è un host valido"

#: classes/package/class.pack.php:382
msgid "Files: <b>%1$s</b> isn't a valid file name"
msgstr "File: <b>%1$s</b> non è un nome file valido"

#: classes/package/class.pack.php:373
msgid "File extension: <b>%1$s</b> isn't a valid extension"
msgstr "Estensione del file: <b>%1$s</b> non è un'estensione valida"

#: classes/package/class.pack.php:364
msgid "Directories: <b>%1$s</b> isn't a valid path"
msgstr "Directory: <b>%1$s</b> non è un percorso valido"

#: classes/package/class.pack.php:355
msgid "Backup name can't be empty"
msgstr "Il nome del backup non può essere vuoto"

#: classes/package/class.pack.installer.php:153
msgid "Error writing installer contents"
msgstr "Errore nello scrivere i contenuti dell'installer"

#: classes/package/class.pack.installer.php:140
msgid "Error reading DupArchive expander"
msgstr "Errore di lettura di DupArchive expander"

#: deactivation.php:314
msgid "Please tell us the reason so we can improve it."
msgstr "Facci sapere il motivo in modo che possiamo migliorarlo."

#: deactivation.php:164
msgid "Your response is sent anonymously."
msgstr "La tua risposta viene inviata in forma anonima."

#: deactivation.php:160
msgid "Send & Deactivate"
msgstr "Invia e disattiva"

#: deactivation.php:158 deactivation.php:381
msgid "Skip & Deactivate"
msgstr "Salta e disattiva"

#: deactivation.php:150
msgid "If you have a moment, please let us know why you are deactivating"
msgstr "Se hai un momento facci sapere perché stai disattivando"

#: deactivation.php:148
msgid "Quick Feedback"
msgstr "Feedback rapido"

#: deactivation.php:106
msgid "Pro version"
msgstr "Versione Pro"

#: deactivation.php:105
msgid "I'm switching over to the %s"
msgstr "Sto passando a %s"

#: deactivation.php:98
msgid "It's a temporary deactivation, I use the plugin all the time."
msgstr "È una disattivazione temporanea, continuerò ad utilizzare il plugin."

#: deactivation.php:94
msgid "What does it need to do?"
msgstr "Cosa deve fare?"

#: deactivation.php:92
msgid "It does not do what I need."
msgstr "Non fa quello che mi serve."

#: deactivation.php:88
msgid "What's the plugin name?"
msgstr "Qual è il nome del plugin?"

#: deactivation.php:86
msgid "I found a different plugin that I like better."
msgstr "Ho trovato un altro plugin che mi piace di più."

#: deactivation.php:81
msgid "Please tell us what is not clear so that we can improve it."
msgstr "Facci sapere cosa non è chiaro in modo che possiamo migliorarlo."

#: deactivation.php:79
msgid "It's too confusing to understand."
msgstr "È troppo complicato da capire."

#: deactivation.php:74
msgid "Kindly share what didn't work so we can fix it in future updates..."
msgstr "Si prega di condividere ciò che non ha funzionato in modo che possiamo risolverlo negli aggiornamenti futuri ..."

#: deactivation.php:72
msgid "It's not working on my server."
msgstr "Non funziona sul mio server."

#: deactivation.php:68
msgid "Contact Support"
msgstr "Contatta il supporto"

#: deactivation.php:67
msgid "Need help? We are ready to answer your questions."
msgstr "Ho bisogno di aiuto? Siamo pronti a rispondere alle tue domande."

#: ctrls/ctrl.package.php:304
msgid "Active Backup object error"
msgstr "Errore dell'oggetto Active Backup"

#: ctrls/ctrl.package.php:285
msgid "An unauthorized security request was made to this page. Please try again!"
msgstr "A questa pagina è stata fatta una richiesta di sicurezza non autorizzata. Riprova!"

#: ctrls/ctrl.package.php:192
msgid "Error building DupArchive Backup"
msgstr "Errore nella creazione del backup DupArchive"

#: views/tools/diagnostics/main.php:44
msgid "Logs"
msgstr "Log"

#: views/tools/diagnostics/main.php:43
msgid "Information"
msgstr "Informazione"

#: classes/class.server.php:653
msgid "Error Log File"
msgstr "File dei log degli errori"

#: views/packages/main/packages.php:120 views/tools/controller.php:25
msgid "Templates"
msgstr "Template"

#: src/Core/MigrationMng.php:245
msgid "NOTICE: Safe mode (Advanced) was enabled during install, be sure to re-enable all your plugins."
msgstr "AVVISO: La modalità sicura (avanzata) è stata abilitata durante l'installazione, assicurarsi di riattivare tutti i plugin."

#: src/Core/MigrationMng.php:242
msgid "NOTICE: Safe mode (Basic) was enabled during install, be sure to re-enable all your plugins."
msgstr "AVVISO: La modalità sicura (di base) è stata abilitata durante l'installazione, assicurarsi di riattivare tutti i plugin."

#: views/settings/packages.php:111
msgid "PHP Code"
msgstr "Codice PHP"

#: views/settings/packages.php:195
msgid "<i class=\"fa fa-exclamation-triangle fa-sm\"></i> The custom path provided is not recognized as a valid mysqldump file:<br/>"
msgstr "<i class=\"fa fa-exclamation-triangle\"></i> Il percorso personalizzato fornito non è riconosciuto come un file mysqldump valido:<br/>"

#: views/settings/packages.php:179
msgid "mysqldump path:"
msgstr "percorso mysqldump:"

#: views/settings/packages.php:152
msgid "Successfully Found:"
msgstr "Trovato:"

#: views/settings/packages.php:431
msgid "To use WordPress timezone formats consider an upgrade to Duplicator Pro."
msgstr "Per utilizzare i formati di fuso orario di WordPress, si consiglia un aggiornamento a Duplicator Pro."

#: views/settings/license.php:38
msgid "License Key"
msgstr "Chiave della licenza"

#: views/settings/license.php:32
msgid "Pro Features"
msgstr "Funzionalità Pro"

#: views/settings/license.php:27
msgid "Basic Features"
msgstr "Funzionalità di base"

#: views/settings/license.php:15
msgid "%1$sManage Licenses%2$s"
msgstr "%1$sGestisci licenze%2$s"

#: views/settings/license.php:7
msgid "Activation"
msgstr "Attivazione"

#: views/settings/controller.php:54
msgid "License"
msgstr "Licenza"

#: views/packages/main/s3.build.php:494
msgid "PHP Max Execution Mode"
msgstr "Modalità di Esecuzione Massima PHP"

#: views/packages/main/s3.build.php:488 views/settings/packages.php:210
msgid "Mode"
msgstr "Modalità"

#: views/packages/main/s3.build.php:484
msgid "This value is represented in seconds. A value of 0 means no timeout limit is set for PHP."
msgstr "Questo valore è espresso in secondi. Un valore 0 significa che non è impostato alcun limite di timeout per PHP."

#: views/packages/main/packages.php:340 views/packages/main/s3.build.php:476
msgid "Time"
msgstr "Tempo"

#: views/packages/main/s3.build.php:474
msgid "PHP Max Execution"
msgstr "Massima Esecuzione PHP"

#: views/packages/main/s3.build.php:467
msgid "RUNTIME DETAILS"
msgstr "DETTAGLI TEMPO DI ESECUZIONE"

#: views/packages/main/s3.build.php:463
msgid "Diagnose Server Setup"
msgstr "Diagnosi della configurazione del server"

#: views/packages/main/s3.build.php:439
msgid "Start Two-Part Install Process"
msgstr "Inizia il processo di installazione in due parti"

#: views/packages/main/s3.build.php:437
msgid "Yes. I have read the above overview and would like to continue!"
msgstr "Sì. Ho letto le informazioni riportate e voglio continuare!"

#: views/packages/main/s3.build.php:419
msgid "On Step 1 the \"Backup Only the Database\" checkbox will be auto checked."
msgstr "Al passo 1 il checkbox \"Esegui il backup solo del database\" verrà selezionato automaticamente."

#: views/packages/main/s3.build.php:418
msgid "Click the button below to go back to Step 1."
msgstr "Fai clic sul pulsante sottostante per tornare al passo 1."

#: views/packages/main/s3.build.php:335 views/packages/main/s3.build.php:416
msgid "Please follow these steps:"
msgstr "Segui questi passi:"

#: views/packages/main/s3.build.php:239
msgid "How to install this Backup?"
msgstr "Come installare questo backup?"

#: views/packages/main/s3.build.php:191
msgid "Click to download both files"
msgstr "Fai clic qui per scaricare entrambi i file"

#: views/packages/main/s3.build.php:186
msgid "Click to download Backup file"
msgstr "Fai clic per scaricare il file di backup"

#: views/packages/main/s3.build.php:183
msgid "Click to download installer file"
msgstr "Fai clic qui per scaricare l'installer"

#: views/packages/main/s2.scan3.php:697
msgid "Migrate large, multi-gig sites with"
msgstr "Migra siti di grandi dimensioni con"

#: views/packages/main/s2.scan3.php:392
msgid "No recursive sym-links found."
msgstr "Non sono stati trovati link simbolici ricorsivi."

#: views/packages/main/s2.scan3.php:385
msgid "Recursive Links:"
msgstr "Link ricorsivi:"

#: views/packages/main/s2.scan3.php:381
msgid "No unreadable items found."
msgstr "Nessun elemento illeggibile trovato."

#: views/packages/main/s2.scan3.php:374
msgid "Unreadable Items:"
msgstr "Elementi illeggibili:"

#: views/packages/main/s2.scan3.php:363
msgid "Read Checks"
msgstr "Controlli lettura"

#: views/packages/main/s2.scan3.php:272
msgid "*Checking a directory will exclude all items in that path recursively."
msgstr "*Selezionando una directory verranno esclusi ricorsivamente tutti gli elementi in quel percorso."

#: views/packages/main/s2.scan3.php:266
msgid "No add on sites found."
msgstr "Nessun sito aggiuntivo trovato."

#: views/packages/main/s2.scan3.php:232
msgid "Addon Sites"
msgstr "Siti aggiuntivi"

#: views/packages/main/s2.scan3.php:211 views/packages/main/s2.scan3.php:346
msgid "*Checking a directory will exclude all items recursively from that path down.  Please use caution when filtering directories."
msgstr "*Selezionando una directory verranno esclusi ricorsivamente tutti gli elementi da quel percorso in poi. Fai attenzione quando filtri le directory."

#: views/packages/main/s2.scan3.php:57 views/packages/main/s2.scan3.php:427
#: views/packages/main/s2.scan3.php:550
msgid "uncompressed"
msgstr "senza compressione"

#: views/packages/main/s2.scan2.php:144
msgid "The core WordPress file below will NOT be included in the Backup. This file is required for WordPress to function!"
msgstr "Il file del core di WordPress qui sotto NON sarà incluso nel backup. Questo file è necessario per il funzionamento di WordPress!"

#: views/packages/main/s2.scan2.php:133
msgid "The core WordPress paths below will NOT be included in the Backup. These paths are required for WordPress to function!"
msgstr "I percorsi del core di WordPress riportati di seguito NON saranno inclusi nel backup. Questi percorsi sono necessari per il funzionamento di WordPress!"

#: views/packages/main/s1.setup2.php:291
msgid "<i class='core-table-info'> Use caution when excluding tables! It is highly recommended to not exclude WordPress core tables*, unless you know the impact.</i>"
msgstr "<i class='core-table-info'> Escludi le tabelle con cautela! È altamente sconsigliato escludere le tabelle del core di WordPress se non ne comprendi le conseguenze.</i>"

#: views/packages/main/s1.setup2.php:290
msgid "Excluding certain tables can cause your site or plugins to not work correctly after install!<br/>"
msgstr "Escludere determinate tabelle può comportare il malfunzionamento del sito o dei plugin dopo l'installazione!<br/>"

#: views/packages/main/s1.setup2.php:289
msgid "Checked tables will be <u>excluded</u> from the database script. "
msgstr "Le tabelle selezionate verranno <u>escluse</u> dallo script del database. "

#: views/packages/main/s2.scan2.php:139
msgid "directories"
msgstr "directory"

#: views/packages/main/s1.setup1.php:186
msgid "If the root WordPress path is not writable by PHP on some systems this can cause issues."
msgstr "Se il percorso root di WordPress non è scrivibile via PHP in alcuni sistemi si possono verificare problemi."

#: views/packages/main/packages.php:421 views/packages/main/s3.build.php:292
msgid "Help review the plugin!"
msgstr "Aiuta a migliorare il plugin!"

#: views/packages/main/packages.php:417
msgid "Need help with the plugin?"
msgstr "Hai bisogno di aiuto per quanto riguarda il plugin?"

#: views/packages/main/packages.php:415
msgid "Other Resources:"
msgstr "Altre risorse:"

#: views/packages/main/packages.php:411
msgid "Frequently Asked Questions!"
msgstr "Domande frequenti!"

#: views/packages/main/packages.php:407
msgid "How do I install a Backup?"
msgstr "Come si installa un backup?"

#: views/packages/main/packages.php:403
msgid "How do I create a Backup"
msgstr "Come posso creare un backup"

#: views/packages/main/packages.php:400
msgid "Common Questions:"
msgstr "Domande comuni:"

#: views/packages/main/packages.php:387
msgid "Duplicator Help"
msgstr "Aiuto di Duplicator"

#: views/packages/main/packages.php:163 views/packages/main/packages.php:216
msgid "New to Duplicator?"
msgstr "Appena installato Duplicator?"

#: src/Controllers/StorageController.php:66
#: src/Controllers/StorageController.php:67
msgid "OneDrive"
msgstr "OneDrive"

#: src/Controllers/StorageController.php:71
#: src/Controllers/StorageController.php:72
msgid "DropBox"
msgstr "DropBox"

#: views/packages/main/s1.setup2.php:449
msgid "cPanel"
msgstr "cPanel"

#: template/admin_pages/welcome/features.php:80
msgid "Large Site Support"
msgstr "Supporto a siti di grandi dimensioni"

#: src/Controllers/AboutUsController.php:171 src/Utils/Upsell.php:69
msgid "Cloud Storage"
msgstr "Archiviazione cloud"

#: src/Views/AdminNotices.php:263
msgid "Invalid token permissions to perform this request."
msgstr "Token di autorizzazione non validi per eseguire questa richiesta."

#: src/Views/AdminNotices.php:260
msgid "Redirecting Please Wait..."
msgstr "Reindirizzamento in corso, attendi..."

#: src/Views/AdminNotices.php:207
msgid "Final step(s):"
msgstr "Passo(i) finale(i):"

#: src/Views/AdminNotices.php:200
msgid "re-activate the plugins"
msgstr "riattiva i plugin"

#: src/Views/AdminNotices.php:199
msgid "During the install safe mode was enabled deactivating all plugins.<br/> Please be sure to "
msgstr "Durante l'installazione è stata abilitata la modalità sicura disattivando tutti i plugin.<br/> Assicurati di "

#: src/Views/AdminNotices.php:198
msgid "Safe Mode:"
msgstr "Modalità sicura:"

#: views/tools/diagnostics/inc.validator.php:80
msgid "Note: Symlinks are not discoverable on Windows OS with PHP"
msgstr "Nota: i link simbolici non sono rilevabili sui Sistemi Operativi Windows con PHP"

#: views/tools/diagnostics/main.php:45
msgid "Support"
msgstr "Supporto"

#: views/tools/diagnostics/inc.data.php:22
msgid "Remove Installation Files"
msgstr "Rimuovi i file di installazione"

#: views/parts/migration-clean-installation-files.php:60
msgid "Security Notes"
msgstr "Note sulla sicurezza"

#: views/settings/packages.php:190
msgid "/usr/bin/mypath/mysqldump"
msgstr "/usr/bin/mypath/mysqldump"

#: views/packages/main/s3.build.php:470
msgid "Allowed Runtime:"
msgstr "Runtime permessi:"

#: views/packages/main/s3.build.php:301
msgid "Host Build Interrupt"
msgstr "Compilazione interrotta dall'host"

#: views/packages/main/s2.scan3.php:985 views/packages/main/s2.scan3.php:992
msgid "Error applying filters.  Please go back to Step 1 to add filter manually!"
msgstr "Errore nell'applicazione dei filtri. Torna indietro al passo 1 per aggiungere i filtri manualmente!"

#: views/packages/main/s2.scan3.php:942
msgid "Initializing Please Wait..."
msgstr "Inizializzazione in corso, attendi..."

#: views/packages/main/s2.scan3.php:935
msgid "Manual copy of selected text required on this browser."
msgstr "Con questo browser &egrave; necessario copiare manualmente il testo selezionato."

#: views/packages/main/s2.scan3.php:933
msgid "Copied to Clipboard!"
msgstr "Copiato negli appunti!"

#: views/packages/main/s2.scan3.php:895
msgid "No files have been selected!"
msgstr "Nessun archivio è stato selezionato!"

#: views/packages/main/s2.scan3.php:891
msgid "No directories have been selected!"
msgstr "Non è stata selezionata alcuna directory!"

#: views/packages/main/s2.scan3.php:864
msgid "Directory applied filter set."
msgstr "Set di filtri applicati alla directory."

#: views/packages/main/s2.scan3.php:847
msgid "Copy the paths above and apply them as needed on Step 1 &gt; Archive &gt; Files section."
msgstr "Copia i percorsi qui sopra e inseriscili dove richiesto nella sezione al passo 1 &gt; Archivio &gt; File."

#: views/packages/main/s2.scan3.php:833 views/packages/main/s2.scan3.php:842
msgid "Click to Copy"
msgstr "Fai clic per copiare"

#: views/packages/main/s2.scan3.php:822
msgid "Auto filters are applied to prevent archiving other backup sets."
msgstr "I filtri automatici vengono applicati per impedire l'archiviazione di altri set di backup."

#: views/packages/main/s2.scan3.php:819
msgid "[view json result report]"
msgstr "[visualizza il report dei risultati in JSON]"

#: views/packages/main/s2.scan3.php:817
msgid "Path filters will be skipped during the archive process when enabled."
msgstr "I filtri dei percorsi verranno saltati durante il processo di archiviazione anche se abilitati."

#: views/packages/main/s2.scan3.php:794
msgid "No custom file filters set."
msgstr "Nessun filtro personalizzato impostato per i file."

#: views/packages/main/s2.scan3.php:772
msgid "No custom directory filters set."
msgstr "Nessun filtro personalizzato per le directory impostato"

#: views/packages/main/s2.scan3.php:719
msgid "Copy Quick Filter Paths"
msgstr "Copia percorsi dei filtri rapidi"

#: views/packages/main/s2.scan3.php:712
msgid "Scan Details"
msgstr "Dettagli della scansione"

#: views/packages/main/s2.scan3.php:467
msgid "3. %1$s if this host supports the option."
msgstr "3. %1$s se questo host supporta l'opzione."

#: views/packages/main/s2.scan3.php:466
msgid "Enable mysqldump"
msgstr "Abilita mysqldump"

#: views/packages/main/s2.scan3.php:464
msgid "2. Remove post revisions and stale data from tables.  Tables such as logs, statistical or other non-critical data should be cleared."
msgstr "2. Rimuovi le revisioni degli articoli e i dati obsoleti dalle tabelle. Tabelle come logs, dati statistici o altri dati non critici dovrebbero essere svuotate."

#: views/packages/main/s2.scan3.php:458 views/packages/main/s2.scan3.php:571
#: views/packages/main/s2.scan3.php:659
msgid "RECOMMENDATIONS:"
msgstr "RACCOMANDAZIONI:"

#: views/packages/main/s2.scan3.php:449
msgid "TABLE DETAILS:"
msgstr "DETTAGLI TABELLA:"

#: views/packages/main/s2.scan3.php:438
msgid "TOTAL SIZE"
msgstr "DIMENSIONE TOTALE"

#: views/packages/main/s2.scan3.php:340
msgid "No file/directory name warnings found."
msgstr "Nessun avviso per nomi di file/directory riscontrato."

#: views/packages/main/s2.scan3.php:294
msgid "  Only consider using this filter if the Backup build is failing. Select files that are not important to your site or you can migrate manually."
msgstr "  Prendi in considerazione l'uso di questo filtro solo se la creazione del backup non funziona. Seleziona i file che non sono importanti per il tuo sito o che puoi migrare manualmente."

#: views/packages/main/s2.scan3.php:293
msgid "Unicode and special characters such as \"*?><:/\\|\", can be problematic on some hosts."
msgstr "I caratteri speciali ed Unicode come per esempio \"*?><:/\\|\", possono essere problematici su alcuni host."

#: views/packages/main/s2.scan3.php:216 views/packages/main/s2.scan3.php:351
msgid "Copy Paths to Clipboard"
msgstr "Copia i percorsi negli appunti"

#: views/packages/main/s2.scan3.php:214 views/packages/main/s2.scan3.php:275
#: views/packages/main/s2.scan3.php:349
msgid "Add Filters &amp; Rescan"
msgstr "Aggiungi i filtri &amp; Ripeti l'analisi"

#: views/packages/main/s2.scan3.php:195
msgid "No large files found during this scan."
msgstr "La scansione non ha trovato file grandi."

#: views/packages/main/s2.scan3.php:175 views/packages/main/s2.scan3.php:320
msgid "Core WordPress directories should not be filtered. Use caution when excluding files."
msgstr "Le cartelle core di WordPress non dovrebbero essere filtrate. Fai attenzione ai file che filtri."

#: views/packages/main/s2.scan3.php:165 views/packages/main/s2.scan3.php:305
msgid "Show All"
msgstr "Mostra tutto"

#: views/packages/main/s2.scan3.php:164 views/packages/main/s2.scan3.php:304
msgid "Hide All"
msgstr "Nascondi tutto"

#: views/packages/main/s2.scan3.php:160 views/packages/main/s2.scan3.php:252
#: views/packages/main/s2.scan3.php:301
msgid "Quick Filters"
msgstr "Filtri rapidi"

#: views/packages/main/s2.scan3.php:122
msgid "Apply the \"Quick Filters\" below or click the back button to apply on previous page."
msgstr "Applica i \"Filtri rapidi\" qui sotto o fai clic sul pulsante indietro per applicarlo sulla pagina precedente."

#: views/packages/main/s2.scan3.php:119
msgid "Timeout Options"
msgstr "Opzioni Timeout"

#: views/packages/main/s2.scan3.php:103
msgid "more details..."
msgstr "altri dettagli..."

#: views/packages/main/s2.scan3.php:102
msgid "Compressing larger sites on <i>some budget hosts</i> may cause timeouts.  "
msgstr "La compressione di grandi siti può causare timeout su <i>alcuni host economici</i>. "

#: views/packages/main/s2.scan3.php:94
msgid "Size Checks"
msgstr "Controlli sulla dimensione"

#: views/packages/main/s2.scan3.php:32
msgid "Show Scan Details"
msgstr "Mostra i dettagli della scansione"

#: views/packages/main/s2.scan2.php:68 views/packages/main/s3.build.php:483
msgid "PHP Max Execution Time"
msgstr "Tempo massimo di esecuzione PHP"

#: views/packages/main/s2.scan2.php:62
msgid "PHP Open Base Dir"
msgstr "PHP Open Base Dir"

#: views/packages/main/s2.scan2.php:57
msgid "The minimum PHP version supported by Duplicator is 5.2.9. It is highly recommended to use PHP 5.3+ for improved stability.  For international language support please use PHP 7.0+."
msgstr "La versione minima di PHP supportata da Duplicator &egrave; la 5.2.9. &egrave; altamente consigliato usare PHP 5.3+ per una migliore stabilit&agrave;. Per il supporto ai linguaggi internazionali si prega di usare PHP 7.0+."

#: views/packages/main/s2.scan2.php:12
msgid "Show Diagnostics"
msgstr "Mostra la diagnostica"

#: views/packages/main/s2.scan1.php:454 views/packages/main/s2.scan3.php:620
msgid "Good"
msgstr "Buono"

#: views/packages/main/s1.setup2.php:90
msgid "Toggle a default name"
msgstr "Attiva / disattiva un nome predefinito"

#: views/packages/main/s1.setup2.php:87
msgid "Add Notes"
msgstr "Aggiungi note"

#: views/packages/main/packages.php:258 views/packages/main/s1.setup2.php:201
#: views/packages/main/s2.scan3.php:44 views/packages/main/s2.scan3.php:65
msgid "Database Only"
msgstr "Solo database"

#: views/packages/details/detail.php:374
msgid "Backup Database Only Enabled"
msgstr "Backup del solo database abilitato"

#: views/packages/details/detail.php:373
msgid "Database Mode"
msgstr "Modalit&agrave; Database"

#: src/Views/AdminNotices.php:244 views/parts/migration-almost-complete.php:33
msgid "Take me there now!"
msgstr "Portami lì ora!"

#: src/Views/AdminNotices.php:215 views/parts/migration-message.php:52
msgid "Remove Installation Files Now!"
msgstr "Rimuovi i file di installazione ora!"

#: src/Views/AdminNotices.php:219
msgid "Optionally, Review Duplicator at WordPress.org..."
msgstr "Opzionalmente, recensisci Duplicator su WordPress.org..."

#: src/Views/AdminNotices.php:225 src/Views/AdminNotices.php:317
#: views/parts/migration-almost-complete.php:17
msgid "Migration Almost Complete!"
msgstr "Migrazione quasi completata!"

#: src/Views/AdminNotices.php:206 views/parts/migration-message.php:17
msgid "This site has been successfully migrated!"
msgstr "Questo sito è stato migrato con successo!"

#: views/settings/packages.php:440
msgid "Save Backup Settings"
msgstr "Salva le impostazioni di backup"

#: deactivation.php:259 deactivation.php:261
msgid "Processing"
msgstr "In elaborazione"

#: views/settings/packages.php:400
msgid "Visuals"
msgstr "Formati"

#: views/settings/packages.php:15
msgid "Backup Settings Saved"
msgstr "Salvataggio delle impostazioni di backup"

#: template/admin_pages/settings/general/general.php:285
msgid "Save General Settings"
msgstr "Salva le impostazioni generali"

#: template/admin_pages/settings/general/general.php:26
msgid "General Settings Saved"
msgstr "Impostazioni generali salvate"

#: template/mocks/schedule/schedules.php:16
msgid "Schedules"
msgstr "Programmazioni"

#: src/Controllers/StorageController.php:61
#: src/Controllers/StorageController.php:62
msgid "Google Drive"
msgstr "Google Drive"

#: src/Controllers/StorageController.php:56
#: src/Controllers/StorageController.php:57
msgid "Amazon S3"
msgstr "Amazon S3"

#: classes/class.server.php:645 views/packages/main/s2.scan3.php:758
msgid "Disabled"
msgstr "Disabilitato"

#: classes/class.server.php:642
msgid "Suhosin Extension"
msgstr "Estensione Suhosin"

#: views/packages/main/s2.scan1.php:220
msgid "This can take several minutes."
msgstr "Potrebbero servire diversi minuti."

#: views/packages/main/s2.scan1.php:219
msgid "Keep this window open during the scan process."
msgstr "Mantieni questa finestra aperta durante il processo di scansione."

#: views/packages/main/s1.setup1.php:252
msgid "WordPress Root Path:"
msgstr "Percorso della root di WordPress:"

#: views/packages/main/s1.setup2.php:507
msgid "example: DatabaseUserName (value is optional)"
msgstr "esempio: DatabaseUserName (il valore &egrave; opzionale)"

#: views/packages/main/s1.setup2.php:494
msgid "example: DatabaseName (value is optional)"
msgstr "esempio: DatabaseName (il valore &egrave; opzionale)"

#: views/packages/main/s1.setup2.php:481
msgid "example: 3306 (value is optional)"
msgstr "esempio: 3306 (il valore &egrave; opzionale)"

#: views/packages/main/s1.setup2.php:468
msgid "example: localhost (value is optional)"
msgstr "esempio: localhost (il valore &egrave; facoltativo)"

#: views/packages/main/s1.setup1.php:214
msgid "MySQL version 5.0+ or better is required and the PHP MySQLi extension (note the trailing 'i') is also required.  Contact your server administrator and request that mysqli extension and MySQL Server 5.0+ be installed."
msgstr "È richiesta una versione di MySQL 5.0+ o superiore, ed è richiesta anche l'estensione MySQLi per PHP (fai attenzione alla 'i' finale). Contatta il tuo amministratore del server per richiedere che l'estensione mysqli e la versione di MySQL Server 5.0+ siano installate."

#. Description of the plugin
#: duplicator.php
msgid "Migrate and backup a copy of your WordPress files and database. Duplicate and move a site from one location to another quickly."
msgstr "Migra o crea una copia di backup dei file e del database del tuo sito WordPress. Duplica o sposta un sito da un posto ad un altro velocemente."

#: views/tools/diagnostics/inc.validator.php:17
msgid "This will run the scan validation check.  This may take several minutes.  Do you want to Continue?"
msgstr "Questo avvierà la scansione di controllo. Potrebbe richiedere diverso tempo. Vuoi continuare?"

#: views/tools/diagnostics/inc.validator.php:16
msgid "Run Validator"
msgstr "Esegui Validator"

#: views/tools/diagnostics/inc.data.php:83
msgid "Clear Build Cache?"
msgstr "Vuoi pulire la cache di creazione?"

#: views/settings/packages.php:422
msgid "By Day"
msgstr "Per giorno"

#: views/settings/packages.php:415
msgid "By Month"
msgstr "Per mese"

#: views/settings/packages.php:408
msgid "By Year"
msgstr "Per anno"

#: views/packages/main/packages.php:380
msgid "Removing Backups, Please Wait..."
msgstr "Rimozione dei backup, attendi..."

#: views/packages/main/packages.php:372
msgid "Selection Required"
msgstr "Selezione obbligatoria"

#: views/packages/main/packages.php:366
msgid "Bulk Action Required"
msgstr "Azione di gruppo obbligatoria"

#: views/packages/main/s2.scan3.php:424
msgid "The database size represents only the included tables. The process for gathering the size uses the query SHOW TABLE STATUS.  The overall size of the database file can impact the final size of the Backup."
msgstr "La dimensione del database rappresenta solo le tabelle incluse. Il processo di raccolta delle dimensioni usa la query SHOW TABLE STATUS. La dimensione complessiva del file del database può influire sulla dimensione finale del backup."

#: views/packages/main/s2.scan3.php:423
msgid "Database Size:"
msgstr "Dimensioni del database:"

#: views/packages/main/s2.scan2.php:197
msgid "This is not a multisite install so duplication will proceed without issue.  Duplicator does not officially support multisite. However, Duplicator Pro supports duplication of a full multisite network and also has the ability to install a multisite subsite as a standalone site."
msgstr "Questa non è una installazione multisito, quindi la duplicazione procederà senza problemi. Duplicator non supporta ufficialmente le installazioni multisito. Invece, Duplicator Pro supporta la duplicazione di una installazione multisito completa ed inoltre è in grado di installare un sottosito di un multisito come un sito indipendente."

#: views/packages/main/s2.scan2.php:196
msgid "Multisite: N/A"
msgstr "Multisito: non disponibile"

#: views/packages/main/s2.scan2.php:187
msgid "Multisite: Unsupported"
msgstr "Multisito: non supportato"

#: views/packages/main/s2.scan2.php:79
msgid "Get faster builds with Duplicator Pro with access to shell_exec zip."
msgstr "Crea pacchetti più velocemente con Duplicator Pro con l'accesso al comando zip tramite la funzionalità shell_exec."

#: views/packages/main/s1.setup2.php:572
msgid "Reset Backup Settings?"
msgstr "Reimpostare le impostazioni di backup?"

#: classes/ui/class.ui.dialog.php:90 deactivation.php:156
msgid "Cancel"
msgstr "Annulla"

#: classes/ui/class.ui.dialog.php:89
msgid "OK"
msgstr "OK"

#: classes/ui/class.ui.dialog.php:86
msgid "Processing please wait..."
msgstr "In elaborazione, attendi..."

#: views/tools/diagnostics/logging.php:216
msgid "Top 20"
msgstr "Top 20"

#: views/settings/packages.php:316
msgid "enable only for large archives"
msgstr "abilita solo per grandi archivi"

#: views/settings/packages.php:238
msgid "A higher limit size will speed up the database build time, however it will use more memory. If your host has memory caps start off low."
msgstr "Una dimensione limite più elevata accelera la compilazione del pacchetto, ma utilizza più memoria. Se il tuo host ha un limite di memoria inizia con un limite basso."

#: views/settings/packages.php:237
msgid "PHP Query Limit Size"
msgstr "Dimensione limite per le query PHP"

#: views/settings/packages.php:177
msgid "Custom Path"
msgstr "Percorso personalizzato"

#: views/settings/packages.php:101
msgid "Mysqldump"
msgstr "Mysqldump"

#: views/settings/packages.php:357
msgid "recommended"
msgstr "raccomandato"

#: views/settings/packages.php:122
msgid "Please contact the host or server administrator to enable this feature."
msgstr "Devi contattare l'amministratore dell'host o del server per abilitare questa funzionalit&agrave;."

#: views/settings/packages.php:121
msgid "This server does not support the PHP shell_exec or exec function which is required for mysqldump to run. "
msgstr "Questo server non supporta la funzione PHP shell_exec che è necessaria per eseguire mysqldump."

#: views/settings/packages.php:430
msgid "The UTC date format shown in the 'Created' column on the Backups screen."
msgstr "Il formato della data UTC mostrato nella colonna \"Creato\" nella schermata dei backup."

#: views/settings/packages.php:404
msgid "Created Format"
msgstr "Formato data di creazione"

#: views/tools/diagnostics/inc.validator.php:129
msgid "Scanning Environment... This may take a few minutes."
msgstr "Analisi dell'Ambiente... Potrebbero essere necessari alcuni minuti."

#: template/admin_pages/settings/general/general.php:185
msgid "Enable debug options throughout user interface"
msgstr "Abilita le opzioni di debug attraverso l'interfaccia utente"

#: template/admin_pages/settings/general/general.php:182
msgid "Debugging"
msgstr "Debug"

#: template/admin_pages/settings/general/general.php:178
msgid "Debug"
msgstr "Debug"

#: views/tools/diagnostics/inc.validator.php:46
#: views/tools/diagnostics/inc.validator.php:160
msgid "Run Scan Integrity Validation"
msgstr "Avvia la Scansione per Validare l'Integrità dei Dati"

#: views/tools/diagnostics/inc.validator.php:28
msgid "Scan Validator"
msgstr "Validatore di scansione"

#: views/packages/main/s1.setup1.php:189
msgid "If Duplicator does not have enough permissions then you will need to manually create the paths above. &nbsp; "
msgstr "Se Duplicator non ha i giusti permessi allora dovrai creare manualmente il percorso qui sopra. &nbsp; "

#: views/settings/about-info.php:63
msgid "Rate Duplicator"
msgstr "Valuta Duplicator"

#: views/packages/main/s2.scan1.php:434
msgid "- Symbolic link recursion can cause timeouts. Ask your server admin if any are present in the scan path. If they are add the full path as a filter and try running the scan again."
msgstr "- La ricorsione dei link simbolici può causare timeout. Chiedi all'amministratore del server se sono presenti nel percorso di scansione. In caso affermativo, aggiungi il percorso completo come filtro e riprova a eseguire la scansione."

#: views/packages/main/s2.scan1.php:429
msgid "- On some budget hosts scanning over 30k files can lead to timeout/gateway issues. Consider scanning only your main WordPress site and avoid trying to backup other external directories."
msgstr "- Su alcuni hosting economici scansionare oltre 30mila file può portare a problemi di timeout. Valuta di eseguire soltanto la scansione del tuo sito WordPress principale ed evita di provare ad eseguire il backup di altre directory esterne."

#: views/packages/main/s2.scan1.php:428
msgid "Common Issues:"
msgstr "Problemi comuni:"

#: views/packages/main/s2.scan1.php:426
msgid "3. This message will go away once the correct filters are applied."
msgstr "3. Questo messaggio scomparirà una volta applicati i filtri corretti."

#: views/packages/main/s2.scan1.php:425
msgid "2. Continue to add/remove filters to isolate which path is causing issues."
msgstr "2. Continua a aggiungere/rimuovere i filtri per isolare il percorso che sta causando problemi."

#: views/packages/main/s2.scan1.php:424
msgid "1. Go back and create a root path directory filter to validate the site is scan-able."
msgstr "1. Torna indietro e crea un filtro per il percorso alla cartella principale e assicurati che il sito sia scansionabile."

#: views/packages/main/s2.scan1.php:423
msgid "Unable to perform a full scan, please try the following actions:"
msgstr "Non sono in grado di eseguire una scansione completa, prova con le seguenti azioni:"

#: views/packages/main/s1.setup2.php:390
msgid "optional"
msgstr "facoltativi"

#: views/packages/main/s1.setup2.php:390
msgid "All values in this section are"
msgstr "Tutti i valori in questa sezione sono"

#: src/Utils/ExtraPlugins/ExtraPluginsMng.php:123
msgid "OptinMonster"
msgstr "OptinMonster"

#: views/settings/packages.php:128 views/tools/diagnostics/logging.php:183
msgid "Duplicator recommends going with the high performance pro plan or better from our recommended list"
msgstr "Duplicator consiglia di passare ad un piano professionale ad alte prestazioni o, meglio ancora, sceglierne uno dalla nostra lista raccomandata"

#: views/settings/packages.php:127 views/tools/diagnostics/logging.php:182
msgid "Host Recommendation:"
msgstr "Raccomandazione hosting:"

#: views/packages/main/s2.scan3.php:470
msgid "4. For table name case sensitivity issues either rename the table with lower case characters or be prepared to work with the %1$s system variable setting."
msgstr "4. Per problemi relativi alla distinzione tra maiuscole e minuscole, rinominare la tabella con caratteri minuscoli o prepararsi a utilizzare l'impostazione %1$s della variabile di sistema."

#: views/packages/main/s2.scan3.php:469
msgid "lower_case_table_names"
msgstr "lower_case_table_names "

#: views/packages/main/s2.scan3.php:462
msgid "1. Run a %1$s on the table to improve the overall size and performance."
msgstr "1. Esegue un %1$s sulla tabella per diminuire la sua grandezza e migliorare le performance."

#: views/packages/main/s2.scan3.php:434 views/packages/main/s3.build.php:334
msgid "Overview"
msgstr "Panoramica"

#: views/packages/main/s1.setup1.php:244
msgid "None of the reserved files where found from a previous install.  This means you are clear to create a new Backup."
msgstr "Nessuno dei file riservati è stato trovato da un'installazione precedente. Questo significa che è possibile creare un nuovo backup."

#: views/packages/main/packages.php:316
msgid "Error Processing"
msgstr "Controllo errori"

#: views/packages/main/s1.setup2.php:346
msgid "This option is only available with mysqldump mode."
msgstr "Questa opzione è disponibile solamente in modalità mysqldump."

#: views/packages/main/s1.setup2.php:341
msgid "no_field_options"
msgstr "no_field_options"

#: views/packages/main/s1.setup2.php:337
msgid "no_key_options"
msgstr "no_key_options"

#: views/packages/main/s1.setup2.php:333
msgid "no_table_options"
msgstr "no_table_options "

#: views/packages/main/s1.setup2.php:329
msgid "mysql40"
msgstr "mysql40 "

#: views/packages/main/s1.setup2.php:308
msgid "Compatibility Mode:"
msgstr "Modalità compatibilità:"

#: views/packages/main/s1.setup2.php:306
msgid "Compatibility Mode"
msgstr "Modalità compatibilità"

#: views/packages/main/s1.setup2.php:235
msgid "Enable Table Filters:"
msgstr "Abilita la tabella dei filtri:"

#: views/packages/main/controller.php:63 views/packages/main/controller.php:67
#: views/packages/main/controller.php:71
msgid "Backups &raquo; New"
msgstr "Backup &raquo; Nuovo"

#: views/packages/main/controller.php:59
msgid "Backups &raquo; All"
msgstr "Backup &raquo; Tutto"

#: template/parts/DashboardWidget/recommended-section.php:45
msgid "Learn More"
msgstr "Maggiori informazioni"

#: views/packages/details/detail.php:515 views/packages/details/detail.php:519
#: views/packages/details/detail.php:523
msgid "- not set -"
msgstr "- non impostato -"

#: views/packages/details/detail.php:436 views/packages/main/s2.scan3.php:747
msgid "MySQL Compatibility Mode Enabled"
msgstr "Abilitata la modalità di compatibilità con MySQL"

#: views/packages/details/detail.php:392 views/packages/main/s2.scan3.php:776
msgid "Extensions"
msgstr "Estensioni"

#: views/packages/details/detail.php:386 views/packages/details/detail.php:396
#: views/packages/details/detail.php:405 views/packages/details/detail.php:453
msgid "- no filters -"
msgstr "- nessun filtro -"

#: template/parts/filters/package_components.php:134
#: views/packages/details/detail.php:378 views/packages/details/detail.php:443
#: views/packages/main/s1.setup2.php:226
msgid "Filters"
msgstr "Filtri"

#: views/settings/packages.php:261
msgid "ZipArchive"
msgstr "Archivio zip"

#: views/packages/details/detail.php:366
msgid "Build Mode"
msgstr "Modalità compilazione"

#: views/packages/details/detail.php:362
msgid "FILES"
msgstr "FILE"

#: views/packages/details/detail.php:336 views/packages/main/s1.setup2.php:166
msgid "Additional Storage:"
msgstr "Spazio di archiviazione aggiuntivo:"

#: views/packages/details/detail.php:162
msgid "in-complete"
msgstr "incompleto"

#: views/packages/details/detail.php:162
msgid "completed"
msgstr "completato"

#: views/packages/details/detail.php:161
msgid "Status"
msgstr "Stato"

#: views/packages/details/detail.php:158
msgid "error running"
msgstr "errore di esecuzione"

#: views/packages/details/detail.php:157
msgid "Runtime"
msgstr "Tempo di esecuzione"

#: classes/class.server.php:402 views/packages/details/detail.php:142
msgid "PHP"
msgstr "PHP"

#: views/packages/details/detail.php:146
msgid "Mysql"
msgstr "Mysql"

#: views/packages/details/detail.php:139 views/packages/details/detail.php:143
#: views/packages/details/detail.php:148 views/packages/details/detail.php:149
#: views/packages/details/detail.php:166
msgid "- unknown -"
msgstr "- non conosciuto -"

#: views/packages/details/detail.php:123
msgid "- no notes -"
msgstr "- nessuna nota -"

#: views/packages/details/detail.php:114
msgid "Full Name"
msgstr "Nome completo"

#: views/packages/details/detail.php:106
msgid "ID"
msgstr "ID"

#: views/packages/details/detail.php:80
msgid "Invalid Backup ID request.  Please try again!"
msgstr "Richiesta di ID Backup non valida. Riprova!"

#: views/packages/details/controller.php:73
msgid "Transfer"
msgstr "Trasferimento"

#: src/Utils/Upsell.php:81
msgid "Custom Search & Replace"
msgstr "Cerca e sostituisci personalizzato"

#: views/packages/main/s2.scan3.php:757
msgid "File Filters"
msgstr "File filtrati"

#: src/Utils/Upsell.php:51 src/Utils/Upsell.php:80
msgid "Email Alerts"
msgstr "Notifiche email"

#: template/admin_pages/about_us/lite_vs_pro/main.php:48
msgid "Feature"
msgstr "Caratteristiche"

#. Plugin Name of the plugin
#. Author of the plugin
#: duplicator.php src/Views/DashboardWidget.php:46
#: template/mail/email_summary.php:29
msgid "Duplicator"
msgstr "Duplicator"

#: views/tools/diagnostics/logging.php:209
msgid "Auto Refresh"
msgstr "Aggiornamento automatico"

#: views/tools/diagnostics/logging.php:206
msgid "Refresh"
msgstr "Aggiorna"

#: views/tools/diagnostics/logging.php:199
#: views/tools/diagnostics/logging.php:204
msgid "Options"
msgstr "Opzioni"

#: views/tools/diagnostics/logging.php:173
msgid "The process that PHP runs under does not have enough permissions to create files.  Please contact your hosting provider for more details"
msgstr "Il processo sotto il quale viene eseguito PHP non ha i permessi per creare file. Contatta il tuo hosting provider per ulteriori dettagli."

#: views/tools/diagnostics/logging.php:172
msgid "The snapshots directory does not have the correct permissions to write files.  Try setting the permissions to 755"
msgstr "La directory dove vengono creati gli snapshot non ha i permessi corretti per scrivere i files. Prova ad impostare i permessi a 755"

#: views/tools/diagnostics/logging.php:171
msgid "The web server does not support returning .log file extentions"
msgstr "Il web server non supporta i file con estensione .log"

#: views/tools/diagnostics/logging.php:170
msgid "Reasons for log file not showing"
msgstr "Ragioni per cui il file di log non viene visualizzato"

#: views/tools/diagnostics/logging.php:168
msgid "Log file not found or unreadable"
msgstr "File di log non trovato o illeggibile"

#: views/tools/diagnostics/inc.phpinfo.php:26
msgid "PHP Information"
msgstr "Informazioni PHP"

#: classes/class.server.php:439
msgid "Free Space"
msgstr "Spazio libero"

#: classes/class.server.php:456
msgid "Server Disk"
msgstr "Disco server"

#: classes/class.server.php:691
msgid "mysqldump Path"
msgstr "Percorso msyqldump"

#: classes/class.server.php:685
msgid "Max Allowed Packets"
msgstr "Numero massimo di pacchetti consentiti"

#: classes/class.server.php:679
msgid "Wait Timeout"
msgstr "Timeout di attesa"

#: classes/class.server.php:637
msgid "Shell Exec Zip"
msgstr "Shell Exec Zip"

#: classes/class.server.php:634 classes/class.server.php:639
msgid "Not Supported"
msgstr "Non supportato"

#: classes/class.server.php:634 classes/class.server.php:639
msgid "Is Supported"
msgstr "È supportato"

#: classes/class.server.php:604
msgid "Memory In Use"
msgstr "Memoria in uso"

#: classes/class.server.php:567 classes/class.server.php:598
msgid "Memory Limit"
msgstr "Limite memoria"

#: classes/class.server.php:628 views/packages/details/detail.php:380
#: views/packages/details/detail.php:444
msgid "Off"
msgstr "Off"

#: views/packages/details/detail.php:380 views/packages/details/detail.php:444
msgid "On"
msgstr "On"

#: classes/class.server.php:562 classes/class.server.php:674
#: views/packages/main/s1.setup2.php:512
msgid "Charset"
msgstr "Charset"

#: classes/class.server.php:557
msgid "Language"
msgstr "Lingua"

#: classes/class.server.php:524
msgid "Client IP"
msgstr "IP Client"

#: classes/class.server.php:519
msgid "Server IP"
msgstr "IP Server"

#: classes/class.server.php:514
msgid "Loaded PHP INI"
msgstr "Caricato PHP INI"

#: views/packages/main/s2.scan3.php:12
msgid "Root Path"
msgstr "Percorso root"

#: classes/class.server.php:504
msgid "Server Time"
msgstr "Tempo sul server"

#: classes/class.server.php:489
msgid "Timezone"
msgstr "Fuso orario"

#: classes/class.server.php:484
msgid "Operating System"
msgstr "Sistema operativo"

#: classes/class.server.php:479 classes/class.server.php:534
msgid "Duplicator Version"
msgstr "Versione Duplicator"

#: views/tools/diagnostics/inc.settings.php:13
msgid "Server Settings"
msgstr "Impostazioni server"

#: views/tools/diagnostics/inc.data.php:49
msgid "Removes all build data from:"
msgstr "Rimuove tutti i dati di creazione da:"

#: views/tools/diagnostics/inc.data.php:46
msgid "Clear Build Cache"
msgstr "Svuota la cache"

#: views/tools/diagnostics/inc.data.php:26
msgid "Removes all reserved installer files."
msgstr "Rimuove tutti i file riservati all'installazione."

#: views/tools/diagnostics/information.php:46
msgid "Build cache removed."
msgstr "Cache rimossa."

#: views/parts/migration-clean-installation-files.php:31
msgid "Found"
msgstr "Trovato"

#: views/settings/packages.php:159
msgid "Mysqldump was not found at its default location or the location provided.  Please enter a custom path to a valid location where mysqldump can run.  If the problem persist contact your host or server administrator.  "
msgstr "Mysqldump non è stato trovato nella posizione predefinita o nella posizione fornita.  Inserisci un percorso personalizzato in una posizione valida in cui può essere eseguito mysqldump.  Se il problema persiste, contatta il tuo host o l'amministratore del server.  "

#: views/settings/packages.php:227
msgid "Query Limit Size"
msgstr "Limite Query Size"

#: views/settings/packages.php:319
msgid "This will attempt to keep a network connection established for large archives."
msgstr "Questo tenterà di mantenere attiva la connessione di rete per archivi di grandi dimensioni."

#: views/settings/packages.php:315
msgid "Attempt Network Keep Alive"
msgstr "Tentativo di tenere attiva la rete"

#: views/settings/storage.php:110
msgid "Disable .htaccess file in storage directory"
msgstr "Disabilita il file .htaccess nella directory di archivio"

#: template/admin_pages/settings/general/general.php:130
msgid "Delete Entire Storage Directory"
msgstr "Elimina l'intera directory di archivio"

#: template/admin_pages/settings/general/general.php:126
msgid "Delete Plugin Settings"
msgstr "Elimina impostazioni plugin"

#: template/admin_pages/settings/general/general.php:122
msgid "Uninstall"
msgstr "Disinstalla"

#: template/admin_pages/settings/general/general.php:110
msgid "Plugin"
msgstr "Plugin"

#: classes/class.server.php:390 views/packages/details/detail.php:92
#: views/settings/controller.php:30 views/tools/controller.php:24
msgid "General"
msgstr "Generale"

#: views/packages/main/s3.build.php:391
msgid "Build Folder:"
msgstr "Cartella della compilazione:"

#: views/packages/main/s2.scan1.php:452 views/packages/main/s2.scan3.php:66
#: views/packages/main/s2.scan3.php:78 views/packages/main/s2.scan3.php:622
#: views/packages/main/s3.build.php:387
msgid "Notice"
msgstr "Attenzione"

#: views/packages/main/s3.build.php:164
msgid "Build Status"
msgstr "Stato della compilazione"

#: views/packages/main/s2.scan3.php:1130
msgid "Unable to report on database stats"
msgstr "Impossibile creare il report di stato del database"

#: views/packages/main/s2.scan3.php:1104
msgid "Unable to report on any tables"
msgstr "Impossibile eseguire il report su qualsiasi tabella"

#: views/packages/main/s2.scan1.php:279
msgid "Rescan"
msgstr "Ripeti l'analisi"

#: views/packages/main/s2.scan1.php:180 views/packages/main/s2.scan1.php:278
msgid "Back"
msgstr "Indietro"

#: views/packages/main/s2.scan1.php:170 views/packages/main/s2.scan1.php:234
#: views/packages/main/s3.build.php:520
msgid "Error Message:"
msgstr "Messaggio di errore:"

#: views/packages/main/s2.scan1.php:231 views/packages/main/s3.build.php:503
msgid "Server Status:"
msgstr "Stato del server:"

#: views/packages/main/s2.scan1.php:168 views/packages/main/s2.scan1.php:229
msgid "Please try again!"
msgstr "Riprova!"

#: views/packages/main/s2.scan1.php:228
msgid "Scan Error"
msgstr "Scansiona errore"

#: views/packages/main/s2.scan1.php:263
msgid "Please review the details for each section by clicking on the detail title."
msgstr "Controlla i dettagli di ogni sezione facendo clic sul titolo del dettaglio."

#: views/packages/main/s2.scan1.php:261
msgid "Scan checks are not required to pass, however they could cause issues on some systems."
msgstr "Non è obbligatorio che i controlli di scansione vengano passati, comunque possono causare problemi in qualche sistema."

#: views/packages/main/s2.scan3.php:741
msgid "Build Mode:"
msgstr "Modalità costruttore:"

#: views/packages/main/s2.scan3.php:739
msgid "Host:"
msgstr "Host:"

#: views/packages/main/s2.scan3.php:738
msgid "Name:"
msgstr "Nome:"

#: views/packages/main/s2.scan3.php:461
msgid "repair and optimization"
msgstr "riparazione e ottimizzazione"

#: views/packages/main/s2.scan3.php:441
msgid "Records"
msgstr "Registro"

#: views/packages/details/detail.php:449 views/packages/main/s2.scan3.php:440
msgid "Tables"
msgstr "Tabelle"

#: views/packages/main/s2.scan3.php:782
msgid "No file extension filters have been set."
msgstr "Non è stato impostato un filtro per l'estensione dei file."

#: views/packages/main/s2.scan3.php:161
msgid "Large Files"
msgstr "File grandi"

#: views/packages/main/s2.scan3.php:288 views/packages/main/s2.scan3.php:302
msgid "Name Checks"
msgstr "Controllo nomi"

#: views/packages/main/s2.scan3.php:100
msgid "Directory Count"
msgstr "Conteggio directory"

#: views/packages/main/s2.scan3.php:99
msgid "File Count"
msgstr "Conteggio file"

#: classes/class.server.php:645
#: template/admin_pages/settings/general/general.php:192
#: template/parts/DashboardWidget/sections-section.php:51
#: views/packages/main/s2.scan3.php:47 views/packages/main/s2.scan3.php:417
#: views/packages/main/s2.scan3.php:758
msgid "Enabled"
msgstr "Abilitato"

#: views/packages/main/s2.scan2.php:127
msgid "Core Files"
msgstr "File core"

#: classes/class.server.php:552 views/packages/main/s2.scan2.php:122
msgid "WordPress Version"
msgstr "Versione WordPress"

#: classes/class.server.php:396 views/packages/details/detail.php:138
#: views/packages/main/s2.scan2.php:116
msgid "WordPress"
msgstr "WordPress"

#: classes/class.server.php:408
msgid "MySQL"
msgstr "MySQL"

#: views/packages/main/s2.scan2.php:72
msgid "Note: Timeouts can also be set at the web server layer, so if the PHP max timeout passes and you still see a build timeout messages, then your web server could be killing the process.   If you are on a budget host and limited on processing time, consider using the database or file filters to shrink the size of your overall Backup.   However use caution as excluding the wrong resources can cause your install to not work properly."
msgstr "Nota: I timeout possono essere impostati anche a livello di server web, quindi se il timeout massimo di PHP viene superato e continui a vedere messaggi di timeout di compilazione, è possibile che il tuo server web stia uccidendo il processo.   Se hai un host economico e il tempo di elaborazione è limitato, prendi in considerazione l'utilizzo dei filtri per i database o per i file per ridurre le dimensioni del backup complessivo.   Tuttavia, fai attenzione perché escludere le risorse sbagliate può causare il malfunzionamento dell'installazione."

#: classes/class.server.php:609
msgid "Max Execution Time"
msgstr "Tempo di esecuzione massima"

#: views/packages/details/detail.php:437 views/packages/main/s1.setup2.php:314
#: views/packages/main/s2.scan2.php:64 views/packages/main/s2.scan2.php:75
#: views/packages/main/s2.scan2.php:81 views/packages/main/s2.scan3.php:748
msgid "details"
msgstr "dettagli"

#: views/packages/main/s2.scan2.php:63
msgid "Issues might occur when [open_basedir] is enabled. Work with your server admin to disable this value in the php.ini file if you’re having issues building a Backup."
msgstr "Potrebbero verificarsi dei problemi quando [open_basedir] è abilitato. Collabora con l'amministratore del tuo server per disabilitare questo valore nel file php.ini se hai problemi nella creazione di un backup."

#: views/packages/main/s2.scan2.php:52
msgid "Supported web servers: "
msgstr "Server web supportati:"

#: classes/class.server.php:509 views/packages/main/s2.scan2.php:51
msgid "Web Server"
msgstr "Server web"

#: views/packages/main/s2.scan1.php:243
msgid "Scan Complete"
msgstr "Scansione completata"

#: views/packages/main/s2.scan1.php:218 views/packages/main/s3.build.php:156
msgid "Please Wait..."
msgstr "Attendi..."

#: views/packages/main/s2.scan1.php:216
msgid "Scanning Site"
msgstr "Scansione del sito in corso"

#: views/packages/main/s1.setup2.php:573
msgid "This will clear and reset all of the current Backup settings.  Would you like to continue?"
msgstr "In questo modo cancellerai e resetterai tutte le impostazioni attuali del backup. Vuoi continuare?"

#: template/parts/DashboardWidget/sections-section.php:56
#: views/packages/main/s1.setup2.php:563
msgid "Next"
msgstr "Successivo"

#: views/packages/main/s1.setup2.php:562
msgid "Reset"
msgstr "Reimposta"

#: views/packages/main/s1.setup2.php:473
msgid "Host Port"
msgstr "Porta host"

#: classes/class.server.php:529 views/packages/details/detail.php:514
#: views/packages/main/s1.setup2.php:460
msgid "Host"
msgstr "Host"

#: views/packages/details/detail.php:510 views/packages/main/s1.setup2.php:457
msgid " MySQL Server"
msgstr "Server MySQL"

#: views/packages/main/s1.setup2.php:236
msgid "Checked tables will not be added to the database script.  Excluding certain tables can possibly cause your site or plugins to not work correctly after install!"
msgstr "Le tabelle selezionate non saranno aggiunte al nostro script. Escludere le tabelle può causare al tuo sito o ai plugin di non funzionare correttamente dopo l'installazione!"

#: views/packages/main/s1.setup2.php:244
msgid "Exclude All"
msgstr "Escludi tutto"

#: views/packages/main/s1.setup2.php:243
msgid "Include All"
msgstr "Includi tutto"

#: views/packages/main/s1.setup2.php:233
msgid "Enable Table Filters"
msgstr "Abilita i filtri tabelle"

#: template/parts/filters/package_components.php:119
msgid "Media"
msgstr "Media"

#: template/parts/filters/package_components.php:161
#: template/parts/filters/package_components.php:163
msgid "File Extensions"
msgstr "Estensioni file"

#: template/parts/filters/package_components.php:149
msgid "cache"
msgstr "cache"

#: template/parts/filters/package_components.php:144
msgid "root path"
msgstr "percorso root"

#: views/packages/details/detail.php:382 views/packages/main/s2.scan3.php:765
#: views/packages/main/s2.scan3.php:830
msgid "Directories"
msgstr "Directory"

#: template/parts/filters/package_components.php:86
#: views/packages/details/detail.php:518 views/packages/main/s1.setup2.php:214
#: views/packages/main/s1.setup2.php:486 views/packages/main/s2.scan3.php:411
#: views/packages/main/s2.scan3.php:736 views/settings/packages.php:93
msgid "Database"
msgstr "Database"

#: views/packages/details/detail.php:169 views/packages/details/detail.php:401
#: views/packages/main/s1.setup2.php:213 views/packages/main/s2.scan3.php:37
#: views/packages/main/s2.scan3.php:787 views/packages/main/s2.scan3.php:839
msgid "Files"
msgstr "File"

#: views/packages/main/s1.setup2.php:196
msgid "Database filter enabled"
msgstr "Il filtro database è abilitato"

#: views/packages/main/s1.setup2.php:192
msgid "File filter enabled"
msgstr "Filtro file abilitato"

#: template/mocks/storage/storage.php:122 views/packages/details/detail.php:307
#: views/packages/main/s1.setup2.php:145
msgid "Local"
msgstr "Locale"

#: template/mocks/storage/storage.php:119 views/packages/details/detail.php:294
#: views/packages/main/s1.setup2.php:132
msgid "Default"
msgstr "Predefinito"

#: views/packages/main/s1.setup2.php:123 views/settings/storage.php:75
msgid "Location"
msgstr "Posizione"

#: template/mocks/storage/storage.php:109 views/packages/details/detail.php:287
#: views/packages/details/detail.php:426 views/packages/main/s1.setup2.php:122
#: views/settings/license.php:23
msgid "Type"
msgstr "Tipo"

#: src/Core/Bootstrap.php:305 src/Core/Bootstrap.php:306
#: template/mocks/storage/storage.php:59 views/packages/details/detail.php:278
#: views/packages/main/s1.setup2.php:104 views/settings/controller.php:42
msgid "Storage"
msgstr "Archiviazione"

#: views/packages/main/s1.setup1.php:257
msgid "Remove Files Now"
msgstr "Rimuovi adesso i file"

#: views/packages/main/s1.setup1.php:239
msgid "Reserved Files"
msgstr "File riservati"

#: views/packages/main/s1.setup1.php:219
#: views/tools/diagnostics/inc.data.php:27
msgid "more info"
msgstr "maggiori informazioni"

#: views/packages/main/s1.setup1.php:208
msgid "MySQLi Support"
msgstr "Supporto MySQLi"

#: views/packages/main/s1.setup1.php:204
msgid "MySQL Version"
msgstr "Versione MySQL"

#: views/packages/main/s1.setup1.php:198
msgid "Server Support"
msgstr "Supporto server"

#: views/packages/main/s1.setup1.php:164
msgid "Required Paths"
msgstr "Percorsi richiesti"

#: views/packages/main/s1.setup1.php:140 views/packages/main/s1.setup1.php:145
#: views/packages/main/s1.setup1.php:150
msgid "Function"
msgstr "Funzione"

#: views/packages/main/s1.setup1.php:135
msgid "Safe Mode Off"
msgstr "Modalità sicura off"

#: views/packages/main/s1.setup1.php:118
msgid "Zip Archive Enabled"
msgstr "Zip Archive attivo"

#: classes/class.server.php:583 views/packages/main/s1.setup1.php:112
#: views/packages/main/s2.scan2.php:56
msgid "PHP Version"
msgstr "Versione PHP"

#: views/packages/main/s1.setup1.php:106
msgid "PHP Support"
msgstr "Supporto PHP"

#: views/packages/main/s1.setup1.php:100
msgid "System requirements must pass for the Duplicator to work properly.  Click each link for details."
msgstr "Richieste di sistema devono essere passate a Duplicator per funzionare correttamente. Clicca ogni link per dettagli."

#: views/packages/main/s1.setup1.php:91
msgid "Requirements:"
msgstr "Requisiti:"

#: views/packages/main/s1.setup1.php:67 views/packages/main/s2.scan1.php:196
#: views/packages/main/s2.scan1.php:280 views/packages/main/s3.build.php:114
msgid "Build"
msgstr "Compila"

#: views/packages/main/s1.setup1.php:66 views/packages/main/s2.scan1.php:195
#: views/packages/main/s3.build.php:113
msgid "Scan"
msgstr "Scansiona"

#: views/packages/details/detail.php:474 views/packages/main/s1.setup1.php:65
#: views/packages/main/s1.setup2.php:401 views/packages/main/s2.scan1.php:194
#: views/packages/main/s2.scan2.php:10 views/packages/main/s3.build.php:112
msgid "Setup"
msgstr "Configurazione"

#: views/packages/main/s1.setup1.php:15
msgid "Backup settings have been reset."
msgstr "Le impostazioni di backup sono state resettate."

#: views/packages/details/detail.php:554
msgid "LOG"
msgstr "LOG"

#: views/packages/details/detail.php:418
msgid "DATABASE"
msgstr "DATABASE"

#: views/packages/main/packages.php:379
msgid "Are you sure you want to delete the selected Backup(s)?"
msgstr "Confermi di voler eliminare i backup selezionati?"

#: views/packages/details/detail.php:245
msgid "The following links contain sensitive data. Share with caution!"
msgstr "I seguenti link contengono dati sensibili. Condividi con cautela!"

#: views/packages/details/detail.php:242
msgid "Download Links"
msgstr "Link per il download"

#: views/packages/main/s2.scan3.php:542
msgid "Total Size"
msgstr "Dimensione totale"

#: views/packages/details/detail.php:200
msgid "Log"
msgstr "Log"

#: views/packages/details/detail.php:122 views/packages/main/s1.setup2.php:93
#: views/packages/main/s2.scan3.php:731
msgid "Notes"
msgstr "Note"

#: views/packages/details/detail.php:110
msgid "Hash"
msgstr "Hash"

#: classes/class.server.php:593 views/packages/details/detail.php:165
#: views/packages/details/detail.php:522 views/packages/main/s1.setup2.php:499
msgid "User"
msgstr "Utente"

#: classes/class.server.php:669
#: template/admin_pages/settings/general/general.php:114
#: views/packages/details/detail.php:130
msgid "Version"
msgstr "Versione"

#: views/packages/main/s3.build.php:187
msgid "Archive"
msgstr "Archivio"

#: views/packages/details/detail.php:190 views/packages/details/detail.php:223
#: views/packages/details/detail.php:466 views/packages/main/packages.php:291
#: views/packages/main/s1.setup2.php:371 views/packages/main/s3.build.php:184
#: views/settings/packages.php:328
msgid "Installer"
msgstr "Installer"

#: template/mocks/storage/storage.php:108 views/packages/details/detail.php:98
#: views/packages/details/detail.php:286 views/packages/details/detail.php:422
#: views/packages/main/packages.php:196 views/packages/main/s1.setup2.php:84
#: views/packages/main/s1.setup2.php:121 views/packages/main/s2.scan3.php:730
msgid "Name"
msgstr "Nome"

#: views/packages/main/packages.php:195 views/packages/main/s2.scan3.php:98
#: views/packages/main/s2.scan3.php:439
msgid "Size"
msgstr "Dimensione"

#: views/packages/details/detail.php:126 views/packages/main/packages.php:194
msgid "Created"
msgstr "Data di creazione"

#: views/packages/details/controller.php:70
msgid "Details"
msgstr "Dettagli"

#: template/parts/DashboardWidget/package-create-section.php:46
#: views/packages/main/packages.php:144 views/packages/main/s3.build.php:139
msgid "Create New"
msgstr "Crea nuovo"

#: template/mocks/storage/storage.php:84 views/packages/main/packages.php:103
msgid "Apply"
msgstr "Applica"

#: template/mocks/storage/storage.php:81 views/packages/main/packages.php:98
msgid "Delete"
msgstr "Elimina"

#: views/packages/main/packages.php:97
msgid "Delete selected backup(s)"
msgstr "Elimina il/i backup selezionato/i"

#: template/mocks/storage/storage.php:79 views/packages/main/packages.php:95
msgid "Bulk Actions"
msgstr "Azioni di gruppo"

#: views/parts/migration-clean-installation-files.php:82
msgid "Help Support Duplicator"
msgstr "Supporto Duplicator"

#: template/parts/help/main.php:236
msgid "Get Support"
msgstr "Ottieni supporto"

#: views/tools/diagnostics/support.php:74
msgid "Change Log"
msgstr "Registro cambiamenti"

#: views/tools/diagnostics/support.php:71
msgid "FAQs"
msgstr "FAQ"

#: views/tools/diagnostics/support.php:66
msgid "User Guide"
msgstr "Guida utente"

#: views/tools/diagnostics/support.php:63
msgid "Quick Start"
msgstr "Avvio veloce"

#: views/tools/diagnostics/support.php:58
msgid "Choose A Section"
msgstr "Scegli una sezione"

#: views/tools/diagnostics/support.php:54
msgid "Complete Online Documentation"
msgstr "Documentazione online completa"

#: views/tools/diagnostics/support.php:51
msgid "Knowledgebase"
msgstr "Documentazione"

#: views/tools/diagnostics/support.php:35
msgid "Migrating WordPress is a complex process and the logic to make all the magic happen smoothly may not work quickly with every site.  With over 30,000 plugins and a very complex server eco-system some migrations may run into issues.  This is why the Duplicator includes a detailed knowledgebase that can help with many common issues.  Resources to additional support, approved hosting, and alternatives to fit your needs can be found below."
msgstr "La migrazione di WordPress è un processo complesso e la logica per far sì che tutta la magia avvenga senza problemi potrebbe non funzionare rapidamente in tutti i siti. Con oltre 30.000 plugin e un ecosistema di server veramente complesso alcune migrazioni possono presentare dei problemi. Per questo Duplicator include una base di conoscenza dettagliata che può essere d'aiuto per molte questioni comuni. Di seguito sono riportate le risorse per il supporto aggiuntivo, gli hosting approvati e le alternative per soddisfare le tue necessità."

#: src/Controllers/AboutUsController.php:239
msgid "Customer Support"
msgstr "Servizio clienti"

#: src/Controllers/AboutUsController.php:155 src/Utils/Upsell.php:38
#: src/Utils/Upsell.php:64 template/admin_pages/welcome/features.php:28
msgid "Scheduled Backups"
msgstr "Backup programmati"

#: src/Controllers/AboutUsController.php:138
msgid "Migration Wizard"
msgstr "Wizard migrazione"

#: src/Controllers/AboutUsController.php:130
msgid "Backup Files & Database"
msgstr "Backup File & Database"

#: views/packages/details/detail.php:333 views/packages/main/packages.php:174
#: views/packages/main/s1.setup2.php:163 views/packages/main/s2.scan3.php:603
#: views/packages/main/s2.scan3.php:698 views/packages/main/s3.build.php:232
msgid "Duplicator Pro"
msgstr "Duplicator Pro"

#: views/settings/about-info.php:91
msgid "Spread the Word"
msgstr "Spargi la voce"

#: src/Core/Bootstrap.php:548 views/settings/license.php:11
msgid "Manage"
msgstr "Gestisci"

#: src/Core/Bootstrap.php:314 src/Core/Bootstrap.php:315
#: views/tools/controller.php:21
msgid "Tools"
msgstr "Strumenti"

#: src/Core/Bootstrap.php:325 src/Core/Bootstrap.php:326
#: template/admin_pages/settings/general/general.php:217
#: template/mocks/storage/storage.php:87 views/settings/controller.php:23
msgid "Settings"
msgstr "Impostazioni"

#: views/packages/main/packages.php:107
msgid "Get Help"
msgstr "Ottieni aiuto"

#: classes/utilities/class.u.php:513
msgid "You do not have sufficient permissions to access this page."
msgstr "Non hai i permessi necessari per potere accedere a questa pagina."

#: views/parts/migration-almost-complete.php:23
msgid "Reserved Duplicator installation files have been detected in the root directory.  Please delete these installation files to avoid security issues."
msgstr "Sono stati rilevati file di installazione riservati di Duplicator nella directory principale.  Elimina questi file di installazione per evitare problemi di sicurezza."