# Translation of Plugins - Contact Form 7 - Stable (latest release) in Italian
# This file is distributed under the same license as the Plugins - Contact Form 7 - Stable (latest release) package.
msgid ""
msgstr ""
"PO-Revision-Date: 2025-03-02 21:28:53+0000\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: GlotPress/4.0.1\n"
"Language: it\n"
"Project-Id-Version: Plugins - Contact Form 7 - Stable (latest release)\n"

#: admin/includes/welcome-panel.php:97
msgid "contribute to the project"
msgstr "contribuisci al progetto"

#: admin/includes/welcome-panel.php:96
msgid "https://contactform7.com/contributing/"
msgstr "https://contactform7.com/contributing/"

#: modules/textarea.php:152
msgid "Text area"
msgstr "Area di testo"

#: modules/text.php:242
msgid "URL field"
msgstr "Campo URL"

#: modules/text.php:236
msgid "Email address field"
msgstr "Campo indirizzo email"

#: modules/select.php:241
msgid "Drop-down menu"
msgstr "Menu a discesa"

#: modules/quiz.php:269
msgid "The capital of Brazil? | Rio"
msgstr "La capitale del Brasile? | Rio"

#: modules/file.php:231
msgid "In bytes. You can use kb and mb suffixes."
msgstr "In byte. Puoi usare i suffissi kb e mb."

#: modules/file.php:223
msgid "File size limit"
msgstr "Limite dimensione file"

#: modules/checkbox.php:365
msgid "Checkboxes"
msgstr "Caselle di controllo"

#: modules/acceptance.php:321
msgid "Put the condition for consent here."
msgstr "Inserisci qui la condizione per il consenso."

#: modules/acceptance.php:301
msgid "This checkbox is optional."
msgstr "Questa casella di controllo è facoltativa."

#: admin/includes/tag-generator.php:453
msgid "Use the first item as a label."
msgstr "Usa il primo elemento come un'etichetta."

#: admin/includes/tag-generator.php:438
msgid ""
"Option 1\n"
"Option 2\n"
"Option 3"
msgstr ""
"Opzione 1\n"
"Opzione 2\n"
"Opzione 3"

#: admin/includes/tag-generator.php:425
msgid "One item per line."
msgstr "Un elemento per riga."

#: admin/includes/tag-generator.php:417
msgid "Selectable values"
msgstr "Valori selezionabili"

#: admin/includes/tag-generator.php:396
msgid "Use this text as the placeholder."
msgstr "Usa questo testo come segnaposto."

#: admin/includes/tag-generator.php:323 modules/text.php:299
#: modules/textarea.php:194
msgid "Length"
msgstr "Lunghezza"

#: admin/includes/tag-generator.php:236
msgid "Field name"
msgstr "Nome del campo"

#: admin/includes/tag-generator.php:121
msgid "Close"
msgstr "Chiudi"

#: modules/checkbox.php:218
msgid "Too many items are selected."
msgstr "Sono stati selezionati troppi elementi."

#: includes/contact-form.php:574
msgid "Invalid action URL is detected."
msgstr "È stato rilevato un URL di azione non valido."

#: modules/constant-contact/service.php:377
msgid "This feature is deprecated. You are not recommended to use it."
msgstr "Questa funzionalità è deprecata. Ti sconsigliamo di usarla."

#: modules/constant-contact/service.php:376
msgid "https://contactform7.com/2024/02/02/we-end-the-constant-contact-integration/"
msgstr "https://contactform7.com/2024/02/02/we-end-the-constant-contact-integration/"

#: modules/constant-contact/service.php:374
msgid "Warning:"
msgstr "Attenzione:"

#: modules/checkbox.php:299 modules/select.php:217
msgid "Undefined value was submitted through this field."
msgstr "È stato inviato un valore non definito in questo campo."

#: includes/rest-api.php:360
msgid "There is no valid unit tag."
msgstr "Non c'è un tag unit valido."

#: modules/sendinblue/contact-form-properties.php:100
#: modules/sendinblue/service.php:160
msgid "Brevo integration"
msgstr "Integrazione con Brevo"

#. translators: 1: blog name, 2: blog URL
#: includes/contact-form-template.php:111
msgid "This email is a receipt for your contact form submission on our website (%1$s %2$s) in which your email address was used. If that was not you, please ignore this message."
msgstr "Questa e-mail è una ricevuta dell'invio del modulo di contatto sul nostro sito web (%1$s %2$s) in cui è stato utilizzato il vostro indirizzo e-mail. Se non siete stati voi, ignorate questo messaggio."

#. translators: 1: blog name, 2: blog URL
#: includes/contact-form-template.php:77
msgid "This is a notification that a contact form was submitted on your website (%1$s %2$s)."
msgstr "Questa è una notifica che segnala l'invio di un modulo di contatto dal vostro sito web (%1$s %2$s)."

#: includes/config-validator/mail.php:234
#: includes/config-validator/mail.php:324
msgid "Unsafe email config is used without sufficient protection."
msgstr "La configurazione delle e-mail non sicure è utilizzata senza una protezione sufficiente."

#: includes/config-validator/validator.php:229
msgid "Configuration error is detected."
msgstr "È stato rilevato un errore di configurazione."

#: modules/sendinblue/service.php:167
msgid "Brevo is active on this site."
msgstr "Brevo è attivo su questo sito."

#: admin/includes/welcome-panel.php:152
#: modules/sendinblue/contact-form-properties.php:108
#: modules/sendinblue/contact-form-properties.php:296
#: modules/sendinblue/service.php:30
msgid "Brevo"
msgstr "Brevo"

#: admin/admin.php:667
msgid "The next major release of Contact Form 7 will discontinue support for outdated PHP versions. If you don't upgrade PHP, you will not be able to upgrade the plugin."
msgstr "La prossima versione di Contact Form 7 interromperà il supporto alle versioni obsolete di PHP. Se non aggiornerai PHP, non potrai aggiornare il plugin."

#: includes/contact-form-functions.php:264
msgid "Contact form not found."
msgstr "Modulo di contatto non trovato."

#: includes/contact-form-functions.php:263 includes/contact-form.php:573
#: includes/js/index.js:1
msgid "Error:"
msgstr "Errore:"

#: includes/block-editor/block.json
msgctxt "block keyword"
msgid "form"
msgstr "modulo"

#: includes/block-editor/block.json
msgctxt "block description"
msgid "Insert a contact form you have created with Contact Form 7."
msgstr "Inserisci un modulo di contatto che è stato creato con Contact Form 7."

#: includes/block-editor/block.json
msgctxt "block title"
msgid "Contact Form 7"
msgstr "Contact Form 7"

#: modules/select.php:88
msgid "&#8212;Please choose an option&#8212;"
msgstr "&#8212;Seleziona un'opzione&#8212;"

#: modules/akismet/service.php:65
msgid "Akismet is active on this site."
msgstr "Akismet è attivo su questo sito."

#: modules/akismet/service.php:51
msgid "CAPTCHAs are designed to distinguish spambots from humans, and are therefore helpless against human spammers. In contrast to CAPTCHAs, Akismet checks form submissions against the global database of spam; this means Akismet is a comprehensive solution against spam. This is why we consider Akismet to be the centerpiece of the spam prevention strategy."
msgstr "I CAPTCHA sono destinati a distinguere gli spambot dagli umani ed in più sono utili contro gli spammer umani. Rispetto ai CAPTCHA, Akismet controlla gli invii dei moduli rispetto al database globale degli spam; questo significa che Akismet è una soluzione a tutto tondo contro lo spam. Questa è la ragione per cui consideriamo Akismet il nucleo centrale della strategia di prevenzione contro lo spam."

#: modules/akismet/akismet.php:300
msgid "Learn how your data is processed."
msgstr "Scopri come vengono elaborati i tuoi dati."

#: modules/akismet/akismet.php:297
msgid "This form uses Akismet to reduce spam."
msgstr "Questo modulo utilizza Akismet per ridurre lo spam."

#: includes/contact-form.php:647
msgid "Contact form"
msgstr "Modulo di contatto"

#: includes/config-validator/form.php:81
msgid "Uploadable file size exceeds PHP’s maximum acceptable size."
msgstr "La dimensione che si può caricare per i file, eccede la dimensione massima accettata da PHP."

#: includes/config-validator/form.php:69
msgid "Colons are used in form-tag names."
msgstr "I due punti sono utilizzati nei nomi dei form-tag."

#: modules/text.php:197
msgid "Please enter a telephone number."
msgstr "Inserisci numero di telefono."

#: modules/text.php:190
msgid "Please enter a URL."
msgstr "Inserisci un URL."

#: modules/text.php:183
msgid "Please enter an email address."
msgstr "Inserisci un indirizzo email."

#: modules/number.php:191
msgid "This field has a too large number."
msgstr "Numero troppo lungo per questo campo."

#: modules/number.php:186
msgid "This field has a too small number."
msgstr "Numero troppo corto per questo campo."

#: modules/number.php:181
msgid "Please enter a number."
msgstr "Inserisci un numero."

#: modules/date.php:172
msgid "This field has a too late date."
msgstr "Data troppo posticipata per questo campo."

#: modules/date.php:167
msgid "This field has a too early date."
msgstr "Data troppo antecedente per questo campo."

#: modules/date.php:162
msgid "Please enter a date in YYYY-MM-DD format."
msgstr "Inserisci la data nel formato YYYY-MM-DD."

#: includes/rest-api.php:334
msgid "The request payload format is not supported."
msgstr "Il formato del payload della richiesta non è supportato."

#. translators: 1: WordPress hook name, 2: version number
#: includes/functions.php:570
msgid "Hook %1$s is <strong>deprecated</strong> since Contact Form 7 version %2$s with no alternative available."
msgstr "L'hook %1$s è <strong>deprecato</strong> dalla versione %2$s di Contact Form 7, non ci sono alternative disponibili."

#: includes/file.php:113
msgid "The uploaded file is too large."
msgstr "Il file caricato è troppo grande."

#: includes/contact-form-template.php:215
msgid "This field has a too short input."
msgstr "Valore troppo corto per questo campo."

#: includes/contact-form-template.php:208
msgid "This field has a too long input."
msgstr "Valore troppo lungo per questo campo."

#: includes/contact-form-template.php:201
msgid "Please fill out this field."
msgstr "Compila questo campo."

#. translators: 1: PHP function name, 2: version number, 3: alternative
#. function name
#: includes/functions.php:526
msgid "Function %1$s is <strong>deprecated</strong> since Contact Form 7 version %2$s! Use %3$s instead."
msgstr "%1$s è <strong>deprecato</strong> dalla versione %2$s! Usa al suo posto %3$s."

#: includes/integration.php:25
msgid "Spam protection"
msgstr "Protezione dallo spam"

#. translators: Developer debugging message. 1: PHP function name, 2:
#. Explanatory message, 3: Contact Form 7 version number.
#: includes/functions.php:610
msgid "Function %1$s was called incorrectly. %2$s %3$s"
msgstr "La funzione %1$s non è stata chiamata correttamente. %2$s %3$s"

#. translators: 1: blog name, 2: confirmation link
#: modules/constant-contact/doi.php:76 modules/sendinblue/doi.php:84
msgid ""
"Hello,\n"
"\n"
"This is a confirmation email sent from %1$s.\n"
"\n"
"We have received your submission to our web form, according to which you have allowed us to add you to our contact list. But, the process has not yet been completed. To complete it, please click the following link.\n"
"\n"
"%2$s\n"
"\n"
"If it was not your intention, or if you have no idea why you received this message, please do not click on the link, and ignore this message. We will never collect or use your personal data without your clear consent.\n"
"\n"
"Sincerely,\n"
"%1$s"
msgstr ""
"Ciao,\n"
"\n"
"Questa è una mail di conferma inviata da %1$s.\n"
"\n"
"Abbiamo ricevuto il tuo messaggio nel nostro modulo online, in base al quale ci hai autorizzato ad aggiungerti alla nostra lista di contatti. Tuttavia, il processo non è ancora stato completato. Per completarlo, fai clic sul seguente link.\n"
"\n"
"%2$s\n"
"\n"
"Se non era tua intenzione, o se non hai idea del perché hai ricevuto questo messaggio, non fare clic sul link e ignora questo messaggio. Non raccoglieremo o utilizzeremo mai i tuoi dati personali senza il tuo espresso consenso.\n"
"\n"
"Cordialmente,\n"
"%1$s"

#. translators: %s: blog name
#: modules/constant-contact/doi.php:70 modules/sendinblue/doi.php:78
msgid "Opt-in confirmation from %s"
msgstr "Conferma dell'opt-in da %s"

#. translators: %s: link labeled 'Constant Contact integration'
#: modules/constant-contact/contact-form-properties.php:163
msgid "You can set up the Constant Contact integration here. For details, see %s."
msgstr "Qui puoi impostare l'integrazione con Constant Contact. Per dettagli, vedi %s."

#: modules/stripe/stripe.php:377
msgid "Complete payment"
msgstr "Completa il pagamento"

#: modules/stripe/stripe.php:362
msgid "Proceed to checkout"
msgstr "Procedi al pagamento"

#: modules/stripe/stripe.php:246
msgid "Payment is required. Please pay by credit card."
msgstr "Pagamento necessario. Paga con carta di credito."

#: includes/integration.php:27
msgid "Payments"
msgstr "Pagamenti"

#: modules/stripe/service.php:214
msgid "Publishable Key"
msgstr "Chiave pubblicabile"

#: modules/stripe/service.php:191
msgid "Stripe is not available on this site. It requires an HTTPS-enabled site."
msgstr "Stripe non è disponibile su questo sito. Richiede un sito su cui sia abilitato HTTPS."

#: modules/stripe/service.php:176
msgid "Stripe is active on this site."
msgstr "Stripe è attivo su questo sito."

#: modules/stripe/service.php:169
msgid "Stripe integration"
msgstr "Integrazione Stripe"

#: modules/stripe/service.php:162
msgid "Stripe is a simple and powerful way to accept payments online. Stripe has no setup fees, no monthly fees, and no hidden costs. Millions of businesses rely on Stripe’s software tools to accept payments securely and expand globally."
msgstr "Stripe è un modo semplice e potente per accettare pagamenti online. Non ha costi di installazione, abbonamento mensile e costi nascosti. Milioni di aziende si sono già affidate agli strumenti software di Stripe per accettare pagamenti in modo sicuro ed espandersi a livello globale."

#. translators: 1: first name, 2: last name
#: modules/sendinblue/sendinblue.php:114
msgctxt "personal name"
msgid "%1$s %2$s"
msgstr "%1$s %2$s"

#: includes/rest-api.php:513
msgid "Unique identifier for the contact form."
msgstr "Identificativo univoco per il modulo di contatto."

#: includes/config-validator/form.php:57
msgid "Dots are used in form-tag names."
msgstr "I punti sono usati nei nomi form-tag."

#: admin/includes/welcome-panel.php:298
msgid "Show welcome panel"
msgstr "Mostra il pannello di benvenuto"

#: admin/includes/welcome-panel.php:295
msgid "Welcome panel"
msgstr "Pannello di benvenuto"

#: admin/includes/welcome-panel.php:164 modules/stripe/service.php:36
msgid "Stripe"
msgstr "Stripe"

#: admin/includes/welcome-panel.php:163 modules/stripe/service.php:168
msgid "https://contactform7.com/stripe-integration/"
msgstr "https://contactform7.com/stripe-integration/"

#. translators: 1: link labeled 'reCAPTCHA', 2: link labeled 'Stripe'
#: admin/includes/welcome-panel.php:157
msgid "With help from cloud-based machine learning, anti-spam services will protect your forms (%1$s). Even payment services are natively supported (%2$s)."
msgstr "I servizi antispam proteggeranno i tuoi moduli (%1$s) con l'aiuto del machine learning basato su Cloud. Anche i servizi di pagamento sono supportati nativamente (%2$s)."

#. translators: 1: link labeled 'Brevo'
#: admin/includes/welcome-panel.php:149
msgid "Your contact forms will become more powerful and versatile by integrating them with external APIs. With CRM and email marketing services, you can build your own contact lists (%1$s)."
msgstr "Integrando le API esterne, i tuoi moduli di contatto saranno ancora più potenti e versatili. Con CRM e servizi di email marketing, puoi creare le tue liste di contatti (%1$s)."

#: admin/includes/welcome-panel.php:141
msgid "You have strong allies to back you up."
msgstr "Al tuo fianco hai partner importanti."

#: admin/admin.php:519
msgid "Integration with external APIs"
msgstr "Integrazione con API esterne"

#: admin/admin.php:518
msgid "https://contactform7.com/integration-with-external-apis/"
msgstr "https://contactform7.com/integration-with-external-apis/"

#. translators: %s: link labeled 'Integration with external APIs'
#: admin/admin.php:516
msgid "You can expand the possibilities of your contact forms by integrating them with external services. For details, see %s."
msgstr "Puoi ampliare le funzionalità dei tuoi moduli di contatto integrandoli con dei servizi esterni. Per dettagli, vedi %s."

#: admin/admin.php:65 admin/admin.php:511
msgid "Integration with External API"
msgstr "Integrazione con API esterne"

#: modules/sendinblue/contact-form-properties.php:281
msgid "Manage your email templates"
msgstr "Gestisci i tuoi template delle email"

#: modules/sendinblue/contact-form-properties.php:266
msgid "You have no active email template yet."
msgstr "Non hai alcun template dell'email attivo."

#: modules/sendinblue/contact-form-properties.php:248
msgid "&mdash; Select &mdash;"
msgstr "&mdash; Seleziona &mdash;"

#: modules/sendinblue/contact-form-properties.php:237
msgid "Select an email template:"
msgstr "Seleziona un template dell'email:"

#: modules/sendinblue/contact-form-properties.php:220
msgid "Send a welcome email to new contacts"
msgstr "Invia una email di benvenuto ai nuovi contatti"

#: modules/sendinblue/contact-form-properties.php:202
#: modules/sendinblue/contact-form-properties.php:211
msgid "Welcome email"
msgstr "Email di benvenuto"

#: modules/constant-contact/contact-form-properties.php:258
#: modules/sendinblue/contact-form-properties.php:192
#: modules/sendinblue/contact-form-properties.php:282
msgid "(opens in a new tab)"
msgstr "(si apre in una nuova scheda)"

#: modules/constant-contact/contact-form-properties.php:257
#: modules/sendinblue/contact-form-properties.php:191
msgid "Manage your contact lists"
msgstr "Gestisci le tue liste di contatti"

#: modules/constant-contact/contact-form-properties.php:242
#: modules/sendinblue/contact-form-properties.php:176
msgid "You have no contact list yet."
msgstr "Non hai ancora alcuna lista di contatti."

#: modules/constant-contact/contact-form-properties.php:220
#: modules/sendinblue/contact-form-properties.php:154
msgid "Select lists to which contacts are added:"
msgstr "Seleziona le liste in cui inserire i contatti:"

#: modules/constant-contact/contact-form-properties.php:203
#: modules/sendinblue/contact-form-properties.php:137
msgid "Add form submitters to your contact lists"
msgstr "Aggiungi chi invia il modulo alle tue liste di contatti"

#: modules/constant-contact/contact-form-properties.php:185
#: modules/constant-contact/contact-form-properties.php:194
#: modules/sendinblue/contact-form-properties.php:119
#: modules/sendinblue/contact-form-properties.php:128
msgid "Contact lists"
msgstr "Liste di contatti"

#. translators: %s: link labeled 'Brevo integration'
#: modules/sendinblue/contact-form-properties.php:96
msgid "You can set up the Brevo integration here. For details, see %s."
msgstr "Qui puoi impostare l'integrazione con Brevo. Per dettagli, guarda %s."

#: modules/sendinblue/service.php:216
msgid "Save changes"
msgstr "Salva le modifiche"

#: modules/sendinblue/service.php:212
msgctxt "API keys"
msgid "Remove key"
msgstr "Rimuovi chiave"

#: modules/sendinblue/service.php:191
msgid "API key"
msgstr "Chiave API"

#: modules/sendinblue/service.php:177
msgid "Setup integration"
msgstr "Impostazioni di integrazione"

#: admin/includes/welcome-panel.php:151
#: modules/sendinblue/contact-form-properties.php:99
#: modules/sendinblue/service.php:159
msgid "https://contactform7.com/sendinblue-integration/"
msgstr "https://contactform7.com/sendinblue-integration/"

#: modules/sendinblue/service.php:153
msgid "Store and organize your contacts while protecting user privacy on Brevo, the leading CRM & email marketing platform in Europe. Brevo offers unlimited contacts and advanced marketing features."
msgstr "Conserva e organizza i tuoi contatti proteggendo la privacy degli utenti su Brevo, la piattaforma di CRM e mail marketing leader in Europa. Brevo offre contatti illimitati e funzionalità di marketing avanzate."

#: modules/sendinblue/service.php:125
msgid "You have not been authenticated. Make sure the provided API key is correct."
msgstr "Non sei stato autenticato. Assicurati che la chiave API inserita sia corretta."

#: includes/mail.php:311
msgid "Failed to attach a file. The total file size exceeds the limit of 25 megabytes."
msgstr "Impossibile allegare il file. La dimensione totale del file eccede il limite di 25 megabyte."

#. translators: %s: Attachment file path.
#: includes/mail.php:291
msgid "Failed to attach a file. %s is not a readable file."
msgstr "Impossibile allegare il file. %s non è un file leggibile."

#. translators: %s: Attachment file path.
#: includes/mail.php:277
msgid "Failed to attach a file. %s is not in the allowed directory."
msgstr "Impossibile allegare il file. %s non è in una directory consentita."

#: includes/contact-form-template.php:42
msgid "Submit"
msgstr "Invia"

#: includes/contact-form-template.php:37
msgid "(optional)"
msgstr "(facoltativo)"

#: admin/includes/welcome-panel.php:69
msgid "disallowed list"
msgstr "elenco delle parole non consentite"

#. translators: %s: comma separated list of disallowed words
#: modules/disallowed-list.php:36
msgid "Disallowed words (%s) are used."
msgstr "Sono utilizzate parole non consentite (%s)."

#: modules/disallowed-list.php:32
msgid "Disallowed words are used."
msgstr "Sono utilizzate parole non consentite."

#. translators: %s: Contact Form 7 version number.
#: includes/functions.php:601
msgid "(This message was added in Contact Form 7 version %s.)"
msgstr "(Questo messaggio è stato aggiunto a Contact Form 7 versione %s.)"

#: includes/special-mail-tags.php:22 includes/special-mail-tags.php:104
#: includes/special-mail-tags.php:170 includes/special-mail-tags.php:239
#: modules/flamingo.php:304
msgid "The fourth parameter ($mail_tag) must be an instance of the WPCF7_MailTag class."
msgstr "Il quarto parametro ($mail_tag) deve essere un'istanza della classe WPCF7_MailTag."

#. translators: time format, see https://www.php.net/date
#: admin/includes/class-contact-forms-list-table.php:238
msgid "g:i a"
msgstr "G:i"

#. translators: 1: date, 2: time
#: admin/includes/class-contact-forms-list-table.php:234
msgid "%1$s at %2$s"
msgstr "%1$s alle %2$s"

#: admin/edit-contact-form.php:245
msgid "docs"
msgstr "documenti"

#. translators: 1: FAQ, 2: Docs ("FAQ & Docs")
#: admin/edit-contact-form.php:238
msgid "%1$s and %2$s"
msgstr "%1$s e %2$s"

#: admin/includes/editor.php:290
msgid "Additional settings"
msgstr "Impostazioni aggiuntive"

#: modules/constant-contact/service.php:354 modules/recaptcha/service.php:261
#: modules/sendinblue/service.php:124 modules/sendinblue/service.php:135
#: modules/stripe/service.php:142
msgid "Error"
msgstr "Errore"

#: includes/config-validator/mail.php:437
msgid "The total size of attachment files is too large."
msgstr "La dimensione totale degli allegati è troppo grande."

#: includes/config-validator/form.php:45
msgid "Unavailable HTML elements are used in the form template."
msgstr "Elementi HTML non disponibili sono usati nel template del modulo."

#: modules/recaptcha/service.php:294
msgid "reCAPTCHA is active on this site."
msgstr "reCAPTCHA è attivo su questo sito."

#: modules/recaptcha/recaptcha.php:148
msgid "reCAPTCHA response token is empty."
msgstr "Il token di risposta di reCAPTCHA è vuoto."

#: includes/submission.php:657
msgid "Submitted nonce is invalid."
msgstr "Il nonce inviato non è valido."

#: includes/submission.php:648
msgid "User-Agent string is unnaturally short."
msgstr "La stringa User-Agent è innaturalmente breve."

#. translators: 1: value of reCAPTCHA score 2: value of reCAPTCHA threshold
#: modules/recaptcha/recaptcha.php:158
msgid "reCAPTCHA score (%1$.2f) is lower than the threshold (%2$.2f)."
msgstr "Il punteggio reCAPTCHA (%1$.2f) è inferiore alla soglia (%2$.2f)."

#: modules/akismet/akismet.php:84
msgid "Akismet returns a spam response."
msgstr "Akismet restituisce una risposta di spam."

#: modules/constant-contact/service.php:468
msgctxt "API keys"
msgid "Reset Keys"
msgstr "Reimposta chiavi"

#: modules/constant-contact/service.php:397
msgid "This site is connected to the Constant Contact API."
msgstr "Questo sito è connesso alle API di Constant Contact."

#: modules/constant-contact/service.php:363
msgid "Configuration updated."
msgstr "Configurazione aggiornata."

#: modules/constant-contact/service.php:355
msgid "Failed to establish connection. Please double-check your configuration."
msgstr "Impossibile stabilire la connessione. Verifica nuovamente la tua configurazione."

#: modules/constant-contact/service.php:345
msgid "Connection established."
msgstr "Connessione stabilita."

#. translators: 1: response code, 2: message, 3: body, 4: URL
#: includes/functions.php:654
msgid "HTTP Response: %1$s %2$s %3$s from %4$s"
msgstr "Risposta HTTP: %1$s %2$s %3$s da %4$s"

#: modules/recaptcha/service.php:366 modules/stripe/service.php:256
msgid "Save Changes"
msgstr "Salva le modifiche"

#: modules/recaptcha/service.php:280
msgid "reCAPTCHA protects you against spam and other types of automated abuse. With Contact Form 7&#8217;s reCAPTCHA integration module, you can block abusive form submissions by spam bots."
msgstr "reCAPTCHA ti protegge contro lo spam ed altri tipi di abusi automatizzati. Con il modulo di integrazione reCAPTCHA di Contact Form 7, puoi bloccare invii abusivi dei moduli da parte di bot spam."

#: modules/recaptcha/recaptcha.php:265 modules/recaptcha/service.php:287
msgid "reCAPTCHA (v3)"
msgstr "reCAPTCHA (v3)"

#. translators: %s: link labeled 'reCAPTCHA (v3)'
#: modules/recaptcha/recaptcha.php:258
msgid "API keys for reCAPTCHA v3 are different from those for v2; keys for v2 do not work with the v3 API. You need to register your sites again to get new keys for v3. For details, see %s."
msgstr "Le chiavi API per reCAPTCHA v3 sono diverse da quelle per v2; le chiavi per v2 non funzionano con le API v3. Devi registrare nuovamente i tuoi siti per avere nuove chiavi per la v3. Per tutti i dettagli, vedi %s."

#: modules/constant-contact/service.php:473
msgid "Connect to the Constant Contact API"
msgstr "Connetti alle API di Constant Contact"

#: modules/recaptcha/service.php:361 modules/stripe/service.php:252
msgctxt "API keys"
msgid "Remove Keys"
msgstr "Rimuovi chiavi"

#: modules/constant-contact/service.php:460
msgid "Set this URL as the redirect URI."
msgstr "Imposta questa URL come redirect URI."

#: modules/constant-contact/service.php:453
msgid "Redirect URI"
msgstr "Redirect URI"

#: modules/constant-contact/service.php:436
msgid "App Secret"
msgstr "App Secret"

#: modules/constant-contact/service.php:419
msgid "API Key"
msgstr "Chiave API"

#: modules/constant-contact/service.php:407 modules/recaptcha/service.php:304
#: modules/stripe/service.php:186
msgid "Setup Integration"
msgstr "Imposta integrazione"

#: modules/constant-contact/contact-form-properties.php:167
#: modules/constant-contact/service.php:390
msgid "Constant Contact integration"
msgstr "Integrazione Constant Contact"

#: modules/constant-contact/contact-form-properties.php:166
#: modules/constant-contact/service.php:389
msgid "https://contactform7.com/constant-contact-integration/"
msgstr "https://contactform7.com/constant-contact-integration/"

#: modules/constant-contact/service.php:383
msgid "The Constant Contact integration module allows you to send contact data collected through your contact forms to the Constant Contact API. You can create reliable email subscription services in a few easy steps."
msgstr "Il modulo di integrazione di Constant Contact consente di inviare i dati dei contatti raccolti tramite i moduli di contatto all'API di Constant Contact. È possibile creare servizi di abbonamento email affidabili in pochi semplici passaggi."

#: modules/constant-contact/contact-form-properties.php:174
#: modules/constant-contact/contact-form-properties.php:272
#: modules/constant-contact/service.php:105
msgid "Constant Contact"
msgstr "Constant Contact"

#: includes/integration.php:26
msgid "Email marketing"
msgstr "Email marketing"

#: includes/config-validator/mail.php:423
msgid "It is not allowed to use files outside the wp-content directory."
msgstr "Non è consentito usare file al di fuori della directory wp-content."

#: admin/edit-contact-form.php:254
msgid "Professional services"
msgstr "Servizi professionali"

#: admin/edit-contact-form.php:253
msgid "https://contactform7.com/custom-development/"
msgstr "https://contactform7.com/custom-development/"

#: admin/edit-contact-form.php:250
msgid "Support forums"
msgstr "Forum di supporto"

#: admin/edit-contact-form.php:249
msgid "https://wordpress.org/support/plugin/contact-form-7/"
msgstr "https://wordpress.org/support/plugin/contact-form-7/"

#: admin/edit-contact-form.php:234
msgid "Here are some available options to help solve your problems."
msgstr "Ecco alcune risorse disponibili per aiutarti nella risoluzione di problemi."

#: admin/edit-contact-form.php:232
msgid "Do you need help?"
msgstr "Hai bisogno di aiuto?"

#: modules/acceptance.php:313
msgid "Condition"
msgstr "Condizione"

#. translators: 1: 'Consented' or 'Not consented', 2: conditions
#: modules/acceptance.php:228
msgctxt "mail output for acceptance checkboxes"
msgid "%1$s: %2$s"
msgstr "%1$s: %2$s"

#: modules/acceptance.php:212
msgid "Not consented"
msgstr "Non accettato"

#: modules/acceptance.php:210
msgid "Consented"
msgstr "Accettato"

#: includes/submission.php:120
msgid "Sending mail has been aborted."
msgstr "L'invio di email è stato interrotto."

#. translators: %s: link labeled 'Editing messages'
#: admin/includes/editor.php:254
msgid "You can edit messages used in various situations here. For details, see %s."
msgstr "Qui puoi modificare i messaggi usati in varie situazioni. Per i dettagli, vedere %s."

#: admin/includes/editor.php:252
msgid "Editing messages"
msgstr "Modifica dei messaggi"

#: admin/includes/editor.php:251
msgid "https://contactform7.com/editing-messages/"
msgstr "https://contactform7.com/editing-messages/"

#. translators: %s: link labeled 'Setting up mail'
#: admin/includes/editor.php:172
msgid "You can edit the mail template here. For details, see %s."
msgstr "Qui puoi modificare il template dell'email. Per i dettagli, vedere %s."

#. translators: %s: link labeled 'Editing form template'
#: admin/includes/editor.php:81
msgid "You can edit the form template here. For details, see %s."
msgstr "Qui puoi modificare il template del modulo. Per i dettagli, vedere %s."

#: admin/includes/editor.php:79
msgid "Editing form template"
msgstr "Modifica del template del modulo"

#: admin/includes/editor.php:78
msgid "https://contactform7.com/editing-form-template/"
msgstr "https://contactform7.com/editing-form-template/"

#: includes/contact-form.php:541 includes/contact-form.php:1064
msgid "This contact form is available only for logged in users."
msgstr "Questo form di contatto è disponibile solo per gli utenti connessi."

#: includes/config-validator/additional-settings.php:19
msgid "Deprecated settings are used."
msgstr "Sono state usate impostazioni deprecate."

#. translators: %s: link labeled 'Really Simple CAPTCHA'
#: modules/really-simple-captcha.php:36
msgid "To use CAPTCHA, you need %s plugin installed."
msgstr "Per utilizzare CAPTCHA, deve essere installato il plugin %s."

#: includes/rest-api.php:319
msgid "There was an error deleting the contact form."
msgstr "Si è verificato un errore eliminando il modulo di contatto."

#: includes/rest-api.php:66 includes/rest-api.php:82 includes/rest-api.php:98
msgid "You are not allowed to access the requested contact form."
msgstr "Non sei abilitato ad accedere al modulo di contatto richiesto."

#: includes/rest-api.php:243 includes/rest-api.php:265
#: includes/rest-api.php:310 includes/rest-api.php:349
#: includes/rest-api.php:421 includes/rest-api.php:440
msgid "The requested contact form was not found."
msgstr "Il modulo di contatto richiesto non è stato trovato."

#: includes/rest-api.php:44
msgid "You are not allowed to create a contact form."
msgstr "Non sei abilitato a creare un modulo di contatto."

#: includes/rest-api.php:197
msgid "Cannot create existing contact form."
msgstr "Impossibile creare un modulo di contatto già esistente."

#: includes/rest-api.php:30
msgid "You are not allowed to access contact forms."
msgstr "Non sei abilitato ad accedere ai moduli di contatto."

#: includes/config-validator/mail.php:409
msgid "Attachment file does not exist at %path%."
msgstr "Il file allegato non esiste in %path%."

#: includes/config-validator/mail.php:310
msgid "Invalid mailbox syntax is used in the %name% field."
msgstr "La sintassi per l'indirizzo di posta nel campo %name% non è valida."

#. translators: %names%: a list of form control names
#: includes/config-validator/form.php:32
msgid "Unavailable names (%names%) are used for form controls."
msgstr "Nomi non disponibili (%names%) sono stati usati per i controlli del modulo."

#: includes/config-validator/mail.php:297
msgid "There are invalid mail header fields."
msgstr "Ci sono campi header non validi."

#: includes/config-validator/messages.php:29
msgid "HTML tags are used in a message."
msgstr "Sono stati usati tag HTML in un messaggio."

#: includes/config-validator/mail.php:198
msgid "Sender email address does not belong to the site domain."
msgstr "L'indirizzo email del mittente non coincide con il dominio del sito."

#: includes/config-validator/mail.php:180
#: includes/config-validator/mail.php:217
msgid "Invalid mailbox syntax is used."
msgstr "È stata usata una sintassi di indirizzo di posta non valida."

#: includes/config-validator/mail.php:160
#: includes/config-validator/mail.php:344
msgid "There is a possible empty field."
msgstr "È possibile che un campo sia vuoto."

#. translators: %s: number of errors detected
#: admin/includes/class-contact-forms-list-table.php:138
msgid "%s configuration error detected"
msgid_plural "%s configuration errors detected"
msgstr[0] "Trovato %s errore di configurazione"
msgstr[1] "Trovati %s errori di configurazione"

#: admin/admin.php:575 includes/rest-api.php:209 includes/rest-api.php:276
msgid "There was an error saving the contact form."
msgstr "Si è verificato un errore nel salvataggio del modulo di contatto."

#. translators: 1: WordPress hook name, 2: version number, 3: alternative hook
#. name
#: includes/functions.php:558
msgid "Hook %1$s is <strong>deprecated</strong> since Contact Form 7 version %2$s! Use %3$s instead."
msgstr "L'hook %1$s è <strong>deprecato</strong> dalla versione %2$s di Contact Form 7! Al suo posto utilizza %3$s."

#: admin/includes/welcome-panel.php:125
msgid "Flamingo"
msgstr "Flamingo"

#. translators: %s: link labeled 'Flamingo'
#: admin/includes/welcome-panel.php:122
msgid "Install a message storage plugin before this happens to you. %s saves all messages through contact forms into the database. Flamingo is a free WordPress plugin created by the same author as Contact Form 7."
msgstr "Installa un plugin di archiviazione di messaggi prima che succeda anche a te. %s conserva nel database tutti i messaggi provenienti da form di contatto. Flamingo è un plugin per WordPress gratuito creato dallo stesso autore di Contact Form 7."

#: admin/includes/welcome-panel.php:119
msgid "Contact Form 7 does not store submitted messages anywhere. Therefore, you may lose important messages forever if your mail server has issues or you make a mistake in mail configuration."
msgstr "Contact Form 7 non memorizza da nessuna parte i messaggi inviati. Quindi, potresti perdere per sempre messaggi importanti se il tuo server di posta dovesse avere qualche problema o se fai un errore nella configurazione della posta."

#: admin/includes/welcome-panel.php:113
msgid "Before you cry over spilt mail&#8230;"
msgstr "Prima che tu pianga per la perdita di email&#8230;"

#: admin/includes/welcome-panel.php:68
msgid "https://contactform7.com/comment-blacklist/"
msgstr "https://contactform7.com/comment-blacklist/"

#. translators: links labeled 1: 'Akismet', 2: 'reCAPTCHA', 3: 'disallowed
#. list'
#: admin/includes/welcome-panel.php:58
msgid "Contact Form 7 supports spam-filtering with %1$s. Intelligent %2$s blocks annoying spambots. Plus, using %3$s, you can block messages containing specified keywords or those sent from specified IP addresses."
msgstr "Contact Form 7 supporta i filtri per lo spam con %1$s. %2$s intelligente blocca i noiosi robot di spam. Inoltre, usando %3$s, puoi bloccare i messaggi contenenti particolari parole chiave o quelli inviati da indirizzi IP specifici."

#: admin/includes/welcome-panel.php:55
msgid "Spammers target everything; your contact forms are not an exception. Before you get spammed, protect your contact forms with the powerful anti-spam features Contact Form 7 provides."
msgstr "Gli spammer prendono di mira qualsiasi cosa; i tuoi form di contatto non sono eccezione. Prima che tu riceva dello spam, proteggi i tuoi form di contatto con le potenti funzioni anti-spam che ti fornisce Contact Form 7."

#: admin/includes/welcome-panel.php:49
msgid "Getting spammed? You have protection."
msgstr "Stai ricevendo dello spam? Ecco la protezione."

#: includes/config-validator/form.php:16
msgid "Multiple form controls are in a single label element."
msgstr "Molteplici controlli del modulo sono posizionati in un unico elemento etichetta."

#: admin/includes/config-validator.php:138
msgid "FAQ about Configuration Validator"
msgstr "Domande frequenti a proposito del Validatore di configurazione"

#: admin/includes/config-validator.php:137
msgid "https://contactform7.com/configuration-validator-faq/"
msgstr "https://contactform7.com/configuration-validator-faq/"

#: modules/quiz.php:181
msgid "The answer to the quiz is incorrect."
msgstr "La risposta al quiz non è corretta."

#: includes/file.php:118
msgid "There was an error uploading the file."
msgstr "Si è verificato un errore durante il caricamento del file."

#: includes/file.php:108
msgid "You are not allowed to upload files of this type."
msgstr "Non sei abilitato al caricamento di file di questo tipo."

#: includes/file.php:103
msgid "There was an unknown error uploading the file."
msgstr "Si è verificato un errore sconosciuto durante il caricamento del file."

#: includes/contact-form-template.php:194
msgid "You must accept the terms and conditions before sending your message."
msgstr "Devi accettare termini e condizioni prima di inviare il tuo messaggio."

#: includes/contact-form-template.php:180
msgid "One or more fields have an error. Please check and try again."
msgstr "Uno o più campi hanno degli errori. Controlla e riprova."

#: includes/contact-form-template.php:173
#: includes/contact-form-template.php:187
msgid "There was an error trying to send your message. Please try again later."
msgstr "Si è verificato un errore durante l'invio del tuo messaggio. Riprova più tardi."

#: includes/contact-form-template.php:166
msgid "Thank you for your message. It has been sent."
msgstr "Grazie per il tuo messaggio. È stato inviato."

#. translators: 1: blog name, 2: [your-subject]
#: includes/contact-form-template.php:52 includes/contact-form-template.php:96
msgctxt "mail subject"
msgid "%1$s \"%2$s\""
msgstr "%1$s \"%2$s\""

#: admin/includes/config-validator.php:48
msgid "Misconfiguration leads to mail delivery failure or other troubles. Validate your contact forms now."
msgstr "Una configurazione non corretta può portare a invii falliti o altri problemi. Valida ora i tuoi moduli di contatto."

#: admin/includes/config-validator.php:45
msgid "Validate Contact Form 7 Configuration"
msgstr "Valida la configurazione di Contact Form 7"

#: includes/config-validator/validator.php:56
msgid "https://contactform7.com/configuration-errors/"
msgstr "https://contactform7.com/configuration-errors/"

#: admin/admin.php:597
msgid "Configuration validation completed. No invalid contact form was found."
msgstr "Validazione configurazione completata. Nessun modulo di contatto non valido trovato."

#. translators: %s: number of contact forms
#: admin/admin.php:587
msgid "Configuration validation completed. %s invalid contact form was found."
msgid_plural "Configuration validation completed. %s invalid contact forms were found."
msgstr[0] "Validazione della configurazione completata. %s modulo di contatto non valido trovato."
msgstr[1] "Validazione della configurazione completata. %s moduli di contatto non validi trovati."

#: admin/includes/config-validator.php:127
msgid "Validate Configuration"
msgstr "Valida la configurazione"

#. translators: %s: number of contact forms
#: admin/includes/config-validator.php:116
msgid "Validate %s contact form now"
msgid_plural "Validate %s contact forms now"
msgstr[0] "Valida ora %s modulo di contatto"
msgstr[1] "Valida ora %s moduli di contatto"

#: admin/includes/config-validator.php:73
msgid "You are not allowed to validate configuration."
msgstr "Non sei abilitato a validare la configurazione."

#: admin/includes/welcome-panel.php:64 admin/includes/welcome-panel.php:159
#: modules/recaptcha/recaptcha.php:264 modules/recaptcha/service.php:286
msgid "https://contactform7.com/recaptcha/"
msgstr "https://contactform7.com/recaptcha/"

#: modules/recaptcha/service.php:337 modules/stripe/service.php:231
msgid "Secret Key"
msgstr "Chiave segreta"

#: modules/recaptcha/service.php:320
msgid "Site Key"
msgstr "Chiave del sito"

#: modules/recaptcha/service.php:270 modules/sendinblue/service.php:144
#: modules/stripe/service.php:151
msgid "Settings saved."
msgstr "Impostazioni salvate."

#: modules/recaptcha/service.php:262 modules/sendinblue/service.php:136
#: modules/stripe/service.php:143
msgid "Invalid key values."
msgstr "Valori chiave non validi."

#: admin/includes/welcome-panel.php:65 admin/includes/welcome-panel.php:160
#: modules/recaptcha/service.php:29
msgid "reCAPTCHA"
msgstr "reCAPTCHA"

#. Author URI of the plugin
#: wp-contact-form-7.php
msgid "https://ideasilo.wordpress.com/"
msgstr "https://ideasilo.wordpress.com/"

#. Author of the plugin
#: wp-contact-form-7.php
msgid "Takayuki Miyoshi"
msgstr "Takayuki Miyoshi"

#. Description of the plugin
#: wp-contact-form-7.php
msgid "Just another contact form plugin. Simple but flexible."
msgstr "Solo un altro plugin per moduli di contatto. Semplice ma flessibile."

#. Plugin URI of the plugin
#: wp-contact-form-7.php
msgid "https://contactform7.com/"
msgstr "https://contactform7.com/"

#. translators: title of your first contact form. %d: number fixed to '1'
#: load.php:193
msgid "Contact form %d"
msgstr "Modulo di contatto %d"

#: modules/textarea.php:143
msgid "text area"
msgstr "area di testo"

#: admin/includes/welcome-panel.php:61 modules/akismet/service.php:22
msgid "Akismet"
msgstr "Akismet"

#: modules/text.php:230
msgid "Text field"
msgstr "Campo di testo"

#: modules/text.php:216
msgid "tel"
msgstr "tel"

#: modules/text.php:215
msgid "URL"
msgstr "URL"

#: modules/text.php:214
msgid "email"
msgstr "email"

#: modules/text.php:213
msgid "text"
msgstr "testo"

#: modules/text.php:195
msgid "Telephone number that the sender entered is invalid"
msgstr "Il numero di telefono che il mittente ha inserito non è valido"

#: modules/text.php:188
msgid "URL that the sender entered is invalid"
msgstr "L'URL che il mittente ha inserito non è valido"

#: modules/text.php:181
msgid "Email address that the sender entered is invalid"
msgstr "L'indirizzo email inserito dal mittente non è valido"

#: modules/submit.php:95
msgid "Label"
msgstr "Etichetta"

#: modules/submit.php:56
msgid "Submit button"
msgstr "Pulsante di invio"

#: modules/submit.php:47
msgid "submit"
msgstr "invia"

#: modules/select.php:232
msgid "drop-down menu"
msgstr "menu a discesa"

#: modules/quiz.php:248
msgid "Questions and answers"
msgstr "Domande e risposte"

#: modules/quiz.php:205
msgid "Quiz"
msgstr "Quesito"

#: modules/quiz.php:196
msgid "quiz"
msgstr "quiz"

#: modules/quiz.php:179
msgid "Sender does not enter the correct answer to the quiz"
msgstr "Il mittente non ha inserito la risposta corretta al quiz"

#: modules/number.php:247
msgid "Slider"
msgstr "Slider"

#: modules/number.php:246
msgid "Spinbox"
msgstr "Casella numerica"

#: modules/number.php:204
msgid "number"
msgstr "numero"

#: modules/number.php:190
msgid "Number is larger than maximum limit"
msgstr "Il numero è superiore al limite massimo"

#: modules/number.php:185
msgid "Number is smaller than minimum limit"
msgstr "Il numero è inferiore al limite minimo"

#: modules/number.php:180
msgid "Number format that the sender entered is invalid"
msgstr "Il formato numerico che il mittente inserito non è valido"

#. translators: %s: the path of the temporary folder
#: includes/file.php:415
msgid "This contact form has file uploading fields, but the temporary folder for the files (%s) does not exist or is not writable. You can create the folder or change its permission manually."
msgstr "Questo modulo di contatto contiene dei campi per il caricamento di file, ma la cartella temporanea per i file (%s) non esiste oppure non è scrivibile. Puoi creare la cartella oppure modificare manualmente i suoi permessi."

#: modules/file.php:195
msgid "Acceptable file types"
msgstr "Tipologia di file consentiti"

#: modules/file.php:142
msgid "file"
msgstr "file"

#: includes/file.php:117
msgid "Uploading a file fails for PHP error"
msgstr "Non è stato possibile caricare il file (errore PHP)"

#: includes/file.php:112
msgid "Uploaded file is too large"
msgstr "Non è stato possibile caricare il file (dimensione eccessiva)"

#: includes/file.php:107
msgid "Uploaded file is not allowed for file type"
msgstr "Il file caricato ha un'estensione non consentita"

#: includes/file.php:102
msgid "Uploading a file fails for any reason"
msgstr "Non è stato possibile caricare il file (ragione generica)"

#: admin/includes/tag-generator.php:349
msgid "Max"
msgstr "Max"

#: admin/includes/tag-generator.php:335
msgid "Min"
msgstr "Min"

#: modules/date.php:237 modules/number.php:256
msgid "Range"
msgstr "Intervallo"

#: admin/includes/tag-generator.php:372
msgid "Default value"
msgstr "Valore predefinito"

#: modules/date.php:194
msgid "Date field"
msgstr "Campo data"

#: modules/date.php:185
msgid "date"
msgstr "data"

#: modules/date.php:171
msgid "Date is later than maximum limit"
msgstr "La data è successiva al limite massimo"

#: modules/date.php:166
msgid "Date is earlier than minimum limit"
msgstr "La data è precedente al limite minimo"

#: modules/date.php:161
msgid "Date format that the sender entered is invalid"
msgstr "Il formato della data che il mittente ha inserito non è valido"

#: admin/includes/tag-generator.php:469
msgid "Wrap each item with a label element."
msgstr "Accorpa ogni elemento con l'etichetta."

#: admin/includes/tag-generator.php:198 modules/acceptance.php:286
msgid "Field type"
msgstr "Tipo di campo"

#: modules/checkbox.php:351
msgid "radio buttons"
msgstr "pulsanti di opzione"

#: modules/checkbox.php:350
msgid "checkboxes"
msgstr "caselle di controllo"

#: modules/really-simple-captcha.php:287
msgid "This contact form contains CAPTCHA fields, but the necessary libraries (GD and FreeType) are not available on your server."
msgstr "Questo modulo di contatto contiene dei campi CAPTCHA, ma le librerie necessarie (GD e FreeType) non sono disponibili nel tuo server."

#. translators: %s: Path to the temporary folder
#: modules/really-simple-captcha.php:276
msgid "This contact form contains CAPTCHA fields, but the temporary folder for the files (%s) does not exist or is not writable. You can create the folder or change its permission manually."
msgstr "Questo modulo di contatto contiene dei campi CAPTCHA, ma la cartella temporanea per i file (%s) non esiste oppure non è scrivibile. Puoi creare la cartella o modificare oppure modificare manualmente i suoi permessi."

#: modules/really-simple-captcha.php:239
msgid "Your entered code is incorrect."
msgstr "Il codice che hai inserito non è valido."

#: modules/really-simple-captcha.php:237
msgid "The code that sender entered does not match the CAPTCHA"
msgstr "Il codice inserito dal mittente non corrisponde con il CAPTCHA"

#: admin/includes/tag-generator.php:498
msgid "Insert Tag"
msgstr "Inserisci tag"

#: admin/includes/tag-generator.php:309
msgid "Class attribute"
msgstr "Attributo class"

#: admin/includes/tag-generator.php:294
msgid "ID attribute"
msgstr "Attributo ID"

#: modules/acceptance.php:255
msgid "Acceptance checkbox"
msgstr "Casella di accettazione"

#: modules/acceptance.php:246
msgid "acceptance"
msgstr "accettazione"

#. translators: 1: property name, 2: method name
#: includes/contact-form.php:234
msgid "<code>%1$s</code> property of a <code>WPCF7_ContactForm</code> object is <strong>no longer accessible</strong>. Use <code>%2$s</code> method instead."
msgstr "La propirietà <code>%1$s</code> di un oggetto <code>WPCF7_ContactForm</code> <strong>non è più accessibile</strong>. Usa in alternativa il metodo <code>%2$s</code>."

#: includes/contact-form.php:114 includes/contact-form.php:438
msgid "Untitled"
msgstr "Senza titolo"

#: includes/contact-form.php:52
msgid "Contact Form"
msgstr "Modulo di contatto"

#: includes/contact-form-template.php:213
msgid "There is a field with input that is shorter than the minimum allowed length"
msgstr "C'è un campo con un contenuto più corto della lunghezza minima consentita"

#: includes/contact-form-template.php:206
msgid "There is a field with input that is longer than the maximum allowed length"
msgstr "C'è un campo con un contenuto più lungo della lunghezza massima consentita"

#: includes/contact-form-template.php:199
msgid "There is a field that the sender must fill in"
msgstr "È presente un campo dati che deve essere compilato dal mittente"

#: includes/contact-form-template.php:192
msgid "There are terms that the sender must accept"
msgstr "Sono presenti dei termini che devono essere accettati dal mittente"

#: includes/contact-form-template.php:185
msgid "Submission was referred to as spam"
msgstr "L'invio è stato classificato come spam"

#: includes/contact-form-template.php:178
msgid "Validation errors occurred"
msgstr "Si è verificato un errore di convalida"

#: includes/contact-form-template.php:171
msgid "Sender's message failed to send"
msgstr "Il messaggio del mittente non è stato inviato"

#: includes/contact-form-template.php:164
msgid "Sender's message was sent successfully"
msgstr "Il messaggio del mittente è stato inviato con successo"

#: includes/contact-form-template.php:72 includes/contact-form-template.php:106
msgid "Message Body:"
msgstr "Corpo del messaggio:"

#. translators: %s: [your-subject]
#: includes/contact-form-template.php:69
msgid "Subject: %s"
msgstr "Oggetto: %s"

#. translators: %s: [your-name] [your-email]
#: includes/contact-form-template.php:64
msgid "From: %s"
msgstr "Da: %s"

#: modules/submit.php:26
msgid "Send"
msgstr "Invia"

#: includes/contact-form-template.php:41
msgid "Your message"
msgstr "Il tuo messaggio"

#: includes/contact-form-template.php:39
msgid "Your email"
msgstr "La tua email"

#: includes/contact-form-template.php:38
msgid "Your name"
msgstr "Il tuo nome"

#. translators: %s: title of form-tag
#: admin/includes/tag-generator.php:82
msgid "Form-tag Generator: %s"
msgstr "Generatore di form-tag: %s"

#: admin/includes/help-tabs.php:97
msgid "For more information:"
msgstr "Per maggiori informazioni:"

#: admin/includes/help-tabs.php:89
msgid "Any information you provide will not be shared with service providers without your authorization."
msgstr "Tutte le informazioni fornite non saranno condivise con i fornitori dei servizi senza la tua autorizzazione."

#: admin/includes/help-tabs.php:88
msgid "You may need to first sign up for an account with the service that you plan to use. When you do so, you would need to authorize Contact Form 7 to access the service with your account."
msgstr "Potrebbe essere necessario registrarsi per un account con il servizio che si intende utilizzare. Quando si fa, si potrebbe aver bisogno di autorizzare Contact Form 7 per accedere al servizio con il proprio account."

#: admin/includes/help-tabs.php:87
msgid "On this screen, you can manage services that are available through Contact Form 7. Using API will allow you to collaborate with any services that are available."
msgstr "In questa schermata, è possibile gestire i servizi che sono disponibili attraverso Contact Form 7. L'utilizzo dell'API ti permetterà di collaborare con tutti i servizi che sono disponibili."

#: admin/includes/help-tabs.php:85
msgid "There are also special mail-tags that have specific names, but do not have corresponding form-tags. They are used to represent meta information of form submissions like the submitter&#8217;s IP address or the URL of the page."
msgstr "Ci sono anche speciali mail-tag che hanno nomi specifici, ma non hanno dei corrispondenti form-tag. Sono utilizzati per rappresentare meta informazioni dell'invio del modulo, come l'indirizzo IP del mittente o l'URL della pagina."

#: admin/includes/help-tabs.php:84
msgid "A mail-tag is also a short code enclosed in square brackets that you can use in every Mail and Mail (2) field. A mail-tag represents a user input value through an input field of a corresponding form-tag."
msgstr "Un mail-tag è uno shortcode racchiuso tra parentesi quadre che può essere utilizzato in ogni campo dei modelli Mail e Mail (2). Un mail-tag rappresenta un valore inserito dall'utente in un campo di input di un corrispondente form-tag."

#: admin/includes/help-tabs.php:82
msgid "While form-tags have a comparatively complex syntax, you do not need to know the syntax to add form-tags because you can use the straightforward tag generator (<strong>Generate Tag</strong> button on this screen)."
msgstr "Anche se i form-tag hanno una sintassi relativamente complessa, non devi conoscerla per poterli aggiungere, in quanto è possibile utilizzare il generatore di tag (pulsante <strong>Genera tag</strong> in questa schermata)."

#: admin/includes/help-tabs.php:81
msgid "A form-tag is a short code enclosed in square brackets used in a form content. A form-tag generally represents an input field, and its components can be separated into four parts: type, name, options, and values. Contact Form 7 supports several types of form-tags including text fields, number fields, date fields, checkboxes, radio buttons, menus, file-uploading fields, CAPTCHAs, and quiz fields."
msgstr "Un form-tag è un codice racchiuso tra parentesi quadre usato in un contenuto del modulo. Un form-tag rappresenta generalmente un campo di input, ed i suoi componenti possono essere suddivisi in quattro parti: tipo, nome, opzioni e valori. Contact Form 7 supporta diversi tipi di form-tag, tra cui campi di testo, campi numerici, campi data, caselle di controllo, pulsanti di opzione, menu, campi di caricamento file, CAPTCHA, e campi quiz."

#: admin/includes/help-tabs.php:79
msgid "<strong>Additional Settings</strong> provides a place where you can customize the behavior of this contact form by adding code snippets."
msgstr "<strong>Impostazioni aggiuntive</strong> fornisce la possibilità di personalizzare il comportamento di questo modulo di contatto con l'aggiunta di frammenti di codice."

#: admin/includes/help-tabs.php:78
msgid "In <strong>Messages</strong>, you can edit various types of messages used for this contact form. These messages are relatively short messages, like a validation error message you see when you leave a required field blank."
msgstr "In <strong>Messaggi</strong>, puoi modificare i vari tipi di messaggi utilizzati per questo modulo di contatto. Questi messaggi sono relativamente brevi, come un messaggio di errore di validazione visibile quando si lascia vuoto un campo obbligatorio."

#: admin/includes/help-tabs.php:77
msgid "<strong>Mail (2)</strong> is an additional mail template that works similar to Mail. Mail (2) is different in that it is sent only when Mail has been sent successfully."
msgstr "<strong>Mail (2)</strong> è un modello di mail aggiuntivo che funziona in modo analogo a Mail. Mail (2) è differente in quanto viene inviato solo quando Mail è stato inviato con successo."

#: admin/includes/help-tabs.php:76
msgid "<strong>Mail</strong> manages a mail template (headers and message body) that this contact form will send when users submit it. You can use Contact Form 7&#8217;s mail-tags here."
msgstr "<strong>Mail</strong> gestisce un modello di mail (intestazioni e corpo del messaggio) che questo modulo di contatto invierà quando gli utenti lo compilano. È possibile utilizzare i mail-tag di Contact Form 7."

#: admin/includes/help-tabs.php:75
msgid "<strong>Form</strong> is a content of HTML form. You can use arbitrary HTML, which is allowed inside a form element. You can also use Contact Form 7&#8217;s form-tags here."
msgstr "<strong>Modulo</strong> è un contenuto di form HTML. Puoi utilizzare codice HTML arbitrario, limitatamente a ciò che è consentito all'interno di un elemento form. Puoi inoltre utilizzare i form-tag di Contact Form 7."

#: admin/includes/help-tabs.php:74
msgid "<strong>Title</strong> is the title of a contact form. This title is only used for labeling a contact form, and can be edited."
msgstr "<strong>Titolo</strong> è il titolo di un modulo di contatto. Questo titolo viene utilizzato solo per l'etichetta di un modulo di contatto, e può essere modificato."

#: admin/includes/help-tabs.php:73
msgid "On this screen, you can edit a contact form. A contact form is comprised of the following components:"
msgstr "In questa schermata, è possibile modificare un modulo di contatto. Un modulo di contatto è costituito dai seguenti componenti:"

#: admin/includes/help-tabs.php:71
msgid "<strong>Duplicate</strong> - Clones that contact form. A cloned contact form inherits all content from the original, but has a different ID."
msgstr "<strong>Duplica</strong> - Clona un modulo di contatto. Un modulo di contatto clonato eredita tutti i contenuti da quello originale, ma ha un ID diverso."

#: admin/includes/help-tabs.php:70
msgid "<strong>Edit</strong> - Navigates to the editing screen for that contact form. You can also reach that screen by clicking on the contact form title."
msgstr "<strong>Modifica</strong> - Passa alla schermata di modifica per quel modulo di contatto. È anche possibile raggiungere quello schermo cliccando sul titolo del modulo di contatto."

#: admin/includes/help-tabs.php:69
msgid "Hovering over a row in the contact forms list will display action links that allow you to manage your contact form. You can perform the following actions:"
msgstr "Passando con il mouse sopra una riga dell'elenco dei moduli di contatto verranno visualizzati i link di azione che ti consentono di gestire il tuo modulo di contatto. Puoi eseguire le seguenti azioni:"

#: admin/includes/help-tabs.php:67
msgid "On this screen, you can manage contact forms provided by Contact Form 7. You can manage an unlimited number of contact forms. Each contact form has a unique ID and Contact Form 7 shortcode ([contact-form-7 ...]). To insert a contact form into a post or a text widget, insert the shortcode into the target."
msgstr "In questa schermata, è possibile gestire moduli di contatto forniti da Contact Form 7. È possibile gestire un numero illimitato di moduli di contatto. Ogni modulo di contatto ha un ID e uno shortcode Contact Form 7 ([contact-form-7 ...]) univoci. Per inserire un modulo di contatto in un post o in un widget di testo, inserire lo shortcode nella destinazione."

#: admin/includes/help-tabs.php:44
msgid "Mail-tags"
msgstr "Mail-tags"

#: admin/includes/help-tabs.php:38
msgid "Form-tags"
msgstr "Form-tags"

#: admin/includes/help-tabs.php:22
msgid "Available Actions"
msgstr "Azioni Disponibili"

#: admin/includes/help-tabs.php:16 admin/includes/help-tabs.php:32
#: admin/includes/help-tabs.php:54
msgid "Overview"
msgstr "Panoramica"

#. translators: %s: link labeled 'Additional settings'
#: admin/includes/editor.php:292
msgid "You can add customization code snippets here. For details, see %s."
msgstr "È possibile aggiungere gli snippet di codice personalizzato qui. Per ulteriori informazioni, vedere %s."

#: admin/includes/editor.php:289
msgid "https://contactform7.com/additional-settings/"
msgstr "https://contactform7.com/additional-settings/"

#: admin/includes/editor.php:236
msgid "File attachments"
msgstr "File allegati"

#: admin/includes/editor.php:230
msgid "Use HTML content type"
msgstr "Utilizza contenuti in HTML"

#: admin/includes/editor.php:228
msgid "Exclude lines with blank mail-tags from output"
msgstr "Escludi dall'ouput le linee con mail-tag vuoti"

#: admin/includes/editor.php:223
msgid "Message body"
msgstr "Corpo del messaggio"

#: admin/includes/editor.php:214
msgid "Additional headers"
msgstr "Intestazioni aggiuntive"

#: admin/includes/editor.php:205 includes/contact-form-template.php:40
msgid "Subject"
msgstr "Oggetto"

#: admin/includes/editor.php:196
msgid "From"
msgstr "Da"

#: admin/includes/editor.php:187
msgid "To"
msgstr "A"

#: admin/includes/editor.php:177
msgid "In the following fields, you can use these mail-tags:"
msgstr "Nei campi seguenti, puoi utilizzare questi mail-tag:"

#: admin/includes/editor.php:160
msgid "Mail (2) is an additional mail template often used as an autoresponder."
msgstr "Mail (2) è un modello di mail aggiuntivo spesso utilizzato come una risposta automatica."

#: admin/includes/editor.php:109
msgid "Use Mail (2)"
msgstr "Usa Mail (2)"

#: admin/includes/editor.php:108
msgid "Mail (2)"
msgstr "Mail (2)"

#. translators: date format, see https://www.php.net/date
#: admin/includes/class-contact-forms-list-table.php:236
msgid "Y/m/d"
msgstr "d/m/Y"

#. translators: %s: title of contact form
#: admin/includes/class-contact-forms-list-table.php:122
msgid "Edit &#8220;%s&#8221;"
msgstr "Modifica &#8220;%s&#8221;"

#: admin/includes/class-contact-forms-list-table.php:169
msgid "Edit"
msgstr "Modifica"

#: admin/includes/class-contact-forms-list-table.php:15
msgid "Date"
msgstr "Data"

#: admin/includes/class-contact-forms-list-table.php:14
msgid "Author"
msgstr "Autore"

#: admin/includes/class-contact-forms-list-table.php:13
msgid "Shortcode"
msgstr "Shortcode"

#: admin/includes/class-contact-forms-list-table.php:12
#: includes/block-editor/index.js:1
msgid "Title"
msgstr "Titolo"

#: admin/edit-contact-form.php:303 admin/includes/editor.php:296
msgid "Additional Settings"
msgstr "Impostazioni aggiuntive"

#. translators: %d: number of additional settings
#: admin/edit-contact-form.php:301
msgid "Additional Settings (%d)"
msgstr "Impostazioni aggiuntive (%d)"

#: admin/edit-contact-form.php:281 admin/includes/editor.php:265
msgid "Messages"
msgstr "Messaggi"

#: admin/edit-contact-form.php:277 admin/includes/editor.php:117
msgid "Mail"
msgstr "Mail"

#: admin/edit-contact-form.php:273 admin/includes/editor.php:85
msgid "Form"
msgstr "Modulo"

#: admin/includes/help-tabs.php:100
msgid "Support"
msgstr "Supporto"

#: admin/includes/help-tabs.php:100
msgid "https://contactform7.com/support/"
msgstr "https://contactform7.com/support/"

#: admin/edit-contact-form.php:241 admin/includes/help-tabs.php:99
msgid "FAQ"
msgstr "FAQ"

#: admin/edit-contact-form.php:240 admin/includes/help-tabs.php:99
msgid "https://contactform7.com/faq/"
msgstr "https://contactform7.com/faq/"

#: admin/includes/help-tabs.php:98
msgid "Docs"
msgstr "Documentazione"

#: admin/edit-contact-form.php:244 admin/includes/help-tabs.php:98
msgid "https://contactform7.com/docs/"
msgstr "https://contactform7.com/docs/"

#: admin/edit-contact-form.php:208
#: admin/includes/class-contact-forms-list-table.php:90
msgid "Delete"
msgstr "Elimina"

#: admin/edit-contact-form.php:179
#: admin/includes/class-contact-forms-list-table.php:187
msgid "Duplicate"
msgstr "Duplica"

#: admin/edit-contact-form.php:160
msgid "Status"
msgstr "Stato"

#: admin/edit-contact-form.php:131
msgid "You can also use this old-style shortcode:"
msgstr "È inoltre possibile utilizzare questo shortcode vecchio stile:"

#: admin/edit-contact-form.php:116
msgid "Copy this shortcode and paste it into your post, page, or text widget content:"
msgstr "Copia questo shortcode ed incollalo nel tuo articolo, pagina o contenuto di un widget di testo:"

#: admin/edit-contact-form.php:100 admin/edit-contact-form.php:101
msgid "Enter title here"
msgstr "Inserisci qui il titolo"

#: admin/edit-contact-form.php:14 admin/edit-contact-form.php:167
msgid "Save"
msgstr "Salva"

#: admin/admin.php:656
msgid "You are not allowed to edit this contact form."
msgstr "Non sei autorizzato a modificare questo modulo di contatto."

#: admin/includes/welcome-panel.php:124
msgid "https://contactform7.com/save-submitted-messages-with-flamingo/"
msgstr "https://contactform7.com/save-submitted-messages-with-flamingo/"

#: modules/akismet/service.php:58
msgid "Spam filtering with Akismet"
msgstr "Filtraggio dello spam con Akismet"

#: admin/includes/welcome-panel.php:60 modules/akismet/service.php:57
msgid "https://contactform7.com/spam-filtering-with-akismet/"
msgstr "https://contactform7.com/spam-filtering-with-akismet/"

#: admin/includes/editor.php:170
msgid "Setting up mail"
msgstr "Impostazioni dell’email"

#: admin/includes/editor.php:169
msgid "https://contactform7.com/setting-up-mail/"
msgstr "https://contactform7.com/setting-up-mail/"

#: admin/includes/welcome-panel.php:85
msgid "Contact Form 7 needs your support."
msgstr "Contact Form 7 ha bisogno del tuo supporto."

#: admin/includes/welcome-panel.php:211
msgid "Dismiss"
msgstr "Ignora"

#. translators: 1: version of Contact Form 7, 2: version of WordPress, 3: URL
#: admin/admin.php:635
msgid "<strong>Contact Form 7 %1$s requires WordPress %2$s or higher.</strong> Please <a href=\"%3$s\">update WordPress</a> first."
msgstr "<strong>Contact Form 7 %1$s richiede WordPress %2$s o superiore.</strong> Aggiorna <a href=\"%3$s\">WordPress</a> prima di procedere."

#: admin/admin.php:618
msgid "Settings"
msgstr "Impostazioni"

#: admin/admin.php:566
msgid "Contact form deleted."
msgstr "Il modulo di contatto è stato eliminato."

#: admin/admin.php:564
msgid "Contact form saved."
msgstr "Il modulo di contatto è stato salvato. "

#: admin/admin.php:562
msgid "Contact form created."
msgstr "Il modulo di contatto è stato creato."

#: admin/admin.php:459
msgid "Search Contact Forms"
msgstr "Ricerca moduli di contatto"

#. translators: %s: search keywords
#: admin/admin.php:436
msgid "Search results for &#8220;%s&#8221;"
msgstr "Risultati della ricerca per &#8220;%s&#8221;"

#: admin/admin.php:341
msgid "Error in deleting."
msgstr "Errore durante l'eliminazione."

#: admin/admin.php:336
msgid "You are not allowed to delete this item."
msgstr "Non puoi eliminare questo elemento."

#: admin/admin.php:243 admin/admin.php:294
msgid "You are not allowed to edit this item."
msgstr "Non puoi modificare questo elemento."

#: admin/admin.php:66
msgid "Integration"
msgstr "Integrazione"

#: admin/admin.php:52 admin/admin.php:427 admin/edit-contact-form.php:35
msgid "Add New"
msgstr "Aggiungi nuovo"

#: admin/admin.php:51 admin/edit-contact-form.php:28
msgid "Add New Contact Form"
msgstr "Aggiungi nuovo modulo di contatto"

#: admin/admin.php:41 admin/admin.php:420 includes/contact-form.php:51
msgid "Contact Forms"
msgstr "Moduli di contatto"

#: admin/admin.php:40 admin/edit-contact-form.php:29
msgid "Edit Contact Form"
msgstr "Modifica modulo di contatto"

#: admin/admin.php:30
msgid "Contact"
msgstr "Contatto"

#. Plugin Name of the plugin
#: wp-contact-form-7.php admin/admin.php:29 modules/flamingo.php:215
msgid "Contact Form 7"
msgstr "Contact Form 7"