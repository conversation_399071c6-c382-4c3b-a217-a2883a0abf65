{"name": "wordpress-custom-template", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"develop": "gulp default", "production": "gulp deploy"}, "author": "", "license": "ISC", "devDependencies": {"babel-core": "^6.7.4", "babel-preset-es2015": "^6.9.0", "babel-preset-react": "^6.5.0", "babel-register": "^6.9.0", "babelify": "^8.0.0", "bootstrap": "^4.3.1", "browser-sync": "^2.12.10", "browserify": "^16.5.1", "critical": "^0.7.2", "dotenv": "^8.2.0", "eslint": "^0.18.0", "eslint-loader": "^0.11.2", "eslint-plugin-react": "^2.0.2", "gulp": "^4.0.2", "gulp-babel": "^6.1.2", "gulp-env": "^0.4.0", "gulp-load-plugins": "^1.2.4", "gulp-notify": "^3.2.0", "gulp-sass": "^4.1.0", "gulp-sourcemaps": "^2.6.5", "gulp-uglify": "^1.5.3", "gulp-util": "^3.0.8", "jquery-mobile": "^1.5.0-alpha.1", "node-sass": "^4.13.0", "node-sass-package-importer": "^5.3.2", "swiper": "^6.5.1", "vinyl-buffer": "^1.0.0", "vinyl-source-stream": "^2.0.0", "waypoints": "^4.0.1"}, "dependencies": {"aos": "^2.3.4", "arrive": "^2.4.1", "bulma": "^0.9.0", "gulp-inject-string": "^1.1.1", "jquery": "^3.4.1", "masonry-layout": "^4.2.2", "popper.js": "^1.14.4", "proxy-middleware": "^0.15.0", "slick-carousel": "^1.8.1", "url": "^0.11.0", "vanilla-lazyload": "^19.1.3"}}